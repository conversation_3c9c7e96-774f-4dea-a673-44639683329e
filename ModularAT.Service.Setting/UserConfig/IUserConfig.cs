using System.Collections.ObjectModel;

namespace ModularAT.Service.Setting;

public interface IUserConfig
{
    ObservableCollection<LoginInfo> LoginInfo { get; set; }
    void Initialize();
    void AddLoginInfo(LoginInfo info);

    void WriteConfig(string file, object info);
    void WriteConfig(object obj, object info, string identifier = null);
    T ReadConfig<T>(string file) where T : new();
    T ReadConfig<T>(object obj, string identifier = null) where T : new();
}