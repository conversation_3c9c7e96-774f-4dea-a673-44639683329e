using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Xml;
using System.Xml.Linq;
using ModularAT.Common.Helper;

namespace ModularAT.Service.Setting;

public class UserConfig : IUserConfig
{
    private readonly string _dir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
        "modular_settings\\LoginInfo.xml");

    private readonly string _path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
        "modular_settings");


    public UserConfig()
    {
        LoginInfo = new ObservableCollection<LoginInfo>();
        Initialize();
    }

    public ObservableCollection<LoginInfo> LoginInfo { get; set; }

    public void Initialize()
    {
        try
        {
            if (!Directory.Exists(_path)) Directory.CreateDirectory(_path);
            if (!File.Exists(_dir)) File.Create(_dir);

            var list = new List<LoginInfo>();
            try
            {
                var xmlDoc = new XmlDocument();
                xmlDoc.Load(_dir);
                var xmlNodeList = xmlDoc.SelectSingleNode("Root").ChildNodes;
                foreach (XmlNode node in xmlNodeList)
                {
                    var element = (XmlElement)node;
                    var infoItem = new LoginInfo();
                    foreach (XmlNode xmlNode in element.ChildNodes)
                    {
                        var xmlEle = (XmlElement)xmlNode;
                        if (xmlEle.Name == "UserName") infoItem.UserName = xmlEle.InnerText;
                        if (xmlEle.Name == "Password") infoItem.Password = xmlEle.InnerText;
                        if (xmlEle.Name == "Culture") infoItem.Culture = xmlEle.InnerText;
                    }

                    list.Add(infoItem);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            LoginInfo = new ObservableCollection<LoginInfo>(list);
        }
        catch
        {
        }
    }

    public void AddLoginInfo(LoginInfo info)
    {
        // 查找是否存在相同 UserName 的记录
        var existingInfo = LoginInfo.FirstOrDefault(i => i.UserName == info.UserName);
        if (existingInfo != null)
        {
            // 若存在，更新记录
            existingInfo.Password = info.Password;
            existingInfo.Culture = info.Culture;
        }
        else
        {
            // 若不存在，插入新记录
            LoginInfo.Insert(0, info);
        }

        // 去除重复的 UserName
        var list = LoginInfo.Distinct(EqualityHelper<LoginInfo>.CreateComparer(p => p.UserName)).ToArray();

        try
        {
            // 构建 XML 元素
            var arrayDataAsXElements = from c in list
                select
                    new XElement
                    ("LoginInfo",
                        new XElement("UserName", c.UserName),
                        new XElement("Password", c.Password),
                        new XElement("Culture", c.Culture)
                    );
            var peopleDoc = new XElement("Root", arrayDataAsXElements);
            var doc = new XDocument(peopleDoc);
            // 保存 XML 文档
            doc.Save(_dir);
        }
        catch (Exception ex)
        {
            // 重新抛出异常
            throw;
        }
    }


    public void WriteConfig(string file, object info)
    {
        if (!Path.IsPathRooted(file)) //如果为相对路径，存在默认用户路径下
            file = _path + "\\" + file;

        File.WriteAllText(file, JsonSerializer.Serialize(info));
    }

    public void WriteConfig(object obj, object info, string identifier)
    {
        var file = obj.GetType().Name;
        if (!string.IsNullOrEmpty(identifier)) file += $"_{identifier}";
        WriteConfig(file, info);
    }


    public T ReadConfig<T>(string file) where T : new()
    {
        if (!Path.IsPathRooted(file)) //如果为相对路径，存在默认用户路径下
            file = _path + "\\" + file;

        if (!File.Exists(file)) return new T();

        var obj = JsonSerializer.Deserialize<T>(File.ReadAllText(file));
        if (obj == null) return new T();

        return obj;
    }

    public T ReadConfig<T>(object obj, string identifier) where T : new()
    {
        var file = obj.GetType().Name;
        if (!string.IsNullOrEmpty(identifier)) file += $"_{identifier}";
        return ReadConfig<T>(file);
    }
}