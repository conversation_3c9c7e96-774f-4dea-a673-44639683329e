using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using MiniExcelLibs;
using ModularAT.Entity;
using ModularAT.Entity.Enum;

namespace ModularAT.Service.Setting;

public class ServoSettingService
{
    private readonly IServiceProvider _provider;
    private readonly string PathToDrive = Environment.CurrentDirectory + "\\settings\\DriveParamer.xlsx";

    public ServoSettingService(IServiceProvider provider)
    {
        _provider = provider;
    }

    public void LoadParameterFromFile(BindingList<ParameterModel> Parameters)
    {
        // 获取Excel文件中的所有工作表名称
        var sheetNames = MiniExcel.GetSheetNames(PathToDrive);
        sheetNames.ForEach(sheet =>
        {
            var parameterList = MiniExcel.Query<ParameterModel>(PathToDrive, sheet);
            foreach (var item in parameterList)
            {
                item.Group = sheet;
                Parameters.Add(item);
            }
        });
# if DEBUG
        // ExportSomething(Parameters.ToList());
#endif
    }

    private DataType GetDataTypeFromString(string dataTypeString)
    {
        // 实现将字符串转换为DataType的功能
        // 例如：
        switch (dataTypeString)
        {
            case "UINT16":
                return DataType.UINT16;
            case "INT16":
                return DataType.INT16;
            case "UINT32":
                return DataType.UINT32;
            case "INT32":
                return DataType.INT32;
            default:
                throw new ArgumentException($"Unsupported data type: {dataTypeString}");
        }
    }

    private void ExportSomething(List<ParameterModel> parameters)
    {
        var existingRows = new List<dynamic>();
        // 判断文件是否存在
        if (System.IO.File.Exists("Db_Lang.csv"))
        {
            // 读取现有的 CSV 文件
            existingRows = MiniExcel.Query<dynamic>("Db_Lang.csv").ToList();
        }

        // 合并数据，如果 Name 相同则更新，否则追加
        foreach (var newParam in parameters)
        {
            var existingRow = existingRows.FirstOrDefault(r => r.Name == newParam.Name);
            if (existingRow != null)
            {
                // 更新现有行
                existingRow.Path = newParam.Group;
                existingRow.Comment = newParam.Description;
            }
            else
            {
                // 追加新行
                existingRows.Add(new { Path = newParam.Group, Comment = newParam.Description, Name = newParam.Name });
            }
        }

        // 保存更新后的数据
        MiniExcel.SaveAs("Db_Lang.csv", existingRows, overwriteFile: true);
    }
}