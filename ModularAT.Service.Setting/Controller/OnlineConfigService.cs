using ModularAT.Localization.Resources;
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Xml.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using ModularAT.Common.Extensions;
using ModularAT.Common.Helper;
using ModularAT.Common.Log;
using ModularAT.Entity.Enum;
using ModularAT.Entity.OnlineConfig;

namespace ModularAT.Service.Setting;

public static class OnlineConfigService
{
    // private static readonly Lazy<OnlineConfigService> lazy = new(() => new OnlineConfigService());
    //
    // public static OnlineConfigService Instance => lazy.Value;
    

    /// <summary>
    /// 加载配置到指定类型对象
    /// </summary>
    public static T LoadConfig<T>(LineConfigEnum configEnum) where T : new()
    {
        var config = new T();
        var isList = typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>);
        
        if (isList)
        {
            var elementType = typeof(T).GetGenericArguments()[0];
            var list = (System.Collections.IList)config;
            var xmlDatas = LoadXmlDatas(configEnum).GroupBy(x => x.Title);
            
            foreach (var group in xmlDatas)
            {
                // 尝试获取带参数的构造函数
                var constructors = elementType.GetConstructors();
                object item;
                
                if (constructors.Length > 0 && constructors[0].GetParameters().Length > 0)
                {
                    // 如果有带参数的构造函数，尝试传递默认值
                    var parameters = constructors[0].GetParameters()
                        .Select(p => p.ParameterType.IsValueType ? Activator.CreateInstance(p.ParameterType) : null)
                        .ToArray();
                    item = Activator.CreateInstance(elementType, parameters);
                }
                else
                {
                    // 否则尝试无参构造函数
                    item = Activator.CreateInstance(elementType);
                }
                
                SetProperties(item, group);
                list.Add(item);
            }
        }
        else
        {
            SetProperties(config, LoadXmlDatas(configEnum));
        }
        
        return config;
    }

    private static void SetProperties(object target, IEnumerable<OnlineConfigModel> models)
    {
        var properties = target.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
        
        foreach (var model in models)
        {
            var prop = properties.FirstOrDefault(p => 
                p.Name.Equals(model.EnglishName, StringComparison.OrdinalIgnoreCase));
            
            if (prop == null || !prop.CanWrite) continue;
            
            try
            {
                var converter = TypeDescriptor.GetConverter(prop.PropertyType);
                if (converter.IsValid(model.Value))
                {
                    prop.SetValue(target, converter.ConvertFromString(model.Value));
                }
            }
            catch (Exception ex)
            {
                LogHelper.GetInstance().Error($"属性 {prop?.Name} 赋值失败: {ex.Message}");
            }
        }
    }


    /// <summary>
    ///     获取配置列表
    /// </summary>
    /// <param name="configEnum"></param>
    /// <returns></returns>
    public static IEnumerable<OnlineConfigModel> LoadXmlDatas(LineConfigEnum configEnum)
    {
        var parameters = new List<OnlineConfigModel>(); // 初始化out参数
        try
        {
            var filePath = Path.Combine(Environment.CurrentDirectory,
                "OnlineConfigsPara/" + configEnum.GetConfigFileName());
            using FileStream fileStream = new(filePath, FileMode.Open);
            var doc = XDocument.Load(fileStream);
            var models = (
                from root in doc.Root?.Elements().Take(1) // 获取第一个子元素作为一级元素
                from secondLevel in root.Elements() // 遍历第二级元素
                let groupName = secondLevel.Name.LocalName ?? "None"
                let groupTitle = secondLevel.Attribute("Title")?.Value ?? Lang.OnlineConfigService_Unknown
                from thirdLevel in secondLevel.Elements() // 遍历第三级元素
                select new OnlineConfigModel
                {
                    GroupName = groupName,
                    Title = groupTitle,
                    EnglishName = thirdLevel.Name.LocalName,
                    Describe = thirdLevel.Element("Describe")?.Value ?? Lang.OnlineConfigService_No_description,
                    DataType = thirdLevel.Element("DataType")?.Value ?? Lang.OnlineConfigService_Unknown,
                    Value = thirdLevel.Element("Value")?.Value ?? Lang.OnlineConfigService_No_value,
                    IsExpand = false // 默认设置为false，根据需求调整
                }).ToList();

            // 将结果添加到OnlineParameters集合中
            foreach (var model in models) parameters.Add(model);
        }
        catch (Exception ex)
        {
            LogHelper.GetInstance().Error($"An error occurred while reading the XML file: {ex.Message}");
        }

        return parameters;
    }

    /// <summary>
    ///     保存修改的配置
    /// </summary>
    /// <param name="configEnum"></param>
    /// <param name="parameters"></param>
    public static void SaveXmlDatas(LineConfigEnum configEnum, IEnumerable<OnlineConfigModel> parameters)
    {
        try
        {
            var filePath = Path.Combine(Environment.CurrentDirectory,
                "OnlineConfigsPara/" + configEnum.GetConfigFileName());
            var doc = new XDocument(
                new XElement(configEnum.GetRootName(),
                    new XElement(configEnum.GetConfigFileName().Replace(".xml", string.Empty),
                        new XAttribute("Title", configEnum.GetConfigTitle()),
                        from model in parameters
                        group model by new { model.GroupName, model.Title }
                        into g
                        select new XElement(g.Key.GroupName,
                            new XAttribute("Title", g.Key.Title),
                            from model in g
                            select new XElement(model.EnglishName,
                                new XElement("Describe", model.Describe),
                                new XElement("DataType", model.DataType),
                                new XElement("Value", model.Value)
                            )
                        )
                    )
                )
            );
            doc.Save(filePath);
        }
        catch (Exception ex)
        {
            LogHelper.GetInstance().Error($"An error occurred while writing to the XML file: {ex.Message}");
        }
    }

    /// <summary>
    ///     写入多组配置，会覆盖之前的配置
    /// </summary>
    /// <param name="configEnum"></param>
    /// <param name="configs"></param>
    public static void WriteXmlData<T>(LineConfigEnum configEnum, IEnumerable<T> configs)
    {
        var filePath = Path.Combine(Environment.CurrentDirectory,
            "OnlineConfigsPara/" + configEnum.GetConfigFileName());
        var configFileNameWithoutExtension = configEnum.GetConfigFileName().Replace(".xml", string.Empty);
        var rootName = configEnum.GetRootName();
        var configTitle = configEnum.GetConfigTitle(true);

        try
        {
            FileHelper.FolderCreate(filePath);

            // Create XML document
            XDocument doc = new(
                new XElement(rootName,
                    new XElement(configFileNameWithoutExtension, new XAttribute("Title", configTitle))
                )
            );

            var index = 0;
            foreach (var item in configs)
            {
                var groupName = configEnum.ObjToString().Replace("CfgPara", string.Empty);
                var properties = item.GetType().GetProperties();

                // Add each object's XML element
                doc.Root?.Element(configFileNameWithoutExtension)?.Add(
                    new XElement(groupName,
                        new XAttribute("Title", $"{configEnum.GetConfigTitle()}-{index}"),
                        from property in properties
                        select new XElement(property.Name,
                            new XElement("Describe",
                                property.GetCustomAttribute<DescriptionAttribute>()?.Description ?? ""),
                            new XElement("DataType", property.PropertyType.Name),
                            new XElement("Value", property.GetValue(item)?.ToString() ?? "")
                        )
                    )
                );
                index++;
            }

            doc.Save(filePath);
        }
        catch (Exception ex)
        {
            LogHelper.GetInstance()
                .Error($"An error occurred while writing to the XML file at path '{filePath}': {ex.Message}", ex);
        }
    }
}