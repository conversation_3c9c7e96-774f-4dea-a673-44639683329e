using System;
using System.Collections.Generic;
using System.Linq;
using MiniExcelLibs;
using ModularAT.Common.Helper;
using ModularAT.Entity.Config;

namespace ModularAT.Service.Setting;

public class ControllerSettingService
{
    private static readonly Dictionary<uint, string> errorCodes = new();

    private static readonly Lazy<ControllerSettingService> lazy = new(() => new ControllerSettingService());
    private readonly string errorcodePath = Environment.CurrentDirectory + "\\settings\\ControlFailures-Chinese.xlsx";

    private ControllerSettingService()
    {
        ErrorCodesDic = GetErrorCodes();
    }

    public static ControllerSettingService Instance => lazy.Value;

    public Dictionary<uint, string> ErrorCodesDic { get; } = [];

    public Dictionary<uint, string> GetErrorCodes()
    {
        if (errorCodes.Count > 0) return errorCodes;
        // 获取Excel文件中的所有工作表名称
        var sheetNames = MiniExcel.GetSheetNames(errorcodePath);
        var rows = MiniExcel.Query(errorcodePath, true, sheetNames[0]).ToList();
        //跳过第二行
        for (var rowIndex = 1; rowIndex <= rows.Count() - 1; rowIndex++)
        {
            var row = rows.ElementAt(rowIndex);
            uint code = Convert.ToUInt32(row.编码, 16);
            var message = $"{row.编码} : {row.内容}";
            errorCodes.Add(code, message);
        }

        return errorCodes;
    }

    public static string GetErrorCodeDescription(uint code)
    {
        var codeDic = ControllerSettingService.Instance.ErrorCodesDic;
        if (codeDic.TryGetValue(code, out var description)) return description;
        return code.ToString();
    }

    public static List<ViewLineConfig> GetViewLineConfig()
    {
        var path = "settings/viewLineConfig.json";
        return JsonHelper.LoadFromJson<List<ViewLineConfig>>(path);
    }


}