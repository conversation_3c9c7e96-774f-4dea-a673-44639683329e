<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <OutputType>Library</OutputType>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>
    <ItemGroup>
        <None Remove="app.config"/>
        <None Remove="LoginInfo.xml"/>
    </ItemGroup>
    <ItemGroup>
        <Content Include="LoginInfo.xml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <Reference Include="System.Configuration"/>
        <Reference Include="System.IO.Compression"/>
        <Reference Include="System.Security"/>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ModularAT.Entity\ModularAT.Entity.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.0"/>
        <PackageReference Include="Microsoft.CSharp" Version="4.7.0"/>
        <PackageReference Include="MiniExcel" Version="1.41.3" />
        <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0"/>
        <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0"/>
        <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.0"/>
    </ItemGroup>
</Project>