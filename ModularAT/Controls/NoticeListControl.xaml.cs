using System.Windows;
using System.Windows.Controls;
using ModularAT.ViewModels;

namespace ModularAT.Controls;

public partial class NoticeListControl : UserControl
{
    public NoticeListControl()
    {
        InitializeComponent();
    }

    private void ClearAllButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is NoticeListViewModel viewModel)
        {
            viewModel.ClearAllNotices();
        }
    }
}