<UserControl x:Class="ModularAT.Controls.NoticeListControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
             xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
             xmlns:converter="clr-namespace:ModularAT.ValueConverter"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <!-- 值转换器 -->
            <converter:LevelToStyleConverter x:Key="LevelToStyleConverter" />

            <!-- 通知项样式 -->
            <Style x:Key="BaseNoticeStyle" TargetType="Border">
                <Setter Property="Margin" Value="0,2,0,2" />
                <Setter Property="Padding" Value="10" />
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="BorderThickness" Value="1" />
            </Style>

        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Background="#F5F5F5"
                BorderBrush="#E0E0E0"
                BorderThickness="0,0,0,1"
                Padding="10,5"
                MaxWidth="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}">
            <Grid>
                <TextBlock
                    Text="{Binding LanResources.NoticeListControl_Feedback_information, Source={x:Static localization:LocalizationService.Current}}"
                    FontWeight="Bold"
                    VerticalAlignment="Center" />
                <Button
                    HorizontalAlignment="Right"
                    Click="ClearAllButton_Click"
                    ToolTip="{Binding LanResources.NoticeListControl_Clear_all_notifications, Source={x:Static localization:LocalizationService.Current}}"
                    Width="24"
                    Height="24">
                    <Button.Style>
                        <Style TargetType="Button" BasedOn="{StaticResource CleanButton}">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#DDDDDD" />
                                    <Setter Property="Cursor" Value="Hand" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <iconPacks:PackIconMaterial Kind="NotificationClearAll" Width="16" Height="16" />
                </Button>
            </Grid>
        </Border>

        <!-- 通知列表 -->
        <ListView Grid.Row="1"
                  ItemsSource="{Binding Notices}"
                  BorderThickness="1"
                  BorderBrush="#CCCCCC"
                  ScrollViewer.CanContentScroll="True"
                  ScrollViewer.HorizontalScrollBarVisibility="Auto"
                  ScrollViewer.VerticalScrollBarVisibility="Auto"
                  ClipToBounds="True">
            <ListView.View>
                <GridView>
                    <GridView.ColumnHeaderContainerStyle>
                        <Style TargetType="GridViewColumnHeader">
                            <Setter Property="Background" Value="#F0F0F0" />
                            <Setter Property="Padding" Value="5" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>

                    <!-- 类型列 -->
                    <GridViewColumn Header="{Binding LanResources.NoticeListControl_Type, Source={x:Static localization:LocalizationService.Current}}" Width="40">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <iconPacks:PackIconMaterial

                                    VerticalAlignment="Center"
                                    Width="24"
                                    Height="24">
                                    <iconPacks:PackIconMaterial.Style>
                                        <Style TargetType="iconPacks:PackIconMaterial">
                                            <Style.Triggers>
                                                <!-- 原始大写格式 -->
                                                <DataTrigger Binding="{Binding Level.Name}" Value="ERROR">
                                                    <Setter Property="Kind" Value="CloseCircle" />
                                                    <Setter Property="Foreground" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Level.Name}" Value="WARN">
                                                    <Setter Property="Kind" Value="AlertCircle" />
                                                    <Setter Property="Foreground" Value="#FF9800" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Level.Name}" Value="INFO">
                                                    <Setter Property="Kind" Value="Information" />
                                                    <Setter Property="Foreground" Value="#2196F3" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Level.Name}" Value="NOTICE">
                                                    <Setter Property="Kind" Value="CheckCircle" />
                                                    <Setter Property="Foreground" Value="ForestGreen" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </iconPacks:PackIconMaterial.Style>
                                </iconPacks:PackIconMaterial>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- 来源列 -->
                    <GridViewColumn Header="{Binding LanResources.NoticeListControl_Source, Source={x:Static localization:LocalizationService.Current}}" Width="60">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding From}" VerticalAlignment="Center"
                                           Margin="0,0,10,0" MinWidth="80" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- ID列 -->
                    <GridViewColumn Header="ID" Width="40">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Id}" VerticalAlignment="Center" Margin="0,0,10,0"
                                           MinWidth="40" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- 消息内容列 -->
                    <GridViewColumn Header="{Binding LanResources.NoticeListControl_Message_content, Source={x:Static localization:LocalizationService.Current}}" Width="300">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Message}" TextWrapping="NoWrap"
                                           VerticalAlignment="Center" TextTrimming="CharacterEllipsis" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Padding" Value="0" />
                    <Setter Property="Background"
                            Value="{Binding Level, Converter={StaticResource LevelToStyleConverter}, ConverterParameter=Background}" />
                    <Setter Property="BorderBrush"
                            Value="{Binding Level, Converter={StaticResource LevelToStyleConverter}, ConverterParameter=BorderBrush}" />
                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                    <Setter Property="Margin" Value="0,2,0,2" />
                    <Setter Property="HorizontalAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>
        </ListView>
    </Grid>
</UserControl>