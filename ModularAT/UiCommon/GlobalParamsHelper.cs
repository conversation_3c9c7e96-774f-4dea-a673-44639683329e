using ModularAT.Driver.Controller;
using ModularAT.Entity.Enum;
using ModularAT.Service.Setting;

namespace ModularAT.UiCommon;

public class GlobalParamsHelper
{
    public static IEnumerable<string> GetMovers(bool hasNone = false)
    {
        var datas = OnlineConfigService.LoadXmlDatas(LineConfigEnum.AxisCfgPara)
            .Select(item => item.Title)
            .Distinct();
        return hasNone ? datas.Prepend("None") : datas;
    }
    public static IEnumerable<string> GetLines(bool hasNone = false)
    {
        var datas = OnlineConfigService.LoadXmlDatas(LineConfigEnum.LineBodyCfgPara)
            .Select(item => item.Title)
            .Distinct();
        return hasNone ? datas.Prepend("None") : datas;
    }

    public static IEnumerable<string> GetStations(bool hasNone = false)
    {
        var datas = OnlineConfigService.LoadXmlDatas(LineConfigEnum.StationCfgPara)
            .Select(item => item.Title)
            .Distinct();
        return hasNone ? datas.Prepend("None") : datas;
    }
    
    public static IEnumerable<string> GetSlaveNodes(bool hasNone = false)
    {
        var datas = OnlineConfigService.LoadXmlDatas(LineConfigEnum.SlaveNodeCfgPara)
            .Select(item => item.Title)
            .Distinct();
        return hasNone ? datas.Prepend("All") : datas;
    }
}