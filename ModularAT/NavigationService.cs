using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.ViewModels;

namespace ModularAT;

public class NavigationService
{
    private ModularATViewModelBase? currentViewModel;

    public ModularATViewModelBase? CurrentViewModel
    {
        get => currentViewModel;
        set
        {
            currentViewModel = value;
            // notify property changed
            CurrentViewModelChanged?.Invoke();
        }
    }

    public event Action? CurrentViewModelChanged;

    public void NavigateTo<T>() where T : ModularATViewModelBase
    {
        CurrentViewModel = App.Current.Services.GetService<T>();
    }

    public UserControl ResolveViewFor<T>() where T : class
    {
        var viewType = typeof(T).FullName.Replace("ViewModel", "View");
        // 尝试获取视图类型
        var viewTypeObj = Type.GetType(viewType);
        if (viewTypeObj != null)
        {
            // 从IOC容器中获取视图实例
            var view = App.Current.Services.GetService(viewTypeObj) as UserControl;
            return view;
        }

        return null;
    }
}