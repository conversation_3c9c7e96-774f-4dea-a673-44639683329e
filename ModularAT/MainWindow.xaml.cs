using System.Windows.Input;
using log4net.Core;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common.Log;
using ModularAT.ViewModels;

namespace ModularAT;

/// <summary>
///     MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : MetroWindow
{
    public MainWindow()
    {
        InitializeComponent();
        var mySettings = new MetroDialogSettings()
        {
            AffirmativeButtonText = "OK",
            NegativeButtonText = "Cancel",
            AnimateShow = true
        };
        MsgToUiHelper.MsgReceived += async (msg, level) =>
        {
            if (level == Level.Notice)
            {
                await Dispatcher.InvokeAsync(async () =>
                {
                    MessageDialogResult result = await DialogManager.ShowMessageAsync(this, "Notice", msg,
                        MessageDialogStyle.Affirmative, mySettings);
                });
            }
        };
    }

    public MainViewModel ViewModel => App.Current.Services.GetRequiredService<MainViewModel>();

    private void MetroWindow_MouseUp(object sender, MouseButtonEventArgs e)
    {
        ViewModel.MsgReceived = string.Empty;
    }
}