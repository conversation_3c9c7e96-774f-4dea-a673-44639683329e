using ModularAT.Localization.Resources;
using System.ComponentModel;
using System.Windows;

namespace ModularAT.ControlHelpers;

public class DesignerHelper
{
    private static bool? _isInDesignMode;

    public static bool IsInDesignMode
    {
        get
        {
            if (!_isInDesignMode.HasValue)
                _isInDesignMode = (bool)DependencyPropertyDescriptor
                    .FromProperty(DesignerProperties.IsInDesignModeProperty, typeof(FrameworkElement)).Metadata
                    .DefaultValue;

            return _isInDesignMode.Value;
        }
    }

    #region IsInMainThread

    /// <summary>
    ///     是否是在主线程中处理
    /// </summary>
    public static bool IsInMainThread
    {
        get
        {
            if (Thread.CurrentThread.IsBackground || Thread.CurrentThread.IsThreadPoolThread) return false;

            if (Thread.CurrentThread.Name == Lang.DesignerHelper_Main_thread) return true;

            if (System.Windows.Application.Current == null)
                return true;

            return Thread.CurrentThread == System.Windows.Application.Current.Dispatcher.Thread;
        }
    }

    #endregion
}