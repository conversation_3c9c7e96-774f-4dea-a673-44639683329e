using System.Windows;
using System.Windows.Media;
using ModularAT.ViewModels;

namespace ModularAT.ControlHelpers;

public static class XamlHelper
{
    public static bool IsChecked(int pid)
    {
        var permAssign = App.GetService<BasePermAssignViewModel>();
        if (permAssign is null || permAssign.Current is null) return false;
        return permAssign.Current.pids.Contains(pid);
    }

    // 辅助方法：在给定的依赖对象中查找指定类型的子控件
    public static T FindChild<T>(DependencyObject parent) where T : DependencyObject
    {
        if (parent == null) return null;

        for (var i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            if (child is T t) return t;

            var childOfChild = FindChild<T>(child);
            if (childOfChild != null) return childOfChild;
        }

        return null;
    }
}