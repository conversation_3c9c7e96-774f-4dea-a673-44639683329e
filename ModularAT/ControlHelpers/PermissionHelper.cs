using ModularAT.Localization.Resources;
﻿using System.Windows;
using System.Windows.Controls;
using ModularAT.Entity.Policy;
using ModularAT.Entitys;
using TriggerAction = Microsoft.Xaml.Behaviors.TriggerAction;

namespace ModularAT.ControlHelpers;

public class PermissionHelper
{
    public static readonly DependencyProperty HasPermProperty = DependencyProperty.RegisterAttached(
        "HasPerm", typeof(string), typeof(PermissionHelper),
        new PropertyMetadata(default(string), OnHasPermPropertyChangedCallback));

    public static readonly DependencyProperty IsCreatorProperty = DependencyProperty.RegisterAttached(
        "IsCreator", typeof(string), typeof(PermissionHelper),
        new PropertyMetadata(default(string), OnIsCreatorChangedCallback));

    public static void SetHasPerm(DependencyObject element, string value)
    {
        element.SetValue(HasPermProperty, value);
    }

    public static string GetHasPerm(DependencyObject element)
    {
        return (string)element.GetValue(HasPermProperty);
    }


    private static void OnHasPermPropertyChangedCallback(DependencyObject dependencyObject,
        DependencyPropertyChangedEventArgs dependencyPropertyChangedEventArgs)
    {
        if (DesignerHelper.IsInDesignMode) return;
        var str = dependencyPropertyChangedEventArgs.NewValue as string;
        if (dependencyObject is FrameworkElement element)
        {
            var _operator = App.GetService<IOperator>();
            if (_operator.UserName is null || _operator.Roles.FirstOrDefault().OrderSort == 0) return;
            var permissionRequirement = App.GetService<PermissionRequirement>();
            var permission = permissionRequirement?.Permissions.FirstOrDefault(p => p.Code == str);
            if (permission != null)
            {
                element.Visibility = permission.IsHide ? Visibility.Collapsed : Visibility.Visible;
            }
            else
            {
                if (element is Button btn)
                {
                    btn.IsEnabled = false;
                    if (btn.Parent is Panel parentContainer) ToolTipService.SetToolTip(parentContainer, Lang.PermissionHelper_No_permission_operation);
                }
                else
                {
                    element.Visibility = Visibility.Collapsed;
                }
            }
        }
        else if (dependencyObject is TriggerAction trigger)
        {
            var permissions = App.GetService<PermissionRequirement>()?.Permissions;
            trigger.IsEnabled = permissions.Any(p => p.Code == str);
        }
    }

    public static void SetIsCreator(DependencyObject element, string value)
    {
        element.SetValue(HasPermProperty, value);
    }

    public static string GetIsCreator(DependencyObject element)
    {
        return (string)element.GetValue(HasPermProperty);
    }

    private static void OnIsCreatorChangedCallback(DependencyObject dependencyObject,
        DependencyPropertyChangedEventArgs dependencyPropertyChangedEventArgs)
    {
        if (DesignerHelper.IsInDesignMode) return;
        var element = dependencyObject as FrameworkElement;
        var str = dependencyPropertyChangedEventArgs.NewValue as string;
        if (element != null)
        {
            var _operator = App.GetService<IOperator>();
            //if (_operator == null || _operator.Property == null || string.IsNullOrEmpty(str) || !str.Contains(_operator.Property.uID))
            //{
            //    element.Visibility = Visibility.Collapsed;
            //}
        }
    }
}