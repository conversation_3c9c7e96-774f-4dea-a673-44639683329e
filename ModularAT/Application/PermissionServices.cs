using ModularAT.Application.Interface;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;
using SqlSugar;

namespace ModularAT.Application;

/// <summary>
///     PermissionServices
/// </summary>
public class PermissionServices : BaseServices<Permission>, IPermissionServices
{
    private IBaseRepository<Permission> _dal;

    public PermissionServices(IBaseRepository<Permission> dal)
    {
        _dal = dal;
        BaseDal = dal;
    }

    public async Task<List<Permission>> QueryLowLevelPermissions(int level)
    {
        return await QuerySql("SELECT p.* FROM Permission p " +
                              "JOIN RoleModulePermission rp ON p.Id = rp.PermissionId " +
                              "JOIN Role r ON rp.RoleId = r.Id " +
                              "WHERE p.IsDeleted==false& r.OrderSort >= @level  " +
                              "GROUP BY p.Id;", [new SugarParameter("level", level)]);
    }
}