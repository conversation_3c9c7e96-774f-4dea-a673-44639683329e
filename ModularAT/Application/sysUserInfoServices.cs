using ModularAT.Application.Interface;
using ModularAT.Common.Extensions;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;
using SqlSugar;

namespace ModularAT.Application;

/// <summary>
///     sysUserInfoServices
/// </summary>
public class SysUserInfoServices : BaseServices<sysUserInfo>, ISysUserInfoServices
{
    private readonly IBaseRepository<sysUserInfo> _dal;
    private readonly IBaseRepository<Role> _roleRepository;
    private readonly IBaseRepository<UserRole> _userRoleRepository;

    public SysUserInfoServices(IBaseRepository<sysUserInfo> dal, IBaseRepository<UserRole> userRoleRepository,
        IBaseRepository<Role> roleRepository)
    {
        _dal = dal;
        _userRoleRepository = userRoleRepository;
        _roleRepository = roleRepository;
        BaseDal = dal;
    }

    public async Task<sysUserInfo> SaveUserInfo(string loginName, string loginPwd)
    {
        var sysUserInfo = new sysUserInfo(loginName, loginPwd);
        var model = new sysUserInfo();
        var userList = await Query(a => a.uLoginName == sysUserInfo.uLoginName && a.uLoginPWD == sysUserInfo.uLoginPWD);
        if (userList.Count > 0)
        {
            model = userList.FirstOrDefault();
        }
        else
        {
            var id = await Add(sysUserInfo);
            model = await QueryById(id);
        }

        return model;
    }


    public async Task<string> GetUserRoleNameStr(string loginName, string loginPwd)
    {
        var roleName = "";
        var user = (await Query(a => a.uLoginName == loginName && a.uLoginPWD == loginPwd)).FirstOrDefault();
        var roleList = await _roleRepository.Query(a => a.IsDeleted == false);
        if (user != null)
        {
            var userRoles = await _userRoleRepository.Query(ur => ur.UserId == user.uID);
            if (userRoles.Count > 0)
            {
                var arr = userRoles.Select(ur => ur.RoleId.ObjToString()).ToList();
                var roles = roleList.Where(d => arr.Contains(d.Id.ObjToString()));

                roleName = string.Join(",", roles.Select(r => r.Name).ToArray());
            }
        }

        return roleName;
    }

    public async Task<List<sysUserInfo>> GetLowLevelUsers(int level)
    {
        return await QuerySql(
            "SELECT si.* FROM sysUserInfo si JOIN UserRole ur ON si.uID = ur.UserId JOIN Role r ON ur.RoleId = r.Id WHERE r.OrderSort >= @level  GROUP BY si.uID;",
            [new SugarParameter("level", level)]);
    }
}