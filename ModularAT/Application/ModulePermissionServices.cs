using ModularAT.Application.Interface;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;

namespace ModularAT.Application;

/// <summary>
///     ModulePermissionServices
/// </summary>
public class ModulePermissionServices : BaseServices<ModulePermission>, IModulePermissionServices
{
    private IBaseRepository<ModulePermission> _dal;

    public ModulePermissionServices(IBaseRepository<ModulePermission> dal)
    {
        _dal = dal;
        BaseDal = dal;
    }
}