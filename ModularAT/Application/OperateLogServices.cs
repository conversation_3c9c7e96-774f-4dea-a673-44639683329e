using ModularAT.Application.Interface;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;

namespace ModularAT.Application;

public class OperateLogServices : BaseServices<OperateLog>, IOperateLogServices
{
    private readonly IBaseRepository<OperateLog> _dal;

    public OperateLogServices(IBaseRepository<OperateLog> dal)
    {
        _dal = dal;
        BaseDal = dal;
    }

    public async Task<OperateLog> GetLimit(bool min)
    {
        return (await _dal.Query("", min ? "LogTime asc" : "LogTime desc")).FirstOrDefault();
    }
}