using ModularAT.Application.Interface;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;

namespace ModularAT.Application;

/// <summary>
///     ModuleServices
/// </summary>
public class ModuleServices : BaseServices<Modules>, IModuleServices
{
    private IBaseRepository<Modules> _dal;

    public ModuleServices(IBaseRepository<Modules> dal)
    {
        _dal = dal;
        BaseDal = dal;
    }
}