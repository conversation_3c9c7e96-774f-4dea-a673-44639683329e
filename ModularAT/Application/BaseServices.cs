using System.Data;
using System.Linq.Expressions;
using ModularAT.Application.Interface;
using ModularAT.Entity;
using ModularAT.Repository.Interface;
using SqlSugar;

namespace ModularAT.Application;

public class BaseServices<TEntity> : IBaseServices<TEntity> where TEntity : class, new()
{
    //public IBaseRepository<TEntity> baseDal = new BaseRepository<TEntity>();
    public IBaseRepository<TEntity> BaseDal; //通过在子类的构造函数中注入，这里是基类，不用构造函数

    public async Task<TEntity> QueryById(object objId)
    {
        return await BaseDal.QueryById(objId);
    }

    /// <summary>
    ///     功能描述:根据ID查询一条数据
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="objId">id（必须指定主键特性 [SugarColumn(IsPrimaryKey=true)]），如果是联合主键，请使用Where条件</param>
    /// <param name="blnUseCache">是否使用缓存</param>
    /// <returns>数据实体</returns>
    public async Task<TEntity> QueryById(object objId, bool blnUseCache = false)
    {
        return await BaseDal.QueryById(objId, blnUseCache);
    }

    /// <summary>
    ///     功能描述:根据ID查询数据
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="lstIds">id列表（必须指定主键特性 [SugarColumn(IsPrimaryKey=true)]），如果是联合主键，请使用Where条件</param>
    /// <returns>数据实体列表</returns>
    public async Task<List<TEntity>> QueryByIDs(object[] lstIds)
    {
        return await BaseDal.QueryByIDs(lstIds);
    }

    /// <summary>
    ///     写入实体数据
    /// </summary>
    /// <param name="entity">博文实体类</param>
    /// <returns></returns>
    public async Task<int> Add(TEntity entity)
    {
        return await BaseDal.Add(entity);
    }

    /// <summary>
    ///     批量插入实体(速度快)
    /// </summary>
    /// <param name="listEntity">实体集合</param>
    /// <returns>影响行数</returns>
    public async Task<int> Add(List<TEntity> listEntity)
    {
        return await BaseDal.Add(listEntity);
    }

    /// <summary>
    ///     更新实体数据
    /// </summary>
    /// <param name="entity">博文实体类</param>
    /// <returns></returns>
    public async Task<bool> Update(TEntity entity)
    {
        return await BaseDal.Update(entity);
    }

    public async Task<bool> Update(TEntity entity, string strWhere)
    {
        return await BaseDal.Update(entity, strWhere);
    }

    public async Task<bool> Update(object operateAnonymousObjects)
    {
        return await BaseDal.Update(operateAnonymousObjects);
    }

    public async Task<bool> Update(
        TEntity entity,
        List<string> lstColumns = null,
        List<string> lstIgnoreColumns = null,
        string strWhere = ""
    )
    {
        return await BaseDal.Update(entity, lstColumns, lstIgnoreColumns, strWhere);
    }


    /// <summary>
    ///     根据实体删除一条数据
    /// </summary>
    /// <param name="entity">博文实体类</param>
    /// <returns></returns>
    public async Task<bool> Delete(TEntity entity)
    {
        return await BaseDal.Delete(entity);
    }

    /// <summary>
    ///     删除指定ID的数据
    /// </summary>
    /// <param name="id">主键ID</param>
    /// <returns></returns>
    public async Task<bool> DeleteById(object id)
    {
        return await BaseDal.DeleteById(id);
    }

    /// <summary>
    ///     删除指定ID集合的数据(批量删除)
    /// </summary>
    /// <param name="ids">主键ID集合</param>
    /// <returns></returns>
    public async Task<bool> DeleteByIds(object[] ids)
    {
        return await BaseDal.DeleteByIds(ids);
    }


    /// <summary>
    ///     功能描述:查询所有数据
    ///     作　　者:ModularAT
    /// </summary>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query()
    {
        return await BaseDal.Query();
    }

    /// <summary>
    ///     功能描述:查询数据列表
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="strWhere">条件</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(string strWhere)
    {
        return await BaseDal.Query(strWhere);
    }

    /// <summary>
    ///     功能描述:查询数据列表
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="whereExpression">whereExpression</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(Expression<Func<TEntity, bool>> whereExpression)
    {
        return await BaseDal.Query(whereExpression);
    }

    /// <summary>
    ///     功能描述:按照特定列查询数据列表
    ///     作　　者:ModularAT
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="expression"></param>
    /// <returns></returns>
    public async Task<List<TResult>> Query<TResult>(Expression<Func<TEntity, TResult>> expression)
    {
        return await BaseDal.Query(expression);
    }

    /// <summary>
    ///     功能描述:按照特定列查询数据列表带条件排序
    ///     作　　者:ModularAT
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <param name="strWhere">过滤条件</param>
    /// <param name="expression">查询实体条件</param>
    /// <param name="strOrderByFileds">排序条件</param>
    /// <returns></returns>
    public async Task<List<TResult>> Query<TResult>(string strWhere, Expression<Func<TEntity, TResult>> expression,
        string strOrderByFileds)
    {
        return await BaseDal.Query(strWhere, expression, strOrderByFileds);
    }

    /// <summary>
    ///     功能描述:查询一个列表
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="whereExpression">条件表达式</param>
    /// <param name="strOrderByFileds">排序字段，如name asc,age desc</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(Expression<Func<TEntity, bool>> whereExpression,
        Expression<Func<TEntity, object>> orderByExpression, bool isAsc = true)
    {
        return await BaseDal.Query(whereExpression, orderByExpression, isAsc);
    }

    public async Task<List<TEntity>> Query(Expression<Func<TEntity, bool>> whereExpression, string strOrderByFileds)
    {
        return await BaseDal.Query(whereExpression, strOrderByFileds);
    }

    /// <summary>
    ///     功能描述:查询一个列表
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="strWhere">条件</param>
    /// <param name="strOrderByFileds">排序字段，如name asc,age desc</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(string strWhere, string strOrderByFileds)
    {
        return await BaseDal.Query(strWhere, strOrderByFileds);
    }

    /// <summary>
    ///     根据sql语句查询
    /// </summary>
    /// <param name="strSql">完整的sql语句</param>
    /// <param name="parameters">参数</param>
    /// <returns>泛型集合</returns>
    public async Task<List<TEntity>> QuerySql(string strSql, SugarParameter[] parameters = null)
    {
        return await BaseDal.QuerySql(strSql, parameters);
    }

    /// <summary>
    ///     根据sql语句查询
    /// </summary>
    /// <param name="strSql">完整的sql语句</param>
    /// <param name="parameters">参数</param>
    /// <returns>DataTable</returns>
    public async Task<DataTable> QueryTable(string strSql, SugarParameter[] parameters = null)
    {
        return await BaseDal.QueryTable(strSql, parameters);
    }

    /// <summary>
    ///     功能描述:查询前N条数据
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="whereExpression">条件表达式</param>
    /// <param name="intTop">前N条</param>
    /// <param name="strOrderByFileds">排序字段，如name asc,age desc</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(Expression<Func<TEntity, bool>> whereExpression, int intTop,
        string strOrderByFileds)
    {
        return await BaseDal.Query(whereExpression, intTop, strOrderByFileds);
    }

    /// <summary>
    ///     功能描述:查询前N条数据
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="strWhere">条件</param>
    /// <param name="intTop">前N条</param>
    /// <param name="strOrderByFileds">排序字段，如name asc,age desc</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(
        string strWhere,
        int intTop,
        string strOrderByFileds)
    {
        return await BaseDal.Query(strWhere, intTop, strOrderByFileds);
    }

    /// <summary>
    ///     功能描述:分页查询
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="whereExpression">条件表达式</param>
    /// <param name="intPageIndex">页码（下标0）</param>
    /// <param name="intPageSize">页大小</param>
    /// <param name="intTotalCount">数据总量</param>
    /// <param name="strOrderByFileds">排序字段，如name asc,age desc</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(
        Expression<Func<TEntity, bool>> whereExpression,
        int intPageIndex,
        int intPageSize,
        string strOrderByFileds)
    {
        return await BaseDal.Query(
            whereExpression,
            intPageIndex,
            intPageSize,
            strOrderByFileds);
    }

    /// <summary>
    ///     功能描述:分页查询
    ///     作　　者:ModularAT
    /// </summary>
    /// <param name="strWhere">条件</param>
    /// <param name="intPageIndex">页码（下标0）</param>
    /// <param name="intPageSize">页大小</param>
    /// <param name="intTotalCount">数据总量</param>
    /// <param name="strOrderByFileds">排序字段，如name asc,age desc</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> Query(
        string strWhere,
        int intPageIndex,
        int intPageSize,
        string strOrderByFileds)
    {
        return await BaseDal.Query(
            strWhere,
            intPageIndex,
            intPageSize,
            strOrderByFileds);
    }

    public async Task<PageModel<TEntity>> QueryPage(Expression<Func<TEntity, bool>> whereExpression,
        int intPageIndex = 1, int intPageSize = 20, string strOrderByFileds = null)
    {
        return await BaseDal.QueryPage(whereExpression,
            intPageIndex, intPageSize, strOrderByFileds);
    }

    public async Task<List<TResult>> QueryMuch<T, T2, T3, TResult>(Expression<Func<T, T2, T3, object[]>> joinExpression,
        Expression<Func<T, T2, T3, TResult>> selectExpression, Expression<Func<T, T2, T3, bool>> whereLambda = null)
        where T : class, new()
    {
        return await BaseDal.QueryMuch(joinExpression, selectExpression, whereLambda);
    }
}