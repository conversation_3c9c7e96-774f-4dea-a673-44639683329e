using ModularAT.Application.Interface;
using ModularAT.Common.Extensions;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;

namespace ModularAT.Application;

/// <summary>
///     UserRoleServices
/// </summary>
public class UserRoleServices : BaseServices<UserRole>, IUserRoleServices
{
    private IBaseRepository<UserRole> _dal;

    public UserRoleServices(IBaseRepository<UserRole> dal)
    {
        _dal = dal;
        BaseDal = dal;
    }

    /// <summary>
    /// </summary>
    /// <param name="uid"></param>
    /// <param name="rid"></param>
    /// <returns></returns>
    public async Task<UserRole> SaveUserRole(int uid, int rid)
    {
        var userRole = new UserRole(uid, rid);

        var model = new UserRole();
        var userList = await Query(a => a.UserId == userRole.UserId && a.RoleId == userRole.RoleId);
        if (userList.Count > 0)
        {
            model = userList.FirstOrDefault();
        }
        else
        {
            var id = await Add(userRole);
            model = await QueryById(id);
        }

        return model;
    }


    public async Task<int> GetRoleIdByUid(int uid)
    {
        return ((await Query(d => d.UserId == uid)).OrderByDescending(d => d.Id).LastOrDefault()?.RoleId).ObjToInt();
    }
}