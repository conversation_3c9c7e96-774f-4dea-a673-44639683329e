using ModularAT.Application.Interface;
using ModularAT.Entity.BaseManage;
using ModularAT.Repository.Interface;

namespace ModularAT.Application;

/// <summary>
///     RoleServices
/// </summary>
public class RoleServices : BaseServices<Role>, IRoleServices
{
    private IBaseRepository<Role> _dal;

    public RoleServices(IBaseRepository<Role> dal)
    {
        _dal = dal;
        BaseDal = dal;
    }

    public async Task<Role> SaveRole(string roleName)
    {
        var role = new Role(roleName);
        var model = new Role();
        var userList = await Query(a => a.Name == role.Name && a.Enabled);
        if (userList.Count > 0)
        {
            model = userList.FirstOrDefault();
        }
        else
        {
            var id = await Add(role);
            model = await QueryById(id);
        }

        return model;
    }


    public async Task<string> GetRoleNameByRid(int rid)
    {
        return (await QueryById(rid))?.Name;
    }
}