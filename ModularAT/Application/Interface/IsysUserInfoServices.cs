using ModularAT.Entity.BaseManage;

namespace ModularAT.Application.Interface;

/// <summary>
///     sysUserInfoServices
/// </summary>
public interface ISysUserInfoServices : IBaseServices<sysUserInfo>
{
    Task<sysUserInfo> SaveUserInfo(string loginName, string loginPwd);
    Task<string> GetUserRoleNameStr(string loginName, string loginPwd);

    Task<List<sysUserInfo>> GetLowLevelUsers(int level);
}