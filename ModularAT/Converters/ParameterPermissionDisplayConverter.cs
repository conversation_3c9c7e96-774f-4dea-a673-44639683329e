using System;
using System.Globalization;
using System.Windows.Data;
using ModularAT.Entity;
using ModularAT.ViewModels;

namespace ModularAT.Converters
{
    /// <summary>
    /// 参数权限显示转换器，用于UI显示样式的权限检查
    /// </summary>
    public class ParameterPermissionDisplayConverter : IMultiValueConverter
    {
        public static readonly ParameterPermissionDisplayConverter Instance = new ParameterPermissionDisplayConverter();

        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 2)
                return false; // 默认有权限显示

            // 第一个值是参数对象
            if (!(values[0] is ParameterModel param))
                return false; // 默认有权限显示

            // 第二个值是ViewModel
            if (!(values[1] is ServoSettingViewModel viewModel))
                return false; // 默认有权限显示

            // 调用ViewModel的权限检查方法，直接返回权限状态
            return viewModel.HasParameterPermission(param);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}