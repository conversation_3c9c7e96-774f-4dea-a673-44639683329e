using System;
using System.Globalization;
using System.Windows.Data;
using ModularAT.Entity;
using ModularAT.ViewModels;

namespace ModularAT.Converters
{
    /// <summary>
    /// 参数权限转换器，用于检查当前用户是否有权限修改指定参数
    /// </summary>
    public class ParameterPermissionConverter : IMultiValueConverter
    {
        public static readonly ParameterPermissionConverter Instance = new ParameterPermissionConverter();

        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values == null || values.Length < 2)
                return true; // 默认只读

            // 第一个值是参数对象
            if (!(values[0] is ParameterModel param))
                return true; // 默认只读

            // 第二个值是ViewModel
            if (!(values[1] is ServoSettingViewModel viewModel))
                return true; // 默认只读

            // 调用ViewModel的权限检查方法，返回相反值用于IsReadOnly
            return !viewModel.HasParameterPermission(param);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}