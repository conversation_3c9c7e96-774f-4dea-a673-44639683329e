{
  "AllowedHosts": "*",
  "EventBus": {
    "Enabled": false,
    "SubscriptionClientName": "ModularAT"
  },
  "AppSettings": {
    "LogAOP": {
      "Enabled": false
    },
    "TranAOP": {
      "Enabled": false
    },
    "SqlAOP": {
      "Enabled": false
    },
    "Date": "2024-08-28",
    "Author": "ModularAT",
    "UseLoadTest": false
  },
  // 请配置MainDB为你想要的主库的ConnId值,并设置对应的Enabled为true；
  // *** 单库操作，把 MutiDBEnabled 设为false ***；
  // *** 多库操作，把 MutiDBEnabled 设为true，其他的从库Enabled也为true **；

  "MainDB": "Modular_SQLITE",
  //当前项目的主库，所对应的连接字符串的Enabled必须为true
  "MutiDBEnabled": false,
  //是否开启多库模式
  "CQRSEnabled": false,
  //是否开启读写分离模式,必须是单库模式，且数据库类型一致，比如都是SqlServer
  "DBS": [
    /*
      对应下边的 DBType
      MySql = 0,
      SqlServer = 1,
      Sqlite = 2,
      Oracle = 3,
      PostgreSQL = 4
    */
    {
      "ConnId": "Modular_SQLITE",
      "DBType": 2,
      "Enabled": true,
      "HitRate": 50,
      // 值越大，优先级越高
      "Connection": "Modular.db"
      //sqlite只写数据库名就行
    },
    {
      "ConnId": "Modular_MSSQL_1",
      "DBType": 1,
      "Enabled": false,
      "HitRate": 40,
      "Connection": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=Modular_MSSQL_1;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
      "ProviderName": "System.Data.SqlClient"
    },
    {
      "ConnId": "Modular_MSSQL_2",
      "DBType": 1,
      "Enabled": false,
      "HitRate": 30,
      "Connection": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=Modular_MSSQL_2;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False",
      "ProviderName": "System.Data.SqlClient"
    },
    {
      "ConnId": "Modular_MYSQL",
      "DBType": 0,
      "Enabled": false,
      "HitRate": 20,
      "Connection": "server=.;Database=ddd;Uid=root;Pwd=******;Port=10060;Allow User Variables=True;"
    },
    {
      "ConnId": "Modular_MYSQL_2",
      "DBType": 0,
      "Enabled": true,
      "HitRate": 20,
      "Connection": "server=.;Database=blogcore001;Uid=root;Pwd=******;Port=3096;Allow User Variables=True;"
    },
    {
      "ConnId": "Modular_ORACLE",
      "DBType": 3,
      "Enabled": false,
      "HitRate": 10,
      "Connection": "Provider=OraOLEDB.Oracle; Data Source=ModularDB; User Id=sss; Password=***;",
      "OracleConnection_other1": "User ID=sss;Password=***;Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521)))(CONNECT_DATA=(SERVICE_NAME = orcl)))"
    }
  ],
  "Startup": {
    "AppConfigAlert": {
      "Enabled": true
    },
    "ApiName": "ModularAT"
  },
  "UnitySetting": {
    "ServiceIP": "127.0.0.1",
    "ServicePort": "12345"
  }
}
