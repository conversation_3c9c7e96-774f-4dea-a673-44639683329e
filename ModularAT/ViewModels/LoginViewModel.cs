using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ModularAT.Application.Interface;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Common.Helper;
using ModularAT.Entity.Policy;
using ModularAT.Entitys;
using ModularAT.Localization;
using ModularAT.Service.Setting;
using ModularAT.Views;

namespace ModularAT.ViewModels;

public partial class LoginViewModel : ObservableRecipient
{
    private readonly IUserConfig _localConfig;
    private readonly IOperator _operator;
    private readonly PermissionRequirement _requirement;
    private readonly IRoleModulePermissionServices _roleModulePermissionServices;
    private readonly IRoleServices _roleServices;
    private readonly ISysUserInfoServices _sysUserInfoServices;
    private readonly IUserRoleServices _userRoleServices;

    [ObservableProperty] private bool isRmembered;

    [ObservableProperty] private string loginError;

    private bool Loginning;

    [ObservableProperty] private string loginStatus = "Input";

    [ObservableProperty] private string password;

    [ObservableProperty] private string userName;

    [ObservableProperty] private string culture;


    [ObservableProperty] private string version = AssemblyHelper.GetAssemblyVersion(typeof(App));

    public LoginViewModel(IUserConfig localConfig,
        IOperator ioperator,
        ISysUserInfoServices sysUserInfoServices,
        IUserRoleServices userRoleServices,
        IRoleServices roleServices,
        PermissionRequirement requirement,
        IRoleModulePermissionServices roleModulePermissionServices)
    {
        _localConfig = localConfig;
        _operator = ioperator;
        _sysUserInfoServices = sysUserInfoServices;
        _userRoleServices = userRoleServices;
        _roleServices = roleServices;
        _requirement = requirement;
        _roleModulePermissionServices = roleModulePermissionServices;

        LoginInfos = _localConfig.LoginInfo;

        var info = LoginInfos.FirstOrDefault();
        if (info != null)
        {
            UserName = info.UserName;
            Password = info.Password;
            MD5Password = info.Password;
            Culture = info.Culture;
        }
    }

    public ObservableCollection<LoginInfo> LoginInfos { get; set; }

    public string MD5Password { get; set; }

    [RelayCommand]
    public void GetPassword(string userName)
    {
        Password = LoginInfos.FirstOrDefault(i => i.UserName == userName)?.Password;
    }

    [Log]
    [RelayCommand]
    private async Task<string> LoginAsync()
    {
        if (Loginning) return string.Empty;

        try
        {
            Loginning = true;

            var success = false;
            MD5Password = Password?.ToMD5String();

            if (UserName == "administrator" && Password == "administrator")
            {
                _operator.UserName = UserName;
                _operator.Roles = await _roleServices.Query(r => r.OrderSort == 0);
                success = true;
            }
            else
            {
                var user = await _sysUserInfoServices.Query(d =>
                    d.uLoginName == userName && d.uLoginPWD == MD5Password && d.tdIsDelete == false);
                if (user.Count > 0)
                {
                    var roleNames = await _sysUserInfoServices.GetUserRoleNameStr(userName, MD5Password);
                    var data = await _roleModulePermissionServices.GetRMPMaps();
                    var list = (from item in data
                        where item.IsDeleted == false && roleNames.Contains(item.Role.Name)
                        orderby item.Id
                        select new PermissionItem
                        {
                            Url = item.Module?.LinkUrl,
                            Code = item.Permission?.Code,
                            IsHide = item.Permission?.IsHide ?? false,
                            Role = item.Role?.Name.ObjToString()
                        }).ToList();
                    _requirement.Permissions = list;
                    _operator.Property = user.FirstOrDefault();
                    _operator.UserName = UserName;
                    _operator.Roles = await _roleServices.Query(r => roleNames.Contains(r.Name));
                    success = true;
                }
            }

            if (success)
            {
                if (IsRmembered) _localConfig.AddLoginInfo(new LoginInfo { UserName = UserName, Password = Password });

                var loginWindow = App.GetService<LoginWindow>();
                loginWindow.DialogResult = success;
                loginWindow.Close();
                Culture = _localConfig.LoginInfo.FirstOrDefault(i => i.UserName == UserName)?.Culture;
                LocalizationManager.GetInstance().Change(Culture);
                return UserName;
            }
        }
        catch (Exception ex)
        {
            LoginError = ex.Message;
        }
        finally
        {
            Loginning = false;
        }

        return string.Empty;
    }

    [RelayCommand]
    private void ResultChanged(object result)
    {
        if ((bool)result) //验证成功
            LoginAsync();
    }

    [RelayCommand]
    public void SaveCulture(string culture)
    {
        Culture = culture;
        _localConfig.AddLoginInfo(new LoginInfo { UserName = UserName, Password = Password, Culture = Culture });
    }
}