using System.Collections.ObjectModel;
using ModularAT.Localization.Resources;
using System.ComponentModel;
using System.Reflection;
using System.Windows.Forms;
using AutoMapper;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using MiniExcelLibs;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Common.Log;
using ModularAT.Driver.Servo;
using ModularAT.Driver.Servo.Packages;
using ModularAT.Entity;
using ModularAT.Entity.Enum;
using ModularAT.Entitys;
using ModularAT.ViewModels;
using ModularAT.Views;
using TouchSocket.Core;

namespace ModularAT.ViewModels;

public partial class ServoSettingViewModel : ModularATViewModelBase
{
    //通过构造注入映射器对象，保留一例。推荐使用扩展方法
    private readonly IMapper _mapper;
    private readonly ServoSerialPortClient servoSerialPort = App.GetService<ServoSerialPortClient>();
    private readonly IOperator _ioperator;

    public ServoSettingViewModel(NavigationService navigationService, IMapper mapper, IOperator ioperator) : base(
        navigationService)
    {
        _mapper = mapper;
        _ioperator = ioperator;

        // 订阅参数变化事件
        //Data.ParamItems.ListChanged += (s, e) => UpdateWriteButtonsState();

        // 订阅每个参数的属性变化事件
        // foreach (var param in Data.ParamItems)
        // {
        //     param.PropertyChanged += OnParameterPropertyChanged;
        // }

        // 初始化按钮状态
        //UpdateWriteButtonsState();

        //自动刷新
        Task.Run(async () =>
        {
            while (true)
            {
                if (servoSerialPort.IsConnected && navigationService.CurrentViewModel is ServoSettingViewModel)
                    servoSerialPort.RequestAuthorization();
                await Task.Delay(1000);
            }
        });
    }

    /// <summary>
    /// 参数属性变化事件处理
    /// </summary>
    private void OnParameterPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(ParameterModel.EditString))
        {
            UpdateWriteButtonsState();
        }
    }

    #region Event & Function

    public bool CheckRolePrimission(int rolepermission)
    {
        try
        {
            return _ioperator.Roles.Any(r => r.OrderSort <= rolepermission);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 检查当前用户是否有权限修改指定参数
    /// </summary>
    /// <param name="parameter">参数模型</param>
    /// <returns>true表示有权限，false表示无权限</returns>
    public bool HasParameterPermission(ParameterModel parameter)
    {
        if (parameter?.RolePermission == null || parameter.RolePermission <= 0)
            return true; // 没有设置权限要求的参数默认允许访问
        
        return CheckRolePrimission(parameter.RolePermission);
    }

    [RelayCommand]
    private async Task ImportAsync()
    {
        var fileDialog = new OpenFileDialog
        {
            Filter = "CSV文件|*.csv",
            Title = "选择导入文件"
        };

        if (fileDialog.ShowDialog() == DialogResult.OK)
        {
            try
            {
                var items = (await MiniExcel.QueryAsync(fileDialog.FileName))
                    .Select(row => new { Name = row.A, EditString = row.B });

                var importErrors = new List<string>();
                var successCount = 0;

                foreach (var item in items)
                {
                    var param = Data.ParamItems.FirstOrDefault(p => p.Name == item.Name);
                    if (param != null)
                    {
                        // 检查权限
                        if (!HasParameterPermission(param))
                        {
                            importErrors.Add($"{item.Name}: 当前用户没有权限修改此参数");
                            continue;
                        }

                        // 临时设置值以进行验证
                        var originalValue = param.EditString;
                        param.EditString = item.EditString;

                        // 验证导入的值
                        var errorMessage = param[nameof(param.EditString)];
                        if (!string.IsNullOrEmpty(errorMessage))
                        {
                            // 如果验证失败，恢复原值并记录错误
                            param.EditString = originalValue;
                            importErrors.Add($"{item.Name}: {errorMessage}");
                        }
                        else
                        {
                            successCount++;
                        }
                    }
                    else
                    {
                        importErrors.Add($"{item.Name}: 参数不存在");
                    }
                }

                // 显示导入结果
                if (importErrors.Any())
                {
                    var errorDetails = string.Join("\n", importErrors.Take(10));
                    if (importErrors.Count > 10)
                    {
                        errorDetails += $"\n...还有{importErrors.Count - 10}个错误";
                    }

                    MsgToUiHelper.SendMsgWarn(
                        $"数据导入完成，成功导入{successCount}个参数，{importErrors.Count}个参数导入失败:\n{errorDetails}");
                }
                else
                {
                    MsgToUiHelper.SendMsgInfo($"数据导入成功，共导入{successCount}个参数");
                }
            }
            catch (Exception ex)
            {
                MsgToUiHelper.SendMsgError($"CSV导入失败：{ex.Message}");
            }
        }
    }

    [RelayCommand]
    private async Task ExportAsync()
    {
        var fileDialog = new SaveFileDialog
        {
            Filter = "CSV文件|*.csv",
            FileName = $"ServoParams_{DateTime.Now:yyyyMMddHHmmss}.csv",
            Title = "选择导出路径"
        };

        if (fileDialog.ShowDialog() == DialogResult.OK)
        {
            var savePath = fileDialog.FileName;
            try
            {
                var exportData = Data.ParamItems
                    .Where(p => HasParameterPermission(p)) // 只导出有权限的参数
                    .Select(p => new { p.Name, p.EditString });

                await MiniExcel.SaveAsAsync(savePath, exportData, overwriteFile: true);
                MsgToUiHelper.SendMsgInfo("驱动器参数导出成功");
            }
            catch (Exception ex)
            {
                MsgToUiHelper.SendMsgError($"驱动器参数导出失败：{ex.Message}");
            }
        }
    }


    #endregion

    #region Property

    public new DataViewModel Data => ViewModelLocator.DataViewModel;

    /// <summary>
    ///     选择设置的参数种类
    /// </summary>
    [ObservableProperty] public SecondDeviceDataSetCode selectFirmwareParaSet = SecondDeviceDataSetCode.Motor;

    [ObservableProperty] private DriverModeSetModel driverMode = new();

    /// <summary>
    /// 是否可以执行部分写入操作（当前分组）
    /// </summary>
    [ObservableProperty] private bool canExecutePartialWrite = true;

    /// <summary>
    /// 是否可以执行全部写入操作
    /// </summary>
    [ObservableProperty] private bool canExecuteAllWrite = true;

    /// <summary>
    /// 第一个有错误的参数项（用于滚动定位）
    /// </summary>
    [ObservableProperty] private ParameterModel? firstErrorParameter;

    /// <summary>
    /// 滚动到第一个错误参数的命令
    /// </summary>
    [RelayCommand]
    private void ScrollToFirstError()
    {
        if (FirstErrorParameter != null)
        {
            var view = App.Current.Services.GetService<ServoSettingView>();
            view?.ScrollToParameter(FirstErrorParameter);
        }
    }

    public Dictionary<string, short> DriverControlBy { get; } = new()
    {
        { "EtherCAT", (short)SecondDriverControlByCode.Ethercat },
        { "Local", (short)SecondDriverControlByCode.Local }
    };

    public Dictionary<string, short> DriverLocalMode { get; } = new()
    {
        { "Nomal", (short)ThirdLocalModeCode.Nomal },
        { "Test", (short)ThirdLocalModeCode.Test }
    };

    public Dictionary<string, short> DriverSubModeNomal { get; } = new()
    {
        { Lang.ServoSettingViewModel_No_control, (short)DriverSubModeNomalCode.None },
        { Lang.ServoSettingViewModel_Dual_axis_position_control, (short)DriverSubModeNomalCode.DoubleAxis },
        { Lang.ServoSettingViewModel_Axis0_electrical_angle, (short)DriverSubModeNomalCode.Axis0 }
    };

    public Dictionary<string, short> DriverSubModeTest { get; } = new()
    {
        { Lang.ServoSettingViewModel_No_control, (short)DriverSubModeTestCode.None },
        { Lang.ServoSettingViewModel_Dc_sampling_test, (short)DriverSubModeTestCode.DC },
        { Lang.ServoSettingViewModel_Ac_sampling_test, (short)DriverSubModeTestCode.AC }
    };

    #endregion

    #region Validation Methods

    /// <summary>
    /// 验证当前选中参数组的有效性
    /// </summary>
    /// <returns>true表示所有参数有效，false表示存在无效参数</returns>
    private bool ValidateParameters()
    {
        var invalidParams = new List<string>();
        var permissionErrors = new List<string>();

        foreach (var param in Data.ParamItems.Where(p => GetParamGroup(p) == SelectFirmwareParaSet))
        {
            // 检查权限
            if (!HasParameterPermission(param))
            {
                permissionErrors.Add($"{param.Name}: 当前用户没有权限修改此参数");
                continue;
            }

            if (!string.IsNullOrEmpty(param.EditString))
            {
                var errorMessage = param[nameof(param.EditString)];
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    invalidParams.Add($"{param.Name}: {errorMessage}");
                }
            }
        }

        // 合并权限错误和验证错误
        var allErrors = permissionErrors.Concat(invalidParams).ToList();
        if (allErrors.Any())
        {
            var errorDetails = string.Join("\n", allErrors.Take(5)); // 只显示前5个错误
            if (allErrors.Count > 5)
            {
                errorDetails += $"\n...还有{allErrors.Count - 5}个参数错误";
            }

            MsgToUiHelper.SendMsgError($"参数验证失败:\n{errorDetails}");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 验证所有参数的有效性
    /// </summary>
    /// <returns>true表示所有参数有效，false表示存在无效参数</returns>
    private bool ValidateAllParameters()
    {
        var invalidParams = new List<string>();
        var permissionErrors = new List<string>();

        foreach (var param in Data.ParamItems)
        {
            // 检查权限
            if (!HasParameterPermission(param))
            {
                permissionErrors.Add($"{param.Name}: 当前用户没有权限修改此参数");
                continue;
            }

            if (!string.IsNullOrEmpty(param.EditString))
            {
                var errorMessage = param[nameof(param.EditString)];
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    invalidParams.Add($"{param.Name}: {errorMessage}");
                }
            }
        }

        // 合并权限错误和验证错误
        var allErrors = permissionErrors.Concat(invalidParams).ToList();
        if (allErrors.Any())
        {
            var errorDetails = string.Join("\n", allErrors.Take(10)); // 只显示前10个错误
            if (allErrors.Count > 10)
            {
                errorDetails += $"\n...还有{allErrors.Count - 10}个参数错误";
            }

            MsgToUiHelper.SendMsgError($"参数验证失败:\n{errorDetails}");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 检查并更新写入按钮的启用状态
    /// </summary>
    private void UpdateWriteButtonsState()
    {
        // 检查当前分组的参数错误
        var hasPartialErrors = false;
        ParameterModel? firstError = null;

        foreach (var param in Data.ParamItems.Where(p => GetParamGroup(p) == SelectFirmwareParaSet))
        {
            if (!string.IsNullOrEmpty(param.ValidateEditString()))
            {
                hasPartialErrors = true;
                if (firstError == null)
                {
                    firstError = param;
                }
            }
        }

        // 检查所有参数的错误
        var hasAllErrors = false;
        foreach (var param in Data.ParamItems)
        {
            if (!string.IsNullOrEmpty(param.ValidateEditString()))
            {
                hasAllErrors = true;
                // 如果当前分组没有错误，但其他分组有错误，则将第一个错误设为全局第一个错误
                if (!hasPartialErrors && firstError == null)
                {
                    firstError = param;
                }
                break;
            }
        }

        var previousCanExecutePartial = CanExecutePartialWrite;
        CanExecutePartialWrite = !hasPartialErrors;
        CanExecuteAllWrite = !hasAllErrors;
        FirstErrorParameter = firstError;

        // 如果从可执行变为不可执行，且有错误参数，自动滚动到第一个错误
        if (previousCanExecutePartial && !CanExecutePartialWrite && FirstErrorParameter != null)
        {
            // 延迟执行滚动，确保UI更新完成
            System.Windows.Application.Current?.Dispatcher.BeginInvoke(new Action(() => { ScrollToFirstError(); }),
                System.Windows.Threading.DispatcherPriority.Background);
        }

        // 通知命令状态变化
        SetParaCommand.NotifyCanExecuteChanged();
        SetParamsAllCommand.NotifyCanExecuteChanged();
    }

    /// <summary>
    /// 根据参数获取其所属的参数组
    /// </summary>
    /// <param name="param">参数对象</param>
    /// <returns>参数组枚举</returns>
    private SecondDeviceDataSetCode GetParamGroup(ParameterModel param)
    {
        // 根据参数的Group属性或其他标识来判断参数组
        // 这里需要根据实际的参数分组逻辑来实现
        if (param.Group?.Contains("Motor") == true || param.Name?.Contains("Motor") == true)
            return SecondDeviceDataSetCode.Motor;
        if (param.Group?.Contains("System") == true || param.Name?.Contains("System") == true)
            return SecondDeviceDataSetCode.System;
        if (param.Group?.Contains("Encoder") == true || param.Name?.Contains("Encoder") == true)
            return SecondDeviceDataSetCode.Encoder;
        if (param.Group?.Contains("Protect") == true || param.Name?.Contains("Protect") == true)
            return SecondDeviceDataSetCode.Protect;
        if (param.Group?.Contains("Control") == true || param.Name?.Contains("Control") == true)
            return SecondDeviceDataSetCode.Control;
        if (param.Group?.Contains("Position") == true || param.Name?.Contains("Position") == true)
            return SecondDeviceDataSetCode.Position;
        if (param.Group?.Contains("Speed") == true || param.Name?.Contains("Speed") == true)
            return SecondDeviceDataSetCode.Speed;
        if (param.Group?.Contains("Torque") == true || param.Name?.Contains("Torque") == true)
            return SecondDeviceDataSetCode.Torque;

        // 默认返回Motor组
        return SecondDeviceDataSetCode.Motor;
    }

    #endregion

    #region Command

    /// <summary>
    /// 检查是否可以执行写入操作
    /// </summary>
    /// <returns></returns>
    /// <summary>
    /// 部分写入操作的CanExecute方法
    /// </summary>
    private bool CanExecutePartialWriteOperation() => CanExecutePartialWrite;

    /// <summary>
    /// 全部写入操作的CanExecute方法
    /// </summary>
    private bool CanExecuteAllWriteOperation() => CanExecuteAllWrite;

    /// <summary>
    ///     设置刚修改的那组参数
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanExecutePartialWriteOperation))]
    public void SetPara()
    {
        // 验证参数有效性
        if (!ValidateParameters())
        {
            MsgToUiHelper.SendMsgError("存在无效参数，请检查输入值后重试");
            return;
        }

        switch (SelectFirmwareParaSet)
        {
            case SecondDeviceDataSetCode.Motor:
                SetMotorParams();
                break;
            case SecondDeviceDataSetCode.System:
                SetSystemParams();
                break;
            case SecondDeviceDataSetCode.Encoder:
                SetEncoderParams();
                break;
            case SecondDeviceDataSetCode.Protect:
                SetProtectParams();
                break;
            case SecondDeviceDataSetCode.Control:
                SetControlParams();
                break;
            case SecondDeviceDataSetCode.Position:
                SetPositionParams();
                break;
            case SecondDeviceDataSetCode.Speed:
                SetSpeedParams();
                break;
            case SecondDeviceDataSetCode.Torque:
                SetTorqueParams();
                break;
        }
    }

    /// <summary>
    ///     设置全部参数
    /// </summary>
    /// <returns></returns>
    [Log]
    [RelayCommand(CanExecute = nameof(CanExecuteAllWriteOperation))]
    public async Task SetParamsAll()
    {
        // 验证所有参数有效性
        if (!ValidateAllParameters())
        {
            MsgToUiHelper.SendMsgError("存在无效参数，请检查所有输入值后重试");
            return;
        }

        try
        {
            SetMotorParams();
            await Task.Delay(100);
            SetSystemParams();
            await Task.Delay(100);
            SetEncoderParams();
            await Task.Delay(100);
            SetProtectParams();
            await Task.Delay(100);
            SetControlParams();
            await Task.Delay(100);
            SetPositionParams();
            await Task.Delay(100);
            SetSpeedParams();
            await Task.Delay(100);
            SetTorqueParams();

            MsgToUiHelper.SendMsgInfo("所有参数设置完成");
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError($"参数设置失败: {ex.Message}");
        }
    }

    [Log]
    private MotorParamPackage SetMotorParams()
    {
        var val = (MotorParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Motor];
        var package = _mapper.Map<MotorParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private SystemParamPackage SetSystemParams()
    {
        var val = (SystemParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.System];
        var package = _mapper.Map<SystemParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private EncoderParamPackage SetEncoderParams()
    {
        var val = (EncoderParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Encoder];
        var package = _mapper.Map<EncoderParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private ProtectParamPackage SetProtectParams()
    {
        var val = (ProtectParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Protect];
        var package = _mapper.Map<ProtectParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private ControlStateParamPackage SetControlParams()
    {
        var val = (ControlStateParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Control];
        var package = _mapper.Map<ControlStateParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private PositionParamPackage SetPositionParams()
    {
        var val = (PositionParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Position];
        var package = _mapper.Map<PositionParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private SpeedParamPackage SetSpeedParams()
    {
        var val = (SpeedParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Speed];
        var package = _mapper.Map<SpeedParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    private TorqueParamPackage SetTorqueParams()
    {
        var val = (TorqueParamModel)ServoContext.Instance.ParamsDic[ParamTableEnum.Torque];
        var package = _mapper.Map<TorqueParamPackage>(val);
        package.AxisSelect = Data.SelectedAxis;
        servoSerialPort.Send(package);
        return package;
    }

    [Log]
    [RelayCommand]
    public void ParaClear()
    {
        servoSerialPort.ParaClear();
    }

    [RelayCommand]
    public void RequestAuth()
    {
        Task.Run(async () =>
        {
            await Task.Delay(10);
            servoSerialPort.RequestAuthorization();
        });
    }

    [Log]
    [RelayCommand]
    public void ErrorReset()
    {
        servoSerialPort.ErrorReset();
    }

    [Log]
    [RelayCommand]
    public void SoftReset()
    {
        servoSerialPort.SoftReset();
        ServoContext.Instance.RefreshData();
    }

    [Log]
    [RelayCommand]
    public void ErrorRecordClear()
    {
        servoSerialPort.ErrorRecordClear();
    }

    /// <summary>
    ///     设置EtherCAT模式
    /// </summary>
    [Log]
    public void SetDriverControlBy()
    {
        DriverMode.Mode = 0;
        DriverMode.SubMode = 0;
        var package = DriverMode.MapTo<DriverModeSetPackage>();
        servoSerialPort.SetDriverMode(package);
    }

    /// <summary>
    ///     设置本地模式
    /// </summary>
    /// <returns></returns>
    [Log]
    [RelayCommand]
    public Task<DriverModeSetModel> SetDriverMode()
    {
        if (DriverMode.SubMode != -1) //防止AutoMapper报错
        {
            var package = DriverMode.MapTo<DriverModeSetPackage>();
            servoSerialPort.SetDriverMode(package);
        }

        return Task.FromResult(DriverMode);
    }
        
    /// <summary>
    /// 设置 CDM 目标位置
    /// </summary>
    /// <param name="targetPosition">目标位置</param>
    [RelayCommand]
    private void SetCdmTargetPosition(double targetPosition)
    {
        servoSerialPort.SetCdmTargetPos((uint)targetPosition);
    }

    /// <summary>
    /// 设置 CDM 驱动器动子检测位置
    /// </summary>
    /// <param name="moverCheckPosition">动子检测位置</param>
    [RelayCommand]
    private void SetCdmMoverCheckPosition(double moverCheckPosition)
    {
        servoSerialPort.SetCdmMoverCheckPos((uint)moverCheckPosition);
    }

    /// <summary>
    /// 设置 CDM 驱动器使能电机位置
    /// </summary>
    /// <param name="focEnsPosition">使能电机位置</param>
    [RelayCommand]
    private void SetCdmFocEnsPosition(double focEnsPosition)
    {
        servoSerialPort.SetCdmFocEnsPos((uint)focEnsPosition);
    }

    #endregion
}

public partial class ReadSetCombineModel : ObservableObject
{
    [ObservableProperty] public string name;

    [ObservableProperty] public float read;

    [ObservableProperty] public float set;

    public int AxisNum { get; set; }
}