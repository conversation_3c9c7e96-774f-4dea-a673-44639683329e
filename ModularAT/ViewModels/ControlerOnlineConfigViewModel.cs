using ModularAT.Localization.Resources;
using System.ComponentModel;
using AutoMapper.Internal;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using ModularAT.Common;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Common.Helper;
using ModularAT.Common.Log;
using ModularAT.Driver.Controller;
using ModularAT.Entity.Config;
using ModularAT.Entity.Enum;
using ModularAT.Entity.OnlineConfig;
using ModularAT.Service.Setting;
using ModularAT.Service.Simulation;

namespace ModularAT.ViewModels;

public partial class ControlerOnlineConfigViewModel : ModularATViewModelBase,
    IRecipient<ValueChangedMessage<IEnumerable<Online_StationCfgDto>>>,
    IRecipient<ValueChangedMessage<Online_SysCfgDto>>
{
    #region Constructor

    public ControlerOnlineConfigViewModel(NavigationService navigationService) : base(navigationService)
    {
        IsActive = true;
        LoadParams(LineConfigEnum.SysCfgPara);
        OnlineParameters.ListChanged += ParamItems_ListChanged;
    }

    #endregion

    #region Variables

    private readonly ControlerTcpClient _client = App.GetService<ControlerTcpClient>()!;

    /// <summary>
    /// UI列表绑定数据源
    /// </summary>
    public BindingList<OnlineConfigModel> OnlineParameters { get; set; } = [];

    //private List<Online_StationCfgDto> _stationConfigs = ControllerContext.Instance.CurrentStationConfigs;

    // private Online_SysCfgDto config = ControllerContext.Instance.CurrentSysConfig;

    private LineConfigEnum _currentConfigType = LineConfigEnum.SysCfgPara;

    #endregion

    #region Functions

    /// <summary>
    /// 点击不同配置按钮，加载不同的配置参数
    /// </summary>
    /// <param name="configEnum"></param>
    [RelayCommand]
    private void LoadParams(LineConfigEnum configEnum)
    {
        _currentConfigType = configEnum;
        OnlineParameters.Clear();
        ControllerContext.Instance.CurrentStationConfigs.Clear();
        OnlineConfigService.LoadXmlDatas(configEnum).ForAll(c => OnlineParameters.Add(c));
    }


    /// <summary>
    /// 把UI参数列表转换成对应的Dto对象列表，并同步更改
    /// </summary>
    private interface IConfigUpdateStrategy
    {
        void HandleUpdate(OnlineConfigModel item, string propertyName, int index, ListChangedType changeType);
    }

    private class StationUpdateStrategy : IConfigUpdateStrategy
    {
        private readonly List<Online_StationCfgDto> _stationConfigs;

        public StationUpdateStrategy(List<Online_StationCfgDto> stationConfigs)
        {
            _stationConfigs = stationConfigs;
        }

        public void HandleUpdate(OnlineConfigModel item, string propertyName, int index, ListChangedType changeType)
        {
            if (changeType == ListChangedType.ItemAdded &&
                item.EnglishName == propertyName)
            {
                _stationConfigs.Add(new Online_StationCfgDto(index));
            }

            var station = _stationConfigs[index];
            OnlineConfigModel.SetTypedPropertyValue(station, propertyName, item.Value, item.DataType);
        }
    }

    private class SystemUpdateStrategy : IConfigUpdateStrategy
    {
        private readonly Online_SysCfgDto _sysConfig;

        public SystemUpdateStrategy(Online_SysCfgDto sysConfig)
        {
            _sysConfig = sysConfig;
        }

        public void HandleUpdate(OnlineConfigModel item, string propertyName, int index, ListChangedType changeType)
        {
            OnlineConfigModel.SetTypedPropertyValue(_sysConfig, propertyName, item.Value, item.DataType);
            ControllerContext.Instance.CurrentSysConfig = _sysConfig;
        }
    }

    private class ViewUpdateStrategy : IConfigUpdateStrategy
    {
        private readonly Online_ViewLineConfig _viewConfig;

        public ViewUpdateStrategy(Online_ViewLineConfig config)
        {
            _viewConfig = config;
        }

        public void HandleUpdate(OnlineConfigModel item, string propertyName, int index, ListChangedType changeType)
        {
            OnlineConfigModel.SetTypedPropertyValue(_viewConfig, propertyName, item.Value, item.DataType);
        }
    }

    // 使用静态策略实例避免重复创建
    private static readonly IConfigUpdateStrategy _stationStrategy =
        new StationUpdateStrategy(ControllerContext.Instance.CurrentStationConfigs);

    private static readonly IConfigUpdateStrategy _systemStrategy =
        new SystemUpdateStrategy(ControllerContext.Instance.CurrentSysConfig);

    private static readonly IConfigUpdateStrategy _viewStrategy =
        new ViewUpdateStrategy(ControllerContext.Instance.CurrentViewConfig);

    private void ParamItems_ListChanged(object sender, ListChangedEventArgs e)
    {
        if (e.NewIndex == -1) return;

        var item = OnlineParameters[e.NewIndex];
        var propertyName = char.ToUpper(item.EnglishName[0]) + item.EnglishName.Substring(1);
        var lastChar = item.Title[item.Title.Length - 1].ToString();
        var index = int.TryParse(lastChar, out var result) ? result : 0;

        if (!IsValidChange(e)) return;

        // 使用策略模式
        var strategy = item.GroupName switch
        {
            "Station" => _stationStrategy,
            "Sys" => _systemStrategy,
            "UIViewLine" => _viewStrategy,
            _ => null
        };

        strategy?.HandleUpdate(item, propertyName, index, e.ListChangedType);
    }

    private bool IsValidChange(ListChangedEventArgs e)
    {
        return e.ListChangedType switch
        {
            ListChangedType.ItemAdded => true,
            ListChangedType.ItemChanged => e.PropertyDescriptor?.Name == "Value",
            _ => false
        };
    }

    [Log]
    [RelayCommand]
    private async Task SendConfig()
    {
        switch (_currentConfigType)
        {
            case LineConfigEnum.SysCfgPara:
                await SendSysConfigs();
                break;
            case LineConfigEnum.StationCfgPara:
                await SendStationConfigs();
                break;
            case LineConfigEnum.UIViewLineCfgPara:
                SendViewConfigs();
                break;
            //Todo: 其他配置
        }
    }

    /// <summary>
    /// 下发在线工位配置
    /// </summary>
    private async Task SendStationConfigs()
    {
        var package = new OnlineConfigCmdPackage<OnlineStationCfgPara>
        {
            //为防止顺序错乱，先排序
            Datas = ControllerContext.Instance.CurrentStationConfigs.OrderBy(station => station.IStationID).MapTo<OnlineStationCfgPara>()
        };
        var ret = await _client.SendWaitReturnRequestInfoAsync(package, 2000);
        if (ret != null)
        {
            OnlineConfigService.SaveXmlDatas(LineConfigEnum.StationCfgPara, OnlineParameters);
            MsgToUiHelper.SendMsgInfo(Lang.ControlerOnlineConfigViewModel_Workstation_config_distributed);
        }
    }

    /// <summary>
    /// 下发系统配置
    /// </summary>
    private async Task SendSysConfigs()
    {
        var package = new OnlineConfigCmdPackage<OnlineSysCfgPara>
        {
            Datas = [ControllerContext.Instance.CurrentSysConfig.MapTo<OnlineSysCfgPara>()]
        };
        var ret = await _client.SendWaitReturnRequestInfoAsync(package, 2000);
        if (ret != null)
        {
            OnlineConfigService.SaveXmlDatas(LineConfigEnum.SysCfgPara, OnlineParameters);
            MsgToUiHelper.SendMsgInfo(Lang.ControlerOnlineConfigViewModel_Send_success);
        }
    }

    private readonly SimulationProtocolServiceImpl simulationService = App.GetService<SimulationProtocolServiceImpl>();


    /// <summary>
    /// 下发在线UI配置
    /// </summary>
    private void SendViewConfigs()
    {
        var config = OnlineConfigModel.ListToGeneric<Online_ViewLineConfig>(OnlineParameters);
        // simulationService.SendLineConfigDirect(config);
        OnlineConfigService.SaveXmlDatas(LineConfigEnum.UIViewLineCfgPara, OnlineParameters);
        MsgToUiHelper.SendMsgInfo(Lang.ControlerOnlineConfigViewModel_Send_success);

        // List<ViewLineConfig> stationConfigs = new List<ViewLineConfig>();
        // var d = new ViewLineConfig() { PrefabName = "Stator",Name = "LineUp"};
        // stationConfigs.Add(d);
        // stationConfigs.Add(d);
        // stationConfigs.Add(d);
        // stationConfigs.Add(d);
        // stationConfigs.Add(d);
        // stationConfigs.Add(d);
        // string path = "settings/viewLineConfig.json";
        // JsonHelper.SaveToJson(stationConfigs, path);
    }

    private void SendSlaveNodeConfigs()
    {
        MsgToUiHelper.SendMsgInfo(Lang.ControlerOnlineConfigViewModel_Send_success);
    }


    //在线配置反馈赋值
    public void Receive(ValueChangedMessage<IEnumerable<Online_StationCfgDto>> message)
    {
        var cfgs = message.Value.ToList();
        var names = typeof(Online_StationCfgDto).GetProperties().Select(x => x.Name).ToList();
        cfgs.ForEach(station =>
        {
            names.ForEach(name =>
            {
                var paramModel = OnlineParameters.FirstOrDefault(x =>
                    x.EnglishName == name && x.GroupName.First() == station.IStationID);
                paramModel.ReadValue = ObjectUtil<string>.GetPropertyValue(station, name);
            });
        });
    }

    //系统参数反馈赋值
    public void Receive(ValueChangedMessage<Online_SysCfgDto> message)
    {
        var cfg = message.Value;
        var names = typeof(Online_SysCfgDto).GetProperties().Select(x => x.Name).ToList();
        names.ForEach(name =>
        {
            var paramModel = OnlineParameters.FirstOrDefault(x => x.EnglishName == name);
            // paramModel.ReadValue = ObjectUtil<string>.GetPropertyValue(cfg, name);
            switch (paramModel.DataType)
            {
                case "Int16":
                    paramModel.ReadValue = ObjectUtil<short>.GetPropertyValue(cfg, name).ToString();
                    break;
                
            }
        });
    }

    #endregion
}