using ModularAT.Localization.Resources;
﻿using System.Collections.ObjectModel;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using FluentFTP;
using ModularAT.Common.Ftp;
using ModularAT.Common.Helper;
using ModularAT.Common.Log;
using ModularAT.Entity;
using ModularAT.Entity.Config;

namespace ModularAT.ViewModels;

public partial class FtpClientViewModel : ModularATViewModelBase
{
    private FtpClient _ftpClient;
    private Stack<string> _backStack = new Stack<string>();
    private Stack<string> _forwardStack = new Stack<string>();
    private Stack<string> _backStackLocal = new Stack<string>();
    private Stack<string> _forwardStackLocal = new Stack<string>();

    private readonly string path = "settings/Ftp.json";

    [ObservableProperty] private bool _isSecure = false;

    [ObservableProperty] private ObservableCollection<FtpFileItem> _remoteFiles = [];

    [ObservableProperty] private ObservableCollection<FtpFileItem> _localFiles = [];

    [ObservableProperty] private FtpFileItem _selectedRemoteFile;
    [ObservableProperty] private FtpFileItem _selectedLocalFile;


    public bool IsConnected
    {
        get;
        set => SetProperty(ref field, value);
    }

    public string CurrentRemoteDirectory
    {
        get;
        set
        {
            if (SetProperty(ref field, value))
            {
                if (IsConnected)
                {
                    LoadRemoteFilesCommand.Execute(null);
                }
            }
        }
    } = "/";

    // 标记是否是导航操作（后退/前进），避免在导航时记录历史
    private bool _isNavigating = false;

    public string CurrentLocalDirectory
    {
        get;
        set
        {
            if (SetProperty(ref field, value))
            {
                LoadLocalFilesCommand.Execute(null);
            }
        }
    } = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

    public string StatusMessage
    {
        get;
        set => SetProperty(ref field, value);
    }

    public ObservableCollection<FtpLogModel> TransferLogs
    {
        get;
        set => SetProperty(ref field, value);
    } = [];

    public FtpClientViewModel(NavigationService navigationService) : base(navigationService)
    {
        Config = File.Exists(path) ? JsonHelper.LoadFromJson<FtpParamConfig>(path) : new FtpParamConfig();
        LoadLocalFilesCommand.Execute(null);
    }

    [RelayCommand]
    private async Task Connect()
    {
        try
        {
            JsonHelper.SaveToJson(Config, path);
            if (IsConnected)
            {
                await Disconnect();
                return;
            }

            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Connecting_to_ftp_server);
            _ftpClient = new FtpClient(Config.Hostname, Config.Username, Config.Password, Config.Port);

            // 增强的FTP配置
            _ftpClient.Config.RetryAttempts = 5;  // 增加重试次数
            _ftpClient.Config.ConnectTimeout = 3000; // 延长连接超时
            _ftpClient.Config.DataConnectionType = FtpDataConnectionType.PASV; // 自动处理PASV模式

            // 新增PASV模式配置
            _ftpClient.Config.ValidateAnyCertificate = true; // 绕过证书验证（测试环境用）
            _ftpClient.Config.ReadTimeout = 30000;

            if (IsSecure)
            {
                _ftpClient.Config.EncryptionMode = FtpEncryptionMode.Explicit;
                _ftpClient.Config.SslProtocols = System.Security.Authentication.SslProtocols.Tls12;
                _ftpClient.Config.DataConnectionEncryption = true; // 安全模式下需要开启数据通道加密
            }

            await Task.Run(() => _ftpClient.Connect());

            IsConnected = true;
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Connected_to_ftp_server);
            AddLog(Lang.FtpClientViewModel_Connect, Lang.FtpClientViewModel_Connected_to_ftp_server, true);

            await LoadRemoteFiles();
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError($"连接失败: {ex.Message}");
            AddLog(Lang.FtpClientViewModel_Connect, $"连接失败: {ex.Message}", false);
            IsConnected = false;
        }
    }

    public FtpParamConfig Config { get; set; }

    [RelayCommand]
    private async Task Disconnect()
    {
        if (_ftpClient.IsConnected)
        {
            await Task.Run(() => _ftpClient.Disconnect());
            IsConnected = false;
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Disconnected);
            AddLog(Lang.FtpClientViewModel_Disconnect, Lang.FtpClientViewModel_Disconnected, true);
            RemoteFiles.Clear();
        }
    }

    [RelayCommand]
    private async Task LoadRemoteFiles()
    {
        if (!IsConnected)
        {
            return;
        }

        try
        {
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Loading_remote_directory + CurrentRemoteDirectory);
            RemoteFiles.Clear();

            // 添加返回上级目录的项目（如果不在根目录）
            if (CurrentRemoteDirectory != "/")
            {
                RemoteFiles.Add(new FtpFileItem
                {
                    Name = "..",
                    FullPath = Path.GetDirectoryName(CurrentRemoteDirectory.TrimEnd('/'))?.Replace("\\", "/") ?? "/",
                    IsDirectory = true,
                    Size = 0,
                    Modified = DateTime.MinValue
                });
            }

            var items = await Task.Run(() => _ftpClient.GetListing(CurrentRemoteDirectory));

            foreach (var item in items)
            {
                RemoteFiles.Add(new FtpFileItem
                {
                    Name = item.Name,
                    FullPath = item.FullName,
                    IsDirectory = item.Type == FtpObjectType.Directory,
                    Size = item.Size,
                    Modified = item.Modified
                });
            }

            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Remote_directory_loaded + CurrentRemoteDirectory);
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Failed_to_load_remote_directory + ex.Message);
            AddLog(Lang.FtpClientViewModel_Browse, $"加载远程目录失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private void LoadLocalFiles()
    {
        try
        {
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Loading_local_directory + CurrentLocalDirectory);
            LocalFiles.Clear();

            // 添加返回上级目录的项目（如果不在根目录）
            if (Directory.GetParent(CurrentLocalDirectory) != null)
            {
                LocalFiles.Add(new FtpFileItem
                {
                    Name = "..",
                    FullPath = Directory.GetParent(CurrentLocalDirectory)?.FullName,
                    IsDirectory = true,
                    Size = 0,
                    Modified = DateTime.MinValue
                });
            }

            // 添加子目录
            foreach (var dir in Directory.GetDirectories(CurrentLocalDirectory))
            {
                var dirInfo = new DirectoryInfo(dir);
                LocalFiles.Add(new FtpFileItem
                {
                    Name = dirInfo.Name,
                    FullPath = dir,
                    IsDirectory = true,
                    Size = 0,
                    Modified = dirInfo.LastWriteTime
                });
            }

            // 添加文件
            foreach (var file in Directory.GetFiles(CurrentLocalDirectory))
            {
                var fileInfo = new FileInfo(file);
                LocalFiles.Add(new FtpFileItem
                {
                    Name = fileInfo.Name,
                    FullPath = file,
                    IsDirectory = false,
                    Size = fileInfo.Length,
                    Modified = fileInfo.LastWriteTime
                });
            }

            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Local_directory_loaded + CurrentLocalDirectory);
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Failed_to_load_local_directory + ex.Message);
            AddLog(Lang.FtpClientViewModel_Browse, $"加载本地目录失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private void NavigateLocalDirectory(FtpFileItem item)
    {
        if (item.IsDirectory)
        {
            _backStackLocal.Push(CurrentLocalDirectory);
            _forwardStackLocal.Clear();
            CurrentLocalDirectory = item.FullPath;
        }
    }

    // 本地前进命令
    [RelayCommand]
    private void ForwardLocal()
    {
        if (_forwardStackLocal.Count > 0)
        {
            _backStackLocal.Push(CurrentLocalDirectory);
            CurrentLocalDirectory = _forwardStackLocal.Pop();
        }
    }

    // 本地后退命令
    [RelayCommand]
    private void BackLocal()
    {
        if (_backStackLocal.Count > 0)
        {
            _forwardStackLocal.Push(CurrentLocalDirectory);
            CurrentLocalDirectory = _backStackLocal.Pop();
        }
    }


    [RelayCommand]
    private void UpLocal()
    {
        var parent = Directory.GetParent(CurrentLocalDirectory);
        if (parent != null)
        {
            _backStackLocal.Push(CurrentLocalDirectory);
            _forwardStackLocal.Clear();
            CurrentLocalDirectory = parent.FullName;
        }
    }


    [RelayCommand]
    private async Task NavigateRemoteDirectory(FtpFileItem item)
    {
        if (item.IsDirectory)
        {
            _backStack.Push(CurrentRemoteDirectory);
            _forwardStack.Clear();
            CurrentRemoteDirectory = item.FullPath;
            await LoadRemoteFiles();
        }
    }

    [RelayCommand]
    private async Task Back()
    {
        if (_backStack.Count > 0)
        {
            _forwardStack.Push(CurrentRemoteDirectory);
            CurrentRemoteDirectory = _backStack.Pop();
            await LoadRemoteFiles();
        }
    }

    [RelayCommand]
    private async Task Forward()
    {
        if (_forwardStack.Count > 0)
        {
            _backStack.Push(CurrentRemoteDirectory);
            CurrentRemoteDirectory = _forwardStack.Pop();
            await LoadRemoteFiles();
        }
    }

    [RelayCommand]
    private async Task Up()
    {
        if (CurrentRemoteDirectory != "/")
        {
            _backStack.Push(CurrentRemoteDirectory);
            _forwardStack.Clear();
            CurrentRemoteDirectory = Path.GetDirectoryName(CurrentRemoteDirectory.TrimEnd('/'))?.Replace("\\", "/") ?? "/";
            await LoadRemoteFiles();
        }
    }


    [RelayCommand]
    private async Task DownloadFile(FtpFileItem item)
    {
        if (item.IsDirectory || !IsConnected)
        {
            return;
        }

        try
        {
            var localPath = Path.Combine(CurrentLocalDirectory, item.Name);
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Downloading + item.Name);

            var progress = new Progress<FtpProgress>(p => { MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Downloading + item.Name + " - " + p.Progress + "%"); });

            var status = await Task.Run(() =>
                _ftpClient.DownloadFile(localPath, item.FullPath));

            if (status == FtpStatus.Success)
            {
                MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Download_completed + item.Name);
                AddLog(Lang.FtpClientViewModel_Download, $"{item.FullPath} -> {localPath}", true);
                LoadLocalFilesCommand.Execute(null);
            }
            else
            {
                MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Download_failed + item.Name);
                AddLog(Lang.FtpClientViewModel_Download, $"下载失败: {item.Name}", false);
            }
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Download_failed + ex.Message);
            AddLog(Lang.FtpClientViewModel_Download, $"下载失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private async Task UploadFile(FtpFileItem item)
    {
        if (item.IsDirectory || !IsConnected)
        {
            return;
        }

        try
        {
            var remotePath = Path.Combine(CurrentRemoteDirectory, item.Name).Replace("\\", "/");
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Uploading + item.Name);

            var status = await Task.Run(() => _ftpClient.UploadFile(item.FullPath, remotePath));

            if (status == FtpStatus.Success)
            {
                MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Upload_completed + item.Name);
                AddLog(Lang.FtpClientViewModel_Upload, $"{item.FullPath} -> {remotePath}", true);
                await LoadRemoteFiles();
            }
            else
            {
                MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Upload_failed + item.Name);
                AddLog(Lang.FtpClientViewModel_Upload, $"上传失败: {item.Name}", false);
            }
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Upload_failed + ex.Message);
            AddLog(Lang.FtpClientViewModel_Upload, $"上传失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private async Task CreateRemoteDirectory(string directoryName)
    {
        if (string.IsNullOrWhiteSpace(directoryName) || !IsConnected)
        {
            return;
        }

        try
        {
            var path = Path.Combine(CurrentRemoteDirectory, directoryName).Replace("\\", "/");
            await Task.Run(() => _ftpClient.CreateDirectory(path));
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Directory_created + directoryName);
            AddLog(Lang.FtpClientViewModel_Create_directory, $"已创建目录: {path}", true);
            await LoadRemoteFiles();
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Failed_to_create_directory + ex.Message);
            AddLog(Lang.FtpClientViewModel_Create_directory, $"创建目录失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private async Task DeleteRemoteFile(FtpFileItem item)
    {
        if (!IsConnected)
        {
            return;
        }

        try
        {
            if (item.IsDirectory)
            {
                await Task.Run(() => _ftpClient.DeleteDirectory(item.FullPath));
                MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Directory_deleted + item.Name);
                AddLog(Lang.FtpClientViewModel_Delete, $"已删除目录: {item.FullPath}", true);
            }
            else
            {
                await Task.Run(() => _ftpClient.DeleteFile(item.FullPath));

                MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_File_deleted + item.Name);
                AddLog(Lang.FtpClientViewModel_Delete, $"已删除文件: {item.FullPath}", true);
            }

            await LoadRemoteFiles();
        }
        catch (Exception ex)
        {
            StatusMessage = $"删除失败: {ex.Message}";
            AddLog(Lang.FtpClientViewModel_Delete, $"删除失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private void DeleteLocalFile(FtpFileItem item)
    {
        if (item.IsDirectory)
        {
            return;
        }

        try
        {
            File.Delete(item.FullPath);
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_File_deleted + item.Name);
            AddLog(Lang.FtpClientViewModel_Delete, $"已删除文件: {item.FullPath}", true);
            LoadLocalFilesCommand.Execute(null);
        }
        catch (Exception ex)
        {
            StatusMessage = $"删除失败: {ex.Message}";
            AddLog(Lang.FtpClientViewModel_Delete, $"删除失败: {ex.Message}", false);
        }
    }

    private void AddLog(string operation, string message, bool success)
    {
        var log = new FtpLogModel
        {
            Timestamp = DateTime.Now,
            Operation = operation,
            Message = message,
            Success = success
        };

        App.Current.Dispatcher.Invoke(() =>
        {
            TransferLogs.Insert(0, log);
            // 限制日志数量
            while (TransferLogs.Count > 100)
            {
                TransferLogs.RemoveAt(TransferLogs.Count - 1);
            }
        });
    }


    [RelayCommand]
    private void CreateLocalDirectory(string directoryName)
    {
        if (string.IsNullOrWhiteSpace(directoryName))
        {
            return;
        }

        try
        {
            var path = Path.Combine(CurrentLocalDirectory, directoryName);
            Directory.CreateDirectory(path);
            MsgToUiHelper.SendMsgInfo(Lang.FtpClientViewModel_Directory_created + directoryName);
            AddLog(Lang.FtpClientViewModel_Create_directory, $"已创建目录: {path}", true);
            LoadLocalFilesCommand.Execute(null);
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError(Lang.FtpClientViewModel_Failed_to_create_directory + ex.Message);
            AddLog(Lang.FtpClientViewModel_Create_directory, $"创建目录失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private void OpenLocalFile(FtpFileItem item)
    {
        if (item.IsDirectory)
        {
            NavigateLocalDirectoryCommand.Execute(item);
            return;
        }

        try
        {
            // 使用系统默认程序打开文件
            var startInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = item.FullPath,
                UseShellExecute = true
            };
            System.Diagnostics.Process.Start(startInfo);

            MsgToUiHelper.SendMsgInfo($"已打开文件: {item.Name}");
            AddLog(Lang.FtpClientViewModel_Open, $"已打开文件: {item.FullPath}", true);
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError($"打开文件失败: {ex.Message}");
            AddLog(Lang.FtpClientViewModel_Open, $"打开文件失败: {ex.Message}", false);
        }
    }

    [RelayCommand]
    private async Task OpenRemoteFile(FtpFileItem item)
    {
        if (item.IsDirectory || !IsConnected)
        {
            if (item.IsDirectory && IsConnected)
            {
                await NavigateRemoteDirectoryCommand.ExecuteAsync(item);
            }
            return;
        }

        try
        {
            // 创建临时文件路径
            string tempDir = Path.Combine(Path.GetTempPath(), "ModularAT_FTP_Temp");
            Directory.CreateDirectory(tempDir);
            string tempFilePath = Path.Combine(tempDir, item.Name);

            // 下载文件到临时目录
            MsgToUiHelper.SendMsgInfo($"正在下载临时文件: {item.Name}");

            var progress = new Progress<FtpProgress>(p =>
            {
                MsgToUiHelper.SendMsgInfo($"下载中: {item.Name} - {p.Progress}%");
            });

            var status = await Task.Run(() =>
                _ftpClient.DownloadFile(tempFilePath, item.FullPath));

            if (status == FtpStatus.Success)
            {
                // 使用系统默认程序打开文件
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFilePath,
                    UseShellExecute = true
                };
                System.Diagnostics.Process.Start(startInfo);

                MsgToUiHelper.SendMsgInfo($"已打开文件: {item.Name}");
                AddLog(Lang.FtpClientViewModel_Open, $"已打开远程文件: {item.FullPath}", true);
            }
            else
            {
                MsgToUiHelper.SendMsgError($"打开文件失败: {item.Name}");
                AddLog(Lang.FtpClientViewModel_Open, $"打开远程文件失败: {item.Name}", false);
            }
        }
        catch (Exception ex)
        {
            MsgToUiHelper.SendMsgError($"打开文件失败: {ex.Message}");
            AddLog(Lang.FtpClientViewModel_Open, $"打开远程文件失败: {ex.Message}", false);
        }
    }
}