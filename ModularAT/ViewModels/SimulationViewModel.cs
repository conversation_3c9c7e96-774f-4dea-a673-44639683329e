using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using GRpc.SimulationProtocol;
using ModularAT.Common.Helper;
using ModularAT.Entity.Config;
using ModularAT.Entity.Controller;
using ModularAT.Service.Setting;
using ModularAT.Service.Simulation;

namespace ModularAT.ViewModels;

public class SimulationViewModel : ModularATViewModelBase,
    IRecipient<ValueChangedMessage<IEnumerable<AxisFeedBackModel>>>,
    IRecipient<ValueChangedMessage<IEnumerable<RoAxisFeedBackModel>>>
{
    private readonly SimulationProtocolServiceImpl simulationService = App.GetService<SimulationProtocolServiceImpl>();

    public SimulationViewModel(NavigationService navigation) : base(navigation)
    {
        IsActive = true;
    }
    

    public void Receive(ValueChangedMessage<IEnumerable<AxisFeedBackModel>> message)
    {
        var data = message.Value;
        var grpcFeedbacks = data.Select((item, index) => new AxisFeedback
        {
            DiAxisCurPos = item.DiAxisCurPos,
            DiAxisCurVel = item.DiAxisCurVel,
            IAxisCurObject = item.IAxisCurObject,
            IAxisCurObjectID = item.IAxisCurObjectID,
            IAxisID = index,
            UdiAxisRunState = item.UdiAxisRunState,
            UiAxisDrvErrCode = item.UiAxisDrvErrCode
        });
        AxisFeedbackList axisFeedbackList = new();
        axisFeedbackList.AxisFeedbacks.AddRange(grpcFeedbacks);
        simulationService.SendAxisFeedbackDirect(ref axisFeedbackList);
    }

    public void Receive(ValueChangedMessage<IEnumerable<RoAxisFeedBackModel>> message)
    {
        var data = message.Value;
        var grpcFeedbacks = data.Select((item, index) => new RoAxisFeedback
        {
            BPoweron = item.BPoweron,
            BRunning = item.BRunning,
            BHomeDone = item.BHomeDone,
            DwAxisErrorID = item.DwAxisErrorID,
            LrActVelocity = item.LrActVelocity,
            LrActPosition = Math.Round(item.LrActPosition, 3)
        });
        RoAxisFeedbackList list = new();
        list.RoAxisFeedbacks.AddRange(grpcFeedbacks);
        simulationService.SendRoAxisFeedbackDirect(ref list);
    }
}