using Microsoft.Extensions.DependencyInjection;

namespace ModularAT.ViewModels;

public static class ViewModelLocator
{
    public static DataViewModel DataViewModel => App.Current.Services.GetService<DataViewModel>();

    public static MainViewModel MainViewModel => App.Current.Services.GetService<MainViewModel>();

    public static ServoSettingViewModel ServoSetting => App.Current.Services.GetService<ServoSettingViewModel>();

    public static ServoSerialPortViewModel ServoSerialPortSetting =>
        App.Current.Services.GetService<ServoSerialPortViewModel>();

    public static ControlerClientViewModel ControlerClient =>
        App.Current.Services.GetService<ControlerClientViewModel>();

    public static ControlerSysViewModel ControlerSys => App.Current.Services.GetService<ControlerSysViewModel>();

    public static ControlerAxisViewModel ControlerAxis => App.Current.Services.GetService<ControlerAxisViewModel>();

    public static ControlerTranStatusViewModel ControlerTranStatus =>
        App.Current.Services.GetService<ControlerTranStatusViewModel>();

    public static ControlerDebugViewModel ControlerDebug => App.Current.Services.GetService<ControlerDebugViewModel>();

    public static FtpClientViewModel FtpClientViewModel => App.Current.Services.GetService<FtpClientViewModel>();
}