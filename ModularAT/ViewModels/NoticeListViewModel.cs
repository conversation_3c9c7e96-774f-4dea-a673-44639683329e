using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using log4net.Core;
using ModularAT.Driver.Controller;
using ModularAT.Entity.Controller;

namespace ModularAT.ViewModels;

public partial class NoticeListViewModel : ModularATViewModelBase
{
    [ObservableProperty] private ObservableCollection<NoticeModel> _notices = new();

    public NoticeListViewModel(NavigationService navigationService) : base(navigationService)
    {
        // 订阅通知变更事件
        ControllerContext.Instance.NoticeChanged += OnNoticeReceived;
    }

    private void OnNoticeReceived(NoticeModel notice)
    {
        // 在UI线程上更新通知列表
        App.Current.Dispatcher.Invoke(() =>
        {
            // 检查是否已存在相同来源和ID的通知
            var existingNotice = Notices.FirstOrDefault(n => n.From == notice.From && n.Id == notice.Id);
            if (existingNotice != null)
            {
                // 更新现有通知内容
                existingNotice.Level = notice.Level;
                existingNotice.Message = notice.Message;
            }
            else
            {
                // 将新通知添加到列表顶部
                Notices.Insert(0, notice);

                // 限制通知数量，保持列表不会过长
                if (Notices.Count > 100)
                {
                    Notices.RemoveAt(Notices.Count - 1);
                }
            }
        });
    }

    // 清除所有通知
    public void ClearAllNotices()
    {
        Notices.Clear();
    }

    // 获取通知级别对应的样式名称
    public string GetLevelStyleName(Level level)
    {
        return level?.Name switch
        {
            "ERROR" => "ErrorNoticeStyle",
            "WARN" => "WarningNoticeStyle",
            _ => "InfoNoticeStyle"
        };
    }
}