using System.Collections.ObjectModel;
using System.IO;
using System.IO.Ports;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common.Attributes;
using ModularAT.Common.Helper;
using ModularAT.Driver.Servo;
using ModularAT.Entity;
using ModularAT.Entity.Config;

namespace ModularAT.ViewModels;

public partial class ServoSerialPortViewModel : ModularATViewModelBase
{
    private readonly string path = "settings/ServoSerialPort.json";
    private readonly ServoSerialPortClient servoSerialPort = App.Current.Services.GetService<ServoSerialPortClient>();

    public ServoSerialPortViewModel(NavigationService navigationService) : base(navigationService)
    {
        try
        {
            SerialPortConfig = JsonHelper.LoadFromJson<SerialPortConfig>(path);
        }
        catch (Exception e)
        {
            SerialPortConfig = new SerialPortConfig();
        }
    }

    public SerialPortConfig SerialPortConfig { get; set; }

    //[ObservableProperty]
    //private bool isConnecting;

    [Log]
    [RelayCommand]
    private void ConnectPort()
    {
        servoSerialPort.Setup(SerialPortConfig);
        servoSerialPort.Connnect();
        JsonHelper.SaveToJson(SerialPortConfig, path);
    }


    [Log]
    [RelayCommand]
    private void ClosePort()
    {
        servoSerialPort.Close();
    }

    [RelayCommand]
    private void Save()
    {
        JsonHelper.SaveToJson(SerialPortConfig, path);
    }
}