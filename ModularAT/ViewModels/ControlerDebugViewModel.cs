using System.Text;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Driver.Controller;

namespace ModularAT.ViewModels;

public partial class ControlerDebugViewModel : ObservableRecipient, IRecipient<ValueChangedMessage<string>>
{
    private readonly ControlerTcpClient client = App.Current.Services.GetService<ControlerTcpClient>();
    private readonly StringBuilder receivedMsgBuilder = new();
    private int receivedMsgCount;

    private string receivedMsgText;

    [ObservableProperty] private string? sendMsgText;

    public ControlerDebugViewModel()
    {
        IsActive = true;
    }

    public string ReceivedMsgText
    {
        get => receivedMsgText;
        set
        {
            receivedMsgText = value;
            OnPropertyChanged();
        }
    }

    public void Receive(ValueChangedMessage<string> message)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            receivedMsgBuilder.Append(message.Value);
            receivedMsgCount++;
            if (receivedMsgCount > 1000) //只保留最近1000条消息
            {
                // 移除第一条字符串
                var firstNewLineIndex = receivedMsgBuilder.IndexOf(Environment.NewLine);
                receivedMsgBuilder.Remove(0, firstNewLineIndex + Environment.NewLine.Length);
            }

            ReceivedMsgText = receivedMsgBuilder.ToString();
        });
    }

    [Log]
    [RelayCommand]
    private void SendMsg()
    {
        client.Send(SendMsgText);
    }

    [Log]
    [RelayCommand]
    private void ClearReceivedMsg()
    {
        receivedMsgBuilder.Clear();
        ReceivedMsgText = string.Empty;
    }
}