using ModularAT.Localization.Resources;
﻿using System.Collections.ObjectModel;
using AutoMapper;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Driver.Controller;
using ModularAT.Entity.Controller;
using ModularAT.Service.Setting;

namespace ModularAT.ViewModels;

public partial class ControlerTranStatusViewModel : ModularATViewModelBase,
    IRecipient<ValueChangedMessage<IEnumerable<TransStateFeedBackModel>>>,
    IRecipient<ValueChangedMessage<IEnumerable<RoAxisFeedBackModel>>>
{
    #region Constructor

    public ControlerTranStatusViewModel(NavigationService navigationService, IMapper mapper) : base(navigationService)
    {
        IsActive = true;
        //ISysRunModeSelected = SysCtrlCmdEnum.EnableBelow;
    }

    #endregion

    #region Variables

    /// <summary>
    /// 导航服务
    /// </summary>
    private readonly NavigationService navigationService;

    /// <summary>
    /// 控制器TCP客户端
    /// </summary>
    private readonly ControlerTcpClient client = App.GetService<ControlerTcpClient>();

    private IRecipient<ValueChangedMessage<IEnumerable<RoAxisFeedBackModel>>> _recipientImplementation;

    #endregion

    #region Properties

    /// <summary>
    /// 数据视图模型
    /// </summary>
    public DataViewModel Data => ViewModelLocator.DataViewModel;

    /// <summary>
    /// 控制器调试视图模型
    /// </summary>
    public ControlerDebugViewModel DebugViewModel => ViewModelLocator.ControlerDebug;

    /// <summary>
    /// 接驳反馈信息列表
    /// </summary>
    public ObservableCollection<TransStateFeedBackModel> FeedBacks { get; set; } = [];

    /// <summary>
    /// 旋转轴反馈信息列表
    /// </summary>
    public ObservableCollection<RoAxisFeedBackModel> RoAxisFeedBacks { get; set; } = [];

    /// <summary>
    /// 接驳状态模型
    /// </summary>
    [ObservableProperty]
    public partial TransStateModel TransState { get; set; } = new();

    /// <summary>
    /// 旋转轴控制命令模型
    /// </summary>
    [ObservableProperty]
    public partial RoAxisCtrlCmdModel RoAxisCtrl { get; set; } = new();

    #endregion

    #region Commands

    /// <summary>
    /// 读取接驳反馈信息命令
    /// </summary>
    [RelayCommand]
    private void Read() //读取接驳反馈信息
    {
        // 清空接驳反馈信息列表
        FeedBacks.Clear();
    }

    /// <summary>
    /// 执行命令
    /// </summary>
    [Log]
    [RelayCommand]
    private void Execute()
    {
        // 将接驳状态模型映射为接驳状态包
        var package = TransState.MapTo<TransStatePackage>();
        // Lang.ControlerTranStatusViewModel_Do_nothing是-1，这里需要减1
        package.ILeftConnectedObjectID--;
        package.IRightConnectedObjectID--;
        // 发送接驳状态包
        client.Send(package);
    }

    /// <summary>
    /// 执行命令
    /// </summary>
    [Log]
    [RelayCommand]
    private void RoAxisExecute()
    {
        var cmd = RoAxisCtrl.MapTo<RoAxisCtrlCmd>();
        var package = new RoAxisCtrlCmdPackage { Data = cmd };
        client.Send(package);
    }

    /// <summary>
    /// 接收接驳反馈信息
    /// </summary>
    /// <param name="message">接驳反馈信息消息</param>
    public void Receive(ValueChangedMessage<IEnumerable<TransStateFeedBackModel>> message)
    {
        var list = message.Value.ToList();
        App.Current?.Dispatcher.BeginInvoke(() =>
        {
            if (FeedBacks.Count == list.Count())
            {
                //把message.Value中的元素依次赋值给FeedBacks
                for (var i = 0; i < FeedBacks.Count; i++)
                {
                    FeedBacks[i] = list.ElementAt(i);
                }
            }
            else
            {
                list.ForEach(FeedBacks.Add);
            }
        });
    }

    public void Receive(ValueChangedMessage<IEnumerable<RoAxisFeedBackModel>> message)
    {
        var list = message.Value.ToList();
        App.Current?.Dispatcher.BeginInvoke(() =>
        {
            if (RoAxisFeedBacks.Count == list.Count())
            {
                for (var i = 0; i < RoAxisFeedBacks.Count; i++)
                {
                    RoAxisFeedBacks[i] = list.ElementAt(i);
                }
            }
            else
            {
                list.ForEach(RoAxisFeedBacks.Add);
            }
        });
    }

    #endregion
}