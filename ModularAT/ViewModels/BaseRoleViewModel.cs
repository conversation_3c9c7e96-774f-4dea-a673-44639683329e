using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ModularAT.Application.Interface;
using ModularAT.Common.Attributes;
using ModularAT.Entity.BaseManage;
using ModularAT.Entitys;

namespace ModularAT.ViewModels;

/// <summary>
/// 基础角色视图模型类，继承自ModularATViewModelBase。
/// </summary>
public partial class BaseRoleViewModel : ModularATViewModelBase
{
    private readonly IOperator _ioperator;
    private readonly IRoleServices _roleServices;
    private readonly IServiceProvider _serviceProvider;

    /// <summary>
    /// 初始化BaseRoleViewModel实例。
    /// </summary>
    /// <param name="serviceProvider">服务提供者。</param>
    /// <param name="navigation">导航服务。</param>
    /// <param name="roleServices">角色服务。</param>
    /// <param name="ioperator">操作员服务。</param>
    public BaseRoleViewModel(
        IServiceProvider serviceProvider,
        NavigationService navigation,
        IRoleServices roleServices,
        IOperator ioperator) : base(navigation)
    {
        _serviceProvider = serviceProvider;
        _roleServices = roleServices;
        _ioperator = ioperator;
    }

    /// <summary>
    /// 表示是否处于添加模式的属性。
    /// </summary>
    [ObservableProperty] public partial bool IsAdd { get; private set; }

    /// <summary>
    /// 当前选中的角色对象。
    /// </summary>
    [ObservableProperty] public partial Role CurrentRole { get; private set; }

    /// <summary>
    /// 角色数据集合。
    /// </summary>
    [ObservableProperty] public partial ObservableCollection<Role> Data { get; set; } = new();

    /// <summary>
    /// 加载数据命令。
    /// </summary>
    [RelayCommand]
    public async Task LoadData()
    {
        // 查询所有未删除的角色数据
        var data = await _roleServices.Query(r => r.IsDeleted != true);
        // 将查询结果转换为ObservableCollection<Role>并赋值给Data属性
        Data = new ObservableCollection<Role>(data.ToList());
    }

    /// <summary>
    /// 删除角色命令。
    /// </summary>
    /// <param name="id">要删除的角色ID。</param>
    [Log]
    [RelayCommand]
    public async Task<bool> Delete(int id)
    {
        // 根据ID查询角色详情
        var userDetail = await _roleServices.QueryById(id);
        // 设置角色为已删除状态
        userDetail.IsDeleted = true;
        // 更新角色数据并返回更新结果
        return await _roleServices.Update(userDetail);
    }

    /// <summary>
    /// 编辑角色命令。
    /// </summary>
    /// <param name="role">要编辑的角色对象。</param>
    [Log]
    [RelayCommand]
    public void Edit(Role role)
    {
        // 设置当前角色为传入的角色对象
        CurrentRole = role;
        // 设置为编辑模式
        IsAdd = false;
    }

    /// <summary>
    /// 添加角色命令。
    /// </summary>
    [Log]
    [RelayCommand]
    public void Add()
    {
        // 设置为添加模式
        IsAdd = true;
        // 创建一个新的角色对象
        CurrentRole = new Role();
    }

    /// <summary>
    /// 保存角色命令。
    /// </summary>
    [Log]
    [RelayCommand]
    public async Task<bool> Save()
    {
        // 获取当前编辑的角色对象
        var role = CurrentRole;
        if (IsAdd)
        {
            // 设置创建者ID和名称
            role.CreateId = _ioperator.UserId;
            role.CreateBy = _ioperator.UserName;
            // 添加角色并返回添加结果
            var id = await _roleServices.Add(role);
            return id > 0;
        }

        // 设置修改者ID、名称和时间
        role.ModifyId = _ioperator.UserId;
        role.ModifyBy = _ioperator.UserName;
        role.ModifyTime = DateTime.Now;
        // 更新角色数据并返回更新结果
        return await _roleServices.Update(role);
    }

    /// <summary>
    /// 取消编辑命令。
    /// </summary>
    [RelayCommand]
    public void Cancel()
    {
        // 重新加载数据
        LoadData();
    }

    /// <summary>
    /// 搜索角色命令。
    /// </summary>
    /// <param name="key">搜索关键字。</param>
    [RelayCommand]
    public async Task Search(string key)
    {
        if (!string.IsNullOrEmpty(key))
        {
            // 根据关键字查询角色数据
            var data = await _roleServices.Query(r => r.Name.Contains(key) || r.Description.Contains(key));
            // 将查询结果转换为ObservableCollection<Role>并赋值给Data属性
            Data = new ObservableCollection<Role>(data.ToList());
        }
    }
}
