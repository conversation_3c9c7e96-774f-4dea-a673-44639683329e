using ModularAT.Localization.Resources;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ModularAT.Application.Interface;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Entity;
using ModularAT.Entity.BaseManage;
using ModularAT.Entitys;
using ModularAT.Repository.UnitOfWork;

namespace ModularAT.ViewModels;

public partial class BaseUserViewModel : ModularATViewModelBase
{
    private readonly IOperator _ioperator;
    private readonly IRoleServices _roleServices;
    private readonly IServiceProvider _serviceProvider;
    private readonly ISysUserInfoServices _sysUserInfoServices;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IUserRoleServices _userRoleServices;

    /// <summary>
    /// 初始化BaseUserViewModel实例。
    /// </summary>
    /// <param name="serviceProvider">服务提供者。</param>
    /// <param name="navigation">导航服务。</param>
    /// <param name="unitOfWork">工作单元。</param>
    /// <param name="sysUserInfoServices">用户信息服务。</param>
    /// <param name="userRoleServices">用户角色服务。</param>
    /// <param name="roleServices">角色服务。</param>
    /// <param name="ioperator">操作员服务。</param>
    public BaseUserViewModel(
        IServiceProvider serviceProvider,
        NavigationService navigation,
        IUnitOfWork unitOfWork,
        ISysUserInfoServices sysUserInfoServices,
        IUserRoleServices userRoleServices,
        IRoleServices roleServices,
        IOperator ioperator) : base(navigation)
    {
        _unitOfWork = unitOfWork;
        _serviceProvider = serviceProvider;
        _sysUserInfoServices = sysUserInfoServices;
        _userRoleServices = userRoleServices;
        _roleServices = roleServices;
        _ioperator = ioperator;
        // 加载数据
        LoadData().GetAwaiter();
    }

    /// <summary>
    /// 表示是否处于添加模式的属性。
    /// </summary>
    [ObservableProperty] public partial bool IsAdd { get; private set; }

    /// <summary>
    /// 当前选中的用户信息。
    /// </summary>
    [ObservableProperty] public partial sysUserInfo Current { get; private set; }

    /// <summary>
    /// 当前选中的角色ID。
    /// </summary>
    [ObservableProperty] public partial int CurrentRId { get; set; }

    /// <summary>
    /// 用户密码。
    /// </summary>
    [ObservableProperty] public partial string PassWord { get; set; }

    /// <summary>
    /// 表示是否更改密码的属性。
    /// </summary>
    [ObservableProperty] public partial bool IsChangePWD { get; set; }

    /// <summary>
    /// 角色数据集合。
    /// </summary>
    [ObservableProperty] public partial ObservableCollection<Role> Roles { get; set; } = new();

    /// <summary>
    /// 用户信息数据集合。
    /// </summary>
    [ObservableProperty] public partial ObservableCollection<sysUserInfo> Datas { get; set; } = new();

    /// <summary>
    /// 获取用户信息分页数据。
    /// </summary>
    /// <param name="page">页码，默认为1。</param>
    /// <param name="key">搜索关键字，默认为空字符串。</param>
    /// <returns>包含用户信息分页数据的MessageModel。</returns>
    public async Task<MessageModel<PageModel<sysUserInfo>>> Get(int page = 1, string key = "")
    {
        // 如果关键字为空或空白，则将其设置为空字符串
        if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key)) key = "";
        // 定义每页显示的记录数
        var intPageSize = 50;

        // 查询所有未删除且状态大于等于0的用户信息，根据登录名或真实姓名进行模糊搜索，并按用户ID降序排序
        //var data = await _sysUserInfoServices.QueryPage(a => !a.tdIsDelete && a.uStatus >= 0 && ((a.uLoginName != null && a.uLoginName.Contains(key)) || (a.uRealName != null && a.uRealName.Contains(key))), page, intPageSize, " uID desc ");

        // 获取当前操作员角色的最小排序值，并查询所有排序值大于等于该值的用户信息
        var data = new PageModel<sysUserInfo>
            { data = await _sysUserInfoServices.GetLowLevelUsers(_ioperator.Roles.FirstOrDefault().OrderSort) };

        #region MyRegion

        // 查询所有未删除的用户角色关系和角色信息
        var allUserRoles = await _userRoleServices.Query(d => d.IsDeleted == false);
        var allRoles = await _roleServices.Query(d => d.IsDeleted == false);

        // 获取用户信息数据
        var sysUserInfos = data.data;
        foreach (var item in sysUserInfos)
        {
            // 获取当前用户的角色ID列表
            var currentUserRoles = allUserRoles.Where(d => d.UserId == item.uID).Select(d => d.RoleId).ToList();
            // 设置当前用户的角色ID列表
            item.RIDs = currentUserRoles;
            // 设置当前用户的角色名称列表
            item.RoleNames = allRoles.Where(d => currentUserRoles.Contains(d.Id)).Select(d => d.Name).ToList();
        }

        // 更新用户信息数据
        data.data = sysUserInfos;

        #endregion

        // 返回包含用户信息分页数据的MessageModel
        return new MessageModel<PageModel<sysUserInfo>>
        {
            msg = Lang.BaseUserViewModel_Get_success,
            success = data.dataCount >= 0,
            response = data
        };
    }

    /// <summary>
    /// 添加一个用户。
    /// </summary>
    /// <param name="sysUserInfo">要添加的用户信息。</param>
    /// <returns>包含添加结果的MessageModel。</returns>
    [Log]
    public async Task<MessageModel<string>> Post(sysUserInfo sysUserInfo)
    {
        var data = new MessageModel<string>();

        // 将用户密码转换为MD5字符串
        sysUserInfo.uLoginPWD = sysUserInfo.uLoginPWD.ToMD5String();
        // 设置用户备注为当前操作员的用户名
        sysUserInfo.uRemark = _ioperator.UserName;

        // 添加用户信息并获取添加后的用户ID
        var id = await _sysUserInfoServices.Add(sysUserInfo);
        // 设置添加结果的成功标志
        data.success = id > 0;
        if (data.success)
        {
            // 设置添加结果的响应数据
            data.response = id.ObjToString();
            // 设置添加结果的消息
            data.msg = Lang.BaseUserViewModel_Add_success;
        }

        // 返回包含添加结果的MessageModel
        return data;
    }

        /// <summary>
    /// 更新用户信息和角色
    /// </summary>
    /// <param name="sysUserInfo">要更新的用户信息</param>
    /// <returns>包含更新结果的MessageModel</returns>
    [Log]
    public async Task<MessageModel<string>> Put(sysUserInfo sysUserInfo)
    {
        // 开启事务
        _unitOfWork.BeginTran();

        var data = new MessageModel<string>();
        try
        {
            // 检查用户信息是否为空且用户ID大于0
            if (sysUserInfo != null && sysUserInfo.uID > 0)
            {
                // 检查用户角色ID列表是否有元素
                if (sysUserInfo.RIDs.Count > 0)
                {
                    // 查询当前用户的所有用户角色关系
                    var usreroles = (await _userRoleServices.Query(d => d.UserId == sysUserInfo.uID))
                        .Select(d => d.Id.ToString()).ToArray();
                    // 如果存在用户角色关系，则删除这些关系
                    if (usreroles.Count() > 0)
                    {
                        var isAllDeleted = await _userRoleServices.DeleteByIds(usreroles);
                    }

                    // 创建新的用户角色关系列表
                    var userRolsAdd = new List<UserRole>();
                    // 遍历用户角色ID列表，为每个角色ID创建一个新的用户角色关系
                    sysUserInfo.RIDs.ForEach(rid => { userRolsAdd.Add(new UserRole(sysUserInfo.uID, rid)); });

                    // 添加新的用户角色关系
                    await _userRoleServices.Add(userRolsAdd);
                }

                // 更新用户信息
                data.success = await _sysUserInfoServices.Update(sysUserInfo);

                // 提交事务
                _unitOfWork.CommitTran();

                if (data.success)
                {
                    // 设置成功消息
                    data.msg = Lang.BaseUserViewModel_Update_success;
                    // 设置响应数据为用户ID的字符串形式
                    data.response = sysUserInfo?.uID.ObjToString();
                }
            }
        }
        catch (Exception e)
        {
            // 回滚事务
            _unitOfWork.RollbackTran();
            // 记录错误日志
            //_logger.LogError(e, e.Message);
        }

        // 返回包含更新结果的MessageModel
        return data;
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">要删除的用户ID</param>
    /// <returns>包含删除结果的MessageModel</returns>
    [Log]
    public async Task<MessageModel<string>> Delete(int id)
    {
        var data = new MessageModel<string>();
        // 检查用户ID是否大于0
        if (id > 0)
        {
            // 根据用户ID查询用户详细信息
            var userDetail = await _sysUserInfoServices.QueryById(id);
            // 设置用户为已删除状态
            userDetail.tdIsDelete = true;
            // 更新用户信息并获取更新结果
            data.success = await _sysUserInfoServices.Update(userDetail);
            if (data.success)
            {
                // 设置成功消息
                data.msg = Lang.BaseUserViewModel_Delete_success;
                // 设置响应数据为用户ID的字符串形式
                data.response = userDetail?.uID.ObjToString();
            }
        }

        // 返回包含删除结果的MessageModel
        return data;
    }

    /// <summary>
    /// 添加用户
    /// </summary>
    /// <param name="loginName">登录名</param>
    /// <param name="loginPwd">登录密码</param>
    /// <returns>包含添加结果的MessageModel</returns>
    public async Task<MessageModel<sysUserInfo>> AddUser(string loginName, string loginPwd)
    {
        // 返回包含添加结果的MessageModel
        return new MessageModel<sysUserInfo>
        {
            // 设置成功标志为true
            success = true,
            // 设置成功消息
            msg = Lang.BaseUserViewModel_Add_success,
            // 调用SaveUserInfo方法添加用户并获取添加后的用户信息
            response = await _sysUserInfoServices.SaveUserInfo(loginName, loginPwd)
        };
    }

    /// <summary>
    /// 加载数据命令
    /// </summary>
    [RelayCommand]
    public async Task LoadData()
    {
        // 调用Get方法获取用户信息数据
        var data = await Get();
        // 将赋值给Datas属性
        Datas = new ObservableCollection<sysUserInfo>(data.response.data);
        // 查询所有排序值大于等于当前操作员角色数据
        Roles = new ObservableCollection<Role>(await _roleServices.Query(r =>
            r.OrderSort >= _ioperator.Roles.FirstOrDefault().OrderSort)); 
        // 将获取到的用户信息数据中的第一个元素赋值给Current属性
        Current = data.response.data.FirstOrDefault();
        // 将当前用户的角色ID列表中的第一个元素赋值给CurrentRId属性
        CurrentRId = Current.RIDs.FirstOrDefault();
    }


    [RelayCommand]
    public async Task<bool> DeleteOne(int id)
    {
        return Delete(id).Result.success;
    }

    [Log]
    [RelayCommand]
    public void Edit(sysUserInfo data)
    {
        Current = data;
        CurrentRId = Current.RIDs.FirstOrDefault();
        IsAdd = false;
        IsChangePWD = false;
    }

    [RelayCommand]
    public void Add()
    {
        IsAdd = true;
        Current = new sysUserInfo();
        IsChangePWD = true;
    }

     /// <summary>
    /// 保存用户信息的命令
    /// </summary>
    [RelayCommand]
    public async Task<bool> Save()
    {
        // 获取当前编辑的用户信息
        var role = Current;
        if (IsAdd)
        {
            // 如果处于添加模式，则调用Post方法添加用户，并将返回的ID赋值给当前用户的uID属性
            var result = Post(Current);
            var uid = result.Result.response.ObjToInt();
            Current.uID = uid;
        }

        // 如果当前用户的角色ID列表为空，则将当前选中的角色ID添加到列表中
        if (Current.RIDs is null)
            Current.RIDs = [CurrentRId];
        // 如果当前用户的角色ID列表为空，则将当前选中的角色ID添加到列表中
        else if (Current.RIDs.Count == 0)
            Current.RIDs.Add(CurrentRId);
        // 如果当前用户的角色ID列表不为空，则将当前选中的角色ID替换列表中的第一个元素
        else
            Current.RIDs[0] = CurrentRId;

        // 如果需要更改密码，则将密码转换为MD5字符串并赋值给当前用户的uLoginPWD属性
        Current.uLoginPWD = IsChangePWD ? PassWord.ToMD5String() : Current.uLoginPWD;
        // 调用Put方法更新当前用户信息，并返回更新结果的success属性
        return Put(Current).Result.success;
    }


    [RelayCommand]
    public void Cancel()
    {
        LoadData();
    }


    [RelayCommand]
    public async Task Search(string key)
    {
        if (!string.IsNullOrEmpty(key))
        {
            var data = await Get(key: key);
            Datas = new ObservableCollection<sysUserInfo>(data.response.data);
        }
    }
}