using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ModularAT.Application.Interface;
using ModularAT.Entity.BaseManage;

namespace ModularAT.ViewModels;

public partial class OperateLogViewModel : ModularATViewModelBase

{
    private readonly IOperateLogServices _logServices;
    private readonly IServiceProvider _serviceProvider;

    public OperateLogViewModel(
        IServiceProvider serviceProvider,
        NavigationService navigation,
        IOperateLogServices logServices) : base(navigation)
    {
        _serviceProvider = serviceProvider;
        _logServices = logServices;
        EndTime = GetLimit(false);
        StartTime = DateTime.Now.AddDays(-7) > GetLimit() ? DateTime.Now.AddDays(-7) : GetLimit();
    }

    [ObservableProperty] public partial DateTime StartTime { get; set; }

    [ObservableProperty] public partial DateTime EndTime { get; set; }

    [ObservableProperty] public partial OperateLog Current { get; private set; }

    [ObservableProperty] public partial ObservableCollection<OperateLog> Data { get; set; } = new();

    [RelayCommand]
    public async Task LoadData()
    {
        var data = await _logServices.Query(r => r.LogTime >= StartTime && r.LogTime <= EndTime);
        Data = new ObservableCollection<OperateLog>(data.ToList());
    }

    [RelayCommand]
    public void Edit(OperateLog current)
    {
        Current = current;
    }

    [RelayCommand]
    public void Cancel()
    {
        LoadData().GetAwaiter();
    }

    [RelayCommand]
    public async Task Search(string key)
    {
        if (!string.IsNullOrEmpty(key))
        {
            var data = await _logServices.Query(r =>
                r.Area.Contains(key) || r.Controller.Contains(key) || r.Action.Contains(key));
            Data = new ObservableCollection<OperateLog>(data.ToList());
        }
    }

    public DateTime GetLimit(bool min = true)
    {
        try
        {
            return (DateTime)_logServices.GetLimit(min).GetAwaiter().GetResult().LogTime;
        }
        catch (Exception)
        {
            return DateTime.Now;
        }
    }
}