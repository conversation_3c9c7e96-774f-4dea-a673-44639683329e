using ModularAT.Localization.Resources;
﻿using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Driver.Controller;
using ModularAT.Entity.Controller;
using ModularAT.Entity.Enum;
using ModularAT.Service.Setting;

namespace ModularAT.ViewModels;

public partial class ControlerAxisViewModel : ModularATViewModelBase,
    IRecipient<ValueChangedMessage<IEnumerable<AxisFeedBackModel>>>
{
    #region Constructor

    public ControlerAxisViewModel(NavigationService navigationService) : base(navigationService)
    {
        IsActive = true;
        AxisCtrlCmdModel.AxisCtrl = AxisCtrlDic.First().Value;
    }

    #endregion

    #region Variables

    public ControlerDebugViewModel DebugViewModel => ViewModelLocator.ControlerDebug;

    private readonly ControlerTcpClient client = App.GetService<ControlerTcpClient>()!;

    #endregion

    #region Properties

    [ObservableProperty] private AxisCtrlCmdModel axisCtrlCmdModel = new(); //用于绑定界面的唯一模型，后映射到package对象

    /// <summary>
    ///     反馈列表数据源
    /// </summary>
    public ObservableCollection<AxisFeedBackModel> AxisFeedBacks { get; set; } = new();

    [ObservableProperty] private string sendMsgText;

    [ObservableProperty] private string receivedMsgText;

    [ObservableProperty] private Dictionary<string, ushort> axisCtrlDic = new()
    {
        { Lang.ControlerAxisViewModel_Jog_forward, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_JOGFOR },
        { Lang.ControlerAxisViewModel_Jog_reverse, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_JOGBACK },
        //{ Lang.ControlerAxisViewModel_Absolute_movement, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVEABS },
        //{ Lang.ControlerAxisViewModel_Relative_movement,  (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVEREL },
        //{ Lang.ControlerAxisViewModel_Workstation_movement,  (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVESTATION },
        { Lang.ControlerAxisViewModel_Set_zero_point, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_SETZERO }
    };

    [ObservableProperty] private bool setPosVisible;

    [ObservableProperty] private bool setStationVisible;

    [ObservableProperty] private bool setLineVisible;

    #endregion

    #region Methods

    public void Receive(ValueChangedMessage<IEnumerable<AxisFeedBackModel>> message)
    {
        var data = message.Value;
        App.Current?.Dispatcher.BeginInvoke(() =>
        {
            for (var i = 0; i < data.Count(); i++)
            {
                if (i < AxisFeedBacks.Count) // 确保索引不会越界
                    AxisFeedBacks[i] = data.ElementAt(i); // 赋值
                else
                    AxisFeedBacks.Add(data.ElementAt(i));
                AxisFeedBacks[i].IAxisID = i;
            }
        });
    }

    private void SendMsg()
    {
        client.Send(SendMsgText);
    }

    [RelayCommand]
    private void ClearReceivedMsg()
    {
        ReceivedMsgText = string.Empty;
    }

    [Log]
    [RelayCommand]
    private void Exec()
    {
        var package = AxisCtrlCmdModel.MapTo<AxisCtrlCmdPackage>();
        package.AxisTargetStationID--; //因为model里取的值是下拉框的索引，实际绑定值是从-1开始的，所以要减1
        client.Send(package);
    }

    [RelayCommand]
    private void Read() //读取轴反馈
    {
        AxisFeedBacks.Clear();
    }

    [Log]
    [RelayCommand]
    private void Stop()
    {
        var package = AxisCtrlCmdModel.MapTo<AxisCtrlCmdPackage>();
        package.AxisTargetStationID--;
        package.AxisCtrl = (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_STOP;
        client.Send(package);
    }

    [RelayCommand]
    private void SetRunType()
    {
        switch (AxisCtrlCmdModel.AxisRunMode)
        {
            case 0:
                AxisCtrlDic = new Dictionary<string, ushort>
                {
                    { Lang.ControlerAxisViewModel_Jog_forward, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_JOGFOR },
                    { Lang.ControlerAxisViewModel_Jog_reverse, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_JOGBACK },
                    //{ Lang.ControlerAxisViewModel_Absolute_movement, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVEABS },
                    //{ Lang.ControlerAxisViewModel_Relative_movement,  (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVEREL },
                    //{ Lang.ControlerAxisViewModel_Workstation_movement,  (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVESTATION },
                    { Lang.ControlerAxisViewModel_Set_zero_point, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_SETZERO }
                };
                break;
            case 1:
                AxisCtrlDic = new Dictionary<string, ushort>
                {
                    { Lang.ControlerAxisViewModel_Absolute_movement, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVEABS },
                    { Lang.ControlerAxisViewModel_Axis_reset, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_RESET },
                    { Lang.ControlerAxisViewModel_Set_zero_point, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_SETZERO }
                };
                break;
            case 2:
                AxisCtrlDic = new Dictionary<string, ushort>
                {
                    { Lang.ControlerAxisViewModel_Relative_movement, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVEREL },
                    { Lang.ControlerAxisViewModel_Axis_reset, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_RESET },
                    { Lang.ControlerAxisViewModel_Set_zero_point, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_SETZERO }
                };
                break;
            case 3:
                AxisCtrlDic = new Dictionary<string, ushort>
                {
                    { Lang.ControlerAxisViewModel_Workstation_movement, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_MOVESTATION },
                    { Lang.ControlerAxisViewModel_Axis_reset, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_RESET },
                    { Lang.ControlerAxisViewModel_Set_zero_point, (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_SETZERO }
                };
                break;
        }

        //设置默认选择值
        AxisCtrlCmdModel.AxisCtrl = AxisCtrlDic.First().Value;
        UpdateVisiable();
    }

    private void UpdateVisiable()
    {
        SetStationVisible = AxisCtrlCmdModel.AxisRunMode == 3;
        SetPosVisible = AxisCtrlCmdModel.AxisRunMode is 1 or 2;
        SetLineVisible = AxisCtrlCmdModel.AxisRunMode != 2;
    }

    #endregion
}