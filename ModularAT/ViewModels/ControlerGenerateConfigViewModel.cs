using ModularAT.Localization.Resources;
﻿using CommunityToolkit.Mvvm.Input;
using ModularAT.Common.Attributes;
using ModularAT.Common.Log;
using ModularAT.Entity.Enum;
using ModularAT.Entity.OnlineConfig;
using ModularAT.Service.Setting;

namespace ModularAT.ViewModels;

public partial class ControlerGenerateConfigViewModel : ModularATViewModelBase
{
    #region Constructor

    public ControlerGenerateConfigViewModel(NavigationService navigationService) : base(navigationService)
    {
    }

    #endregion

    #region Variables

    public ConfigNumModel ConfigNum { get; set; } = new();

    #endregion

    #region Functions


    private void LoadConfigNums()
    {
        // ConfigNum
    }

    [Log]
    [RelayCommand]
    private async Task GenerateConfig()
    {
        await Task.Run(() =>
        {
            var configData = new Dictionary<LineConfigEnum, (Type dtoType, int count)>
            {
                //{ LineConfigName.SysCfgPara, (typeof(Online_SysCfgDto), ConfigNum.SystemCount) },
                { LineConfigEnum.MotorCfgPara, (typeof(Online_MotorCfgDto), ConfigNum.MotorCount) },
                { LineConfigEnum.SlaveNodeCfgPara, (typeof(Online_SlaveNodeCfgDto), ConfigNum.SlaveNodeCount) },
                { LineConfigEnum.LineBodyCfgPara, (typeof(Online_LineBodyCfgDto), ConfigNum.LineSegmentCount) },
                { LineConfigEnum.StationCfgPara, (typeof(Online_StationCfgDto), ConfigNum.StationCount) },
                //{ LineConfigName.StationOffsetCfgPara, (typeof(Online_AxisOffsetCfgDto), ConfigNum.StationCount) },
                { LineConfigEnum.AxisCfgPara, (typeof(Online_MoverCfgDto), ConfigNum.MoverCount) },
                { LineConfigEnum.AxisOffsetCfgPara, (typeof(Online_AxisOffsetCfgDto), ConfigNum.MoverCount) },
                { LineConfigEnum.PIDCfgPara, (typeof(Online_PIDCfgDto), ConfigNum.MoverCount) },
                { LineConfigEnum.UIViewLineCfgPara , (typeof(Online_ViewLineConfig), 1)}
            };
            //根据设定数量新建配置文件
            foreach (var item in configData)
            {
                var list = new List<object>();
                for (var i = 0; i < item.Value.count; i++)
                {
                    // 修正构造器判断逻辑：存在无参构造器时使用无参构造，否则使用带索引参数的构造
                    list.Add(item.Value.dtoType.GetConstructor(Type.EmptyTypes) != null
                        ? Activator.CreateInstance(item.Value.dtoType)
                        : Activator.CreateInstance(item.Value.dtoType, i));
                }
                OnlineConfigService.WriteXmlData(item.Key, list);
            }

            //系统配置单独创建
            OnlineConfigService.WriteXmlData(LineConfigEnum.SysCfgPara, [
                new Online_SysCfgDto()
                {
                    ISysDrvMotorNums = (short)ConfigNum.MotorCount, ISysSlaveNodeNums = (short)ConfigNum.SlaveNodeCount,
                    // ISysLineBodySegNums = (short)ConfigNum.LineSegmentCount,
                    // ISysStationNums = (short)ConfigNum.StationCount, ISysMoverNums = (short)ConfigNum.MoverCount,
                    // ISysIONums = (short)ConfigNum.IoCount`
                }
            ]);
        });
        MsgToUiHelper.SendMsgInfo(Lang.ControlerGenerateConfigViewModel_Config_file_generated);
    }

    #endregion
}