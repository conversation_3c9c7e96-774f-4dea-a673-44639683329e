using System.ComponentModel;
using ModularAT.Localization.Resources;
using System.Drawing;
using System.Windows.Forms;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using log4net.Core;
using MahApps.Metro.Controls.Dialogs;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common.Attributes;
using ModularAT.Common.Helper;
using ModularAT.Common.Log;
using ModularAT.Controls;
using ModularAT.Driver.Controller;
using ModularAT.Driver.Servo;
using ModularAT.Entitys;
using ModularAT.Localization;
using ModularAT.Service.Setting;
using UserControl = System.Windows.Controls.UserControl;

namespace ModularAT.ViewModels;

public partial class MainViewModel : ModularATViewModelBase
{
    #region Fields

    private readonly IOperator _operator;
    private readonly ControlerTcpClient client = App.Current.Services.GetRequiredService<ControlerTcpClient>();

    [ObservableProperty] private partial ModularATViewModelBase? CurrentViewModel { get; set; }

    /// <summary>
    ///     程序左上角标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    ///     下拉框轴序号
    /// </summary>
    public int[] AxisNums { get; set; } = [1, 2];

    #endregion

    #region 顶部按钮状态

    [ObservableProperty] private bool isManual;

    [ObservableProperty] private bool isInitialize;

    [ObservableProperty] private bool isResetting;

    [ObservableProperty] private bool isControlPower;

    [ObservableProperty] private bool isEmergency;

    [ObservableProperty] private bool isRunning;

    [ObservableProperty] private bool isStop;

    #endregion

    #region 安灯状态

    [ObservableProperty] private bool isNormal;

    [ObservableProperty] private bool isError;

    [ObservableProperty] private bool isMaintain;

    #endregion

    #region 备用

    [ObservableProperty] private bool isDisableStation;

    [ObservableProperty] private bool isEnableStation;

    [ObservableProperty] private bool isAxisResetting;

    [ObservableProperty] private bool isInitializeAxisArray;

    [ObservableProperty] private bool isInitializeSystemMove;

    [ObservableProperty] private bool isRestartting;

    [ObservableProperty] private bool isSaving;

    [ObservableProperty] private bool isServo;

    [ObservableProperty] private Color msgColor; //消息颜色

    [ObservableProperty] private string msgReceived; //底部提示信息

    #endregion

    #region 侧边栏控制

    /// <summary>
    /// 侧边栏是否展开
    /// </summary>
    [ObservableProperty] private bool isSidebarExpanded = true;

    /// <summary>
    /// 右侧栏内容
    /// </summary>
    [ObservableProperty] private object rightSidebarContent;

    #endregion

    #region Constructor

    public MainViewModel(NavigationService navigationService, IOperator ioperator) : base(navigationService)
    {
        _operator = ioperator;
        navigation.CurrentViewModelChanged += () =>
        {
            CurrentViewModel = navigationService.CurrentViewModel;
            IsServo = CurrentViewModel is ServoSettingViewModel; //只有参数表页面显示轴选择
        };
        Title = $"IMTS-Studio - {AssemblyHelper.GetAssemblyVersion(typeof(App))} - {_operator.UserName}";
        InitializeMsgReceived();
        InitializeData();
        InitalizeDevices();
        InitializeNoticeList();
    }

    #endregion

    #region 上位机初始化

    private void InitializeMsgReceived()
    {
        MsgToUiHelper.MsgReceived += (msg, level) =>
        {
            MsgColor = level switch
            {
                _ when level == Level.Info => Color.Green,
                _ when level == Level.Warn => Color.Orange,
                _ when level == Level.Error => Color.Red,
                _ => Color.Black
            };
            MsgReceived = msg;
        };
    }

    private void InitializeNoticeList()
    {
        // 创建通知列表控件并设置为右侧栏内容
        var noticeListViewModel = App.Current.Services.GetRequiredService<NoticeListViewModel>();
        var noticeListControl = new NoticeListControl { DataContext = noticeListViewModel };
        RightSidebarContent = noticeListControl;
    }

    private void InitializeData()
    {
        var setService = App.Current.Services.GetService<ServoSettingService>();
        setService.LoadParameterFromFile(ServoContext.Instance.ParamItems);
    }

    /// <summary>
    ///     上位机初始化设备
    /// </summary>
    private void InitalizeDevices()
    {
        //ViewModelLocator.ControlerClient.ConnectPortCommand.Execute(null);//自动连接控制器
    }

    #endregion

    #region Command

    public List<LocalizationInfo> LocalizationInfos { get; set; } = LocalizationManager.List;

    [RelayCommand]
    private void ChangeLanguage(string language)
    {
        LocalizationManager.GetInstance().Change(language);
        App.GetService<LoginViewModel>()!.SaveCulture(language);
    }

    /// <summary>
    ///     使能
    /// </summary>
    /// <returns></returns>
    [Log]
    [RelayCommand]
    private async Task PowerOnOff()
    {
        SysCtrlCmdPackage package = new();
        package.ISysRunMode = 0;
        package.ISysAutoRunMode = 0;
        package.ISysRunVelRatio = 0;
        package.IDrvID = -1;
        package.UiSysCtrl = (ushort)(IsControlPower ? SysCtrlCmdEnum.EnableAbove : SysCtrlCmdEnum.EnableBelow);
        client.Send(package);
    }

    /// <summary>
    ///     复位
    /// </summary>
    [Log]
    [RelayCommand]
    private async Task RestControl()
    {
        //可能没收到，多发几次
        for (var i = 0; i < 3; i++)
        {
            var facllityStatusCtl
                = new FacilityStatusCtl
                {
                    Manual = IsManual,
                    Run = IsRunning,
                    Reset = IsResetting,
                    Initialize = IsInitialize,
                    EmergencyStop = false,
                    Stop = IsStop
                };
            client.SetFacllityState(facllityStatusCtl
            );
            await Task.Delay(500);
        }
    }

    //设备状态切换
    [RelayCommand]
    [Log]
    private async Task ChangeAutoMode()
    {
        var facilityStatusCtl
            = new FacilityStatusCtl
            {
                Manual = false,
                Run = IsRunning,
                Reset = IsResetting,
                Initialize = IsInitialize,
                EmergencyStop = IsEmergency,
                Stop = IsStop
            };
        client.SetFacllityState(facilityStatusCtl
        );
        await Task.Delay(500);
    }

    [RelayCommand]
    [Log]
    private async Task ChangeManualMode()
    {
        var facilityStatusCtl
            = new FacilityStatusCtl
            {
                Manual = true,
                Run = IsRunning,
                Reset = IsResetting,
                Initialize = IsInitialize,
                EmergencyStop = IsEmergency,
                Stop = IsStop
            };
        client.SetFacllityState(facilityStatusCtl
        );
        await Task.Delay(500);
    }

    /// <summary>
    ///     整个磁浮线系统初始化
    /// </summary>
    [Log]
    [RelayCommand]
    private async Task InitalizeSystem()
    {
        var facllityStatusCtl
            = new FacilityStatusCtl
            {
                Manual = IsManual,
                Run = IsRunning,
                Reset = IsResetting,
                Initialize = true,
                EmergencyStop = IsEmergency,
                Stop = IsStop
            };
        // for (int i = 0; i < 4; i++) //发4次
        // {
        client.SetFacllityState(facllityStatusCtl);
        await Task.Delay(300);
        // }
    }


    /// <summary>
    ///     开始运行
    /// </summary>
    [Log]
    [RelayCommand]
    private async Task StartControl()
    {
        var facilityStatusCtl
            = new FacilityStatusCtl
            {
                Manual = IsManual,
                Run = true,
                Reset = IsResetting,
                Initialize = IsInitialize,
                EmergencyStop = IsEmergency,
                Stop = false
            };
        client.SetFacllityState(facilityStatusCtl
        );
        await Task.Delay(500);
    }

    [Log]
    [RelayCommand]
    private async Task StopControl()
    {
        var facilityStatusCtl
            = new FacilityStatusCtl
            {
                Manual = IsManual,
                Run = false,
                Reset = IsResetting,
                Initialize = IsInitialize,
                EmergencyStop = IsEmergency,
                Stop = true
            };
        client.SetFacllityState(facilityStatusCtl
        );
        await Task.Delay(500);
    }


    [Log]
    [RelayCommand]
    private async Task EmergencyStop()
    {
        var facllityStatusCtl
            = new FacilityStatusCtl
            {
                Manual = IsManual,
                Run = false,
                Reset = false,
                Initialize = IsInitialize,
                EmergencyStop = true,
                Stop = IsStop
            };
        client.SetFacllityState(facllityStatusCtl
        );
        await Task.Delay(500);
    }

    [Log]
    [RelayCommand]
    private async Task AxisErrorRest()
    {
        if (ViewModelLocator.ControlerAxis.AxisFeedBacks.Count > 0)
            foreach (var item in ViewModelLocator.ControlerAxis.AxisFeedBacks)
            {
                //以下数据来之元磁上位机反编译后得到的结果，具体问测试人员
                AxisCtrlCmdPackage package = new()
                {
                    AxisID = (short)item.IAxisID,
                    AxisType = 0,
                    AxisTargetObjectID = Convert.ToInt16(0),
                    AxisTargetStationID = Convert.ToInt16(-1),
                    AxisRunMode = Convert.ToInt16(0),
                    VelMode = Convert.ToInt16(1),
                    AxisSetVel = Convert.ToDouble(200),
                    AxisSetAcc = Convert.ToDouble(5000),
                    AxisSetDec = Convert.ToDouble(5000),
                    AxisSetJerk = Convert.ToDouble(120000),
                    AxisTarPos = Convert.ToDouble(0),
                    AxisCtrl = (ushort)AxisCtrlCmdEnum.AXIS_CTRLCMD_RESET
                };
                client.Send(package);
            }
        else
            MsgToUiHelper.SendMsgWarn(Lang.MainViewModel_Controller_feedback_zero);

        await Task.Delay(500);
        IsAxisResetting = false;
    }


    [Log]
    [RelayCommand]
    private async Task RestartControlSystem()
    {
        client.Send((ushort)CmdTypeEnum.CMD_REQUEST_SYS_REBOOT);
        await Task.Delay(500);
        IsRestartting = false;
    }

    [Log]
    [RelayCommand]
    private async Task SaveControlData()
    {
        client.Send((ushort)CmdTypeEnum.CMD_REQUEST_CFG_SAVE);
        await Task.Delay(500);
        IsSaving = false;
    }

    [Log]
    [RelayCommand]
    private async Task InitializeSystemMove()
    {
        await Task.Delay(500);
        IsInitializeSystemMove = false;
    }

    [Log]
    [RelayCommand]
    private async Task InitializeAxisArray()
    {
        //axisArraytextFeedBack是控制器反馈得来
        var axisArraytextFeedBack = CmdTypeEnum.CMD_REQUEST_CFG_MOVERARRAYINFO_CFGFILE;
        //axisArraytextFeedBack= CmdTypeEnum.CMD_REQUEST_CFG_MOVERARRAYINFO_RESUMEFILE;

        var cmdType = (ushort)axisArraytextFeedBack;
        var data = client.BuildBytes(cmdType);
        client.Send((ushort)CmdTypeEnum.CMD_REQUEST_CFG_SAVE);

        await Task.Delay(500);
    }

    [Log]
    [RelayCommand]
    private async Task DisableStation()
    {
        await Task.Delay(500);
        IsDisableStation = false;
    }

    [Log]
    [RelayCommand]
    private async Task EnableStation()
    {
        await Task.Delay(500);
        IsEnableStation = false;
    }

    #endregion

    #region 导航

    [RelayCommand]
    private void SwitchPage(Type viewModelType)
    {
        if (viewModelType is null) return;
        // 使用反射来调用下面的泛型SwitchPage方法
        var method = GetType().GetMethod(nameof(SwitchPage));
        var generic = method.MakeGenericMethod(viewModelType);
        generic.Invoke(this, null);
    }

    public void SwitchPage<T>() where T : ModularATViewModelBase
    {
        Content = navigation.ResolveViewFor<T>();
        navigation.NavigateTo<T>();
    }

    private UserControl _content;

    public UserControl Content
    {
        get => _content;
        set
        {
            _content = value;
            OnPropertyChanged();
        }
    }

    #endregion
}