using ModularAT.Localization.Resources;
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ModularAT.Application.Interface;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Common.Helper;
using ModularAT.Entity;
using ModularAT.Entity.BaseManage;
using ModularAT.Entity.Policy;
using ModularAT.Entitys;

namespace ModularAT.ViewModels;

public partial class BasePermAssignViewModel : ModularATViewModelBase

{
    private readonly IOperator _ioperator;
    private readonly IModuleServices _moduleServices;
    private readonly IPermissionServices _permissionServices;
    private readonly PermissionRequirement _requirement;
    private readonly IRoleModulePermissionServices _roleModulePermissionServices;
    private readonly IRoleServices _roleServices;
    private readonly IServiceProvider _serviceProvider;
    private readonly IUserRoleServices _userRoleServices;

    public BasePermAssignViewModel(
        IServiceProvider serviceProvider,
        NavigationService navigation,
        IPermissionServices permissionServices,
        IModuleServices moduleServices,
        IRoleModulePermissionServices roleModulePermissionServices,
        IUserRoleServices userRoleServices,
        IRoleServices roleServices,
        IOperator ioperator,
        PermissionRequirement requirement
    ) : base(navigation)
    {
        _serviceProvider = serviceProvider;
        _permissionServices = permissionServices;
        _moduleServices = moduleServices;
        _roleModulePermissionServices = roleModulePermissionServices;
        _userRoleServices = userRoleServices;
        _roleServices = roleServices;
        _ioperator = ioperator;
        _requirement = requirement;
        LoadData();
    }

    /// <summary>
    /// 当前选中的分配视图
    /// </summary>
    public AssignView Current { get; set; }

    /// <summary>
    /// 可观察的角色集合
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<Role> Roles { get; set; } = new();

    /// <summary>
    /// 可观察的权限树
    /// </summary>
    [ObservableProperty]
    public partial PermissionTree PermTree { get; set; } = new();



    /// <summary>
    ///     获取树状 Table
    /// </summary>
    /// <param name="f">父节点ID</param>
    /// <param name="key">搜索关键字</param>
    /// <returns>包含权限信息的消息模型</returns>
    public async Task<MessageModel<List<Permission>>> GetTreeTable(int f = 0, string key = "")
    {
        // 初始化权限列表
        List<Permission> permissions = new();
        // 查询所有未删除的模块
        var apiList = await _moduleServices.Query(d => d.IsDeleted == false);
        // 查询所有未删除的权限
        var permissionsList = await _permissionServices.Query(d => d.IsDeleted == false);
        // 如果关键字为空，则设置为空字符串
        if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key)) key = "";

        // 如果关键字不为空，则根据关键字筛选权限
        if (key != "")
            permissions = permissionsList.Where(a => a.Name.Contains(key)).OrderBy(a => a.OrderSort).ToList();
        else
            // 如果关键字为空，则根据父节点ID筛选权限
            permissions = permissionsList.Where(a => a.Pid == f).OrderBy(a => a.OrderSort).ToList();

        // 遍历权限列表
        foreach (var item in permissions)
        {
            // 初始化父节点ID列表
            var pidarr = new List<int>();
            // 获取当前权限的父节点
            var parent = permissionsList.FirstOrDefault(d => d.Id == item.Pid);

            // 循环获取父节点的父节点，直到根节点
            while (parent != null)
            {
                pidarr.Add(parent.Id);
                parent = permissionsList.FirstOrDefault(d => d.Id == parent.Pid);
            }

            // 将父节点ID列表反转，并在开头插入0
            pidarr.Reverse();
            pidarr.Insert(0, 0);
            // 设置当前权限的父节点ID列表
            item.PidArr = pidarr;

            // 设置当前权限的模块名称
            item.MName = apiList.FirstOrDefault(d => d.Id == item.Mid)?.LinkUrl;
            // 设置当前权限是否有子节点
            item.hasChildren = permissionsList.Where(d => d.Pid == item.Id).Any();
        }

        // 返回包含权限信息的消息模型
        return new MessageModel<List<Permission>>
        {
            msg = "Get tree table successfully",
            success = true,
            response = permissions
        };
    }


    /// <summary>
    ///     分配权限
    /// </summary>
    /// <param name="assignView">包含角色ID和权限ID列表的视图模型</param>
    /// <returns>包含操作结果的消息模型</returns>
    [Log]
    public async Task<MessageModel<string>> Assign(AssignView assignView)
    {
        // 创建一个新的消息模型实例，用于存储操作结果
        var data = new MessageModel<string>();

        // 检查分配视图中的角色ID是否大于0
        if (assignView.rid > 0)
        {
            // 设置操作成功标志为true
            data.success = true;

            // 查询与指定角色ID关联的所有角色模块权限
            var roleModulePermissions = await _roleModulePermissionServices.Query(d => d.RoleId == assignView.rid);

            // 筛选出需要删除的权限ID，即不在分配视图中的权限ID
            var remove = roleModulePermissions.Where(d => !assignView.pids.Contains(d.PermissionId.ObjToInt()))
                .Select(c => (object)c.Id);

            // 如果存在需要删除的权限ID，则调用删除方法并更新操作成功标志
            data.success &= remove.Any() ? await _roleModulePermissionServices.DeleteByIds(remove.ToArray()) : true;

            // 遍历分配视图中的每个权限ID
            foreach (var item in assignView.pids)
            {
                // 检查当前权限ID是否已经存在于角色模块权限中
                var rmpitem = roleModulePermissions.Where(d => d.PermissionId == item);

                // 如果当前权限ID不存在，则创建一个新的角色模块权限实例
                if (!rmpitem.Any())
                {
                    // 查询与当前权限ID关联的模块ID
                    var moduleid = (await _permissionServices.Query(p => p.Id == item)).FirstOrDefault()?.Mid;

                    // 创建一个新的角色模块权限实例
                    var roleModulePermission = new RoleModulePermission
                    {
                        IsDeleted = false,
                        RoleId = assignView.rid,
                        ModuleId = moduleid.ObjToInt(),
                        PermissionId = item
                    };

                    // 设置创建者ID和创建者名称
                    roleModulePermission.CreateId = _ioperator.UserId;
                    roleModulePermission.CreateBy = _ioperator.UserName;

                    // 调用添加方法并更新操作成功标志
                    data.success &= await _roleModulePermissionServices.Add(roleModulePermission) > 0;
                }
            }

            // 如果操作成功，则清除权限要求并设置响应消息
            if (data.success)
            {
                _requirement.Permissions.Clear();
                data.response = "";
                data.msg = "Assignment successful";
            }
        }

        // 返回操作结果消息模型
        return data;
    }


    /// <summary>
    ///     获取角色权限树
    /// </summary>
    /// <param name="pid">角色id</param>
    /// <param name="needbtn">是否需要按钮权限</param>
    /// <returns>包含权限树的消息模型</returns>
    public async Task<MessageModel<PermissionTree>> GetPermissionTree(int pid = 0, bool needbtn = false)
    {
        // 创建一个新的消息模型实例，用于存储操作结果
        var data = new MessageModel<PermissionTree>();
        // 获取当前操作员的角色等级
        var rLevel = _ioperator.Roles.FirstOrDefault().OrderSort;
        // 根据角色等级查询权限
        var permissions = rLevel == 0
            // 如果是开发者角色，查询所有未删除的权限
            ? await _permissionServices.Query(d => d.IsDeleted == false)
            // 如果是其他角色，根据角色等级查询低级别权限
            : await _permissionServices.QueryLowLevelPermissions(_ioperator.Roles.FirstOrDefault().OrderSort);
        // 将权限转换为权限树节点列表
        var permissionTrees = (from child in permissions
            where child.IsDeleted == false
            orderby child.Id
            select new PermissionTree
            {
                Value = child.Id,
                Label = child.Name,
                Pid = child.Pid,
                IsBtn = child.IsButton,
                Order = child.OrderSort
            }).ToList();
        // 创建根节点
        var rootRoot = new PermissionTree
        {
            Value = 0,
            Pid = 0,
            Label = Lang.BasePermAssignViewModel_Root_node
        };

        // 对权限树节点列表进行排序
        permissionTrees = permissionTrees.OrderBy(d => d.Order).ToList();

        // 递归构建权限树
        RecursionHelper.LoopToAppendChildren(permissionTrees, rootRoot, pid, needbtn);

        // 设置操作成功标志为true
        data.success = true;
        // 如果操作成功，则设置响应消息和权限树
        if (data.success)
        {
            data.response = rootRoot;
            data.msg = Lang.BasePermAssignViewModel_Get_success;
        }

        // 返回操作结果消息模型
        return data;
    }


    /// <summary>
    ///     获取导航栏
    /// </summary>
    /// <param name="uid">用户ID</param>
    /// <returns>包含导航栏信息的消息模型</returns>
    public async Task<MessageModel<NavigationBar>> GetNavigationBar(int uid)
    {
        // 创建一个新的消息模型实例，用于存储操作结果
        var data = new MessageModel<NavigationBar>();

        // 初始化一个变量，用于存储HTTP上下文中的用户ID
        var uidInHttpcontext1 = 0;
        // 查询与指定用户ID关联的所有未删除的用户角色，并获取角色ID列表
        var roleIds = (await _userRoleServices.Query(d => d.IsDeleted == false && d.UserId == uid))
            .Select(d => d.RoleId.ObjToInt()).Distinct().ToList();

        // 检查用户ID是否大于0且与HTTP上下文中的用户ID相同
        if (uid > 0 && uid == uidInHttpcontext1)
            // 检查角色ID列表是否不为空
            if (roleIds.Any())
            {
                // 查询与指定角色ID关联的所有未删除的角色模块权限，并获取权限ID列表
                var pids =
                    (await _roleModulePermissionServices.Query(d => d.IsDeleted == false && roleIds.Contains(d.RoleId)))
                    .Select(d => d.PermissionId.ObjToInt()).Distinct();
                // 检查权限ID列表是否不为空
                if (pids.Any())
                {
                    // 查询与指定权限ID关联的所有未删除的权限，并按排序顺序排序
                    var rolePermissionMoudles =
                        (await _permissionServices.Query(d => pids.Contains(d.Id))).OrderBy(c => c.OrderSort);
                    // 将权限转换为导航栏节点列表
                    var permissionTrees = (from child in rolePermissionMoudles
                        where child.IsDeleted == false
                        orderby child.Id
                        select new NavigationBar
                        {
                            id = child.Id,
                            name = child.Name,
                            pid = child.Pid,
                            order = child.OrderSort,
                            path = child.Code,
                            iconCls = child.Icon,
                            Func = child.Func,
                            IsHide = child.IsHide.ObjToBool(),
                            IsButton = child.IsButton.ObjToBool(),
                            meta = new NavigationBarMeta
                            {
                                requireAuth = true,
                                title = child.Name,
                                NoTabPage = child.IsHide.ObjToBool(),
                                keepAlive = child.IskeepAlive.ObjToBool()
                            }
                        }).ToList();

                    // 创建根节点
                    var rootRoot = new NavigationBar
                    {
                        id = 0,
                        pid = 0,
                        order = 0,
                        name = Lang.BasePermAssignViewModel_Root_node,
                        path = "",
                        iconCls = "",
                        meta = new NavigationBarMeta()
                    };

                    // 对导航栏节点列表进行排序
                    permissionTrees = permissionTrees.OrderBy(d => d.order).ToList();

                    // 递归构建导航栏
                    RecursionHelper.LoopNaviBarAppendChildren(permissionTrees, rootRoot);

                    // 设置操作成功标志为true
                    data.success = true;
                    // 如果操作成功，则设置响应消息和导航栏
                    if (data.success)
                    {
                        data.response = rootRoot;
                        data.msg = Lang.BasePermAssignViewModel_Get_success;
                    }
                }
            }

        // 返回操作结果消息模型
        return data;
    }


    /// <summary>
    ///     获取角色的所有权限Id
    /// </summary>
    /// <param name="rid">角色ID</param>
    /// <returns>包含权限ID和按钮权限名称的消息模型</returns>
    public async Task<MessageModel<AssignShow>> GetPermissionIdByRoleId(int rid = 0)
    {
        // 创建一个新的消息模型实例，用于存储操作结果
        var data = new MessageModel<AssignShow>();

        // 查询与指定角色ID关联的所有未删除的角色模块权限
        var rmps = await _roleModulePermissionServices.Query(d => d.IsDeleted == false && d.RoleId == rid);
        // 将角色模块权限转换为权限ID列表
        var permissionTrees = (from child in rmps
            orderby child.Id
            select child.PermissionId.ObjToInt()).ToList();

        // 查询所有未删除的权限
        var permissions = await _permissionServices.Query(d => d.IsDeleted == false);
        // 初始化一个列表，用于存储按钮权限名称
        List<string> assignbtns = new();

        // 遍历权限ID列表
        foreach (var item in permissionTrees)
        {
            // 获取当前权限ID对应的权限名称
            var pername = permissions.FirstOrDefault(d => d.IsButton && d.Id == item)?.Name;
            // 如果权限名称不为空，则将权限ID添加到按钮权限名称列表中
            if (!string.IsNullOrEmpty(pername))
                //assignbtns.Add(pername + "_" + item);
                assignbtns.Add(item.ObjToString());
        }

        // 设置操作成功标志为true
        data.success = true;
        // 如果操作成功，则设置响应消息和权限ID列表
        if (data.success)
        {
            data.response = new AssignShow
            {
                permissionids = permissionTrees,
                assignbtns = assignbtns
            };
            data.msg = Lang.BasePermAssignViewModel_Get_success;
        }

        // 返回操作结果消息模型
        return data;
    }


    [RelayCommand]
    public async void LoadData()
    {
        //加载角色列表，只加载当前角色以下的角色
        Roles = new ObservableCollection<Role>(await _roleServices.Query(r =>
            r.OrderSort > _ioperator.Roles.FirstOrDefault().OrderSort));
        //加载权限树
        PermTree = (await GetPermissionTree()).response;
    }


    /// <summary>
    ///     选择角色并加载其权限
    /// </summary>
    /// <param name="rid">角色ID</param>
    [RelayCommand]
    public async void SelectedRole(int rid)
    {
        // 获取指定角色的权限ID和按钮权限名称
        var assignShow = (await GetPermissionIdByRoleId(rid)).response;
        // 创建一个新的分配视图实例，设置权限ID列表和角色ID
        Current = new AssignView { pids = assignShow.permissionids, rid = rid };
        // 设置权限树中已选中的权限
        SetCheckedPid(PermTree);
    }


    [Log]
    [RelayCommand]
    public async Task<bool> Save()
    {
        Current.pids = GetEnabledPids(PermTree);
        return Assign(Current).GetAwaiter().GetResult().success;
    }

    /// <summary>
    /// 设置权限树中节点的选中状态
    /// </summary>
    /// <param name="root">权限树的根节点</param>
    private void SetCheckedPid(PermissionTree root)
    {
        // 如果当前分配视图为空，则直接返回
        if (Current is null) return;
        // 设置当前节点的选中状态，根据当前分配视图中的权限ID列表判断
        root.IsChecked = Current.pids.Contains(root.Value);
        // 如果当前节点有按钮权限子节点，则递归设置它们的选中状态
        if (root.Btns != null)
            foreach (var child in root.Btns)
                SetCheckedPid(child);

        // 如果当前节点有子节点，则递归设置它们的选中状态
        if (root.Children != null)
            foreach (var child in root.Children)
                SetCheckedPid(child);
    }


    /// <summary>
    /// 获取权限树中所有已启用的权限ID
    /// </summary>
    /// <param name="root">权限树的根节点</param>
    /// <returns>已启用的权限ID列表</returns>
    public List<int> GetEnabledPids(PermissionTree root)
    {
        // 初始化一个列表，用于存储已启用的权限ID
        var pids = new List<int>();

        // 如果当前节点已选中，则将其权限ID添加到列表中
        if (root.IsChecked) pids.Add(root.Value);

        // 如果当前节点有按钮权限子节点，则递归获取它们的已启用权限ID并添加到列表中
        if (root.Btns != null)
            foreach (var child in root.Btns)
                pids.AddRange(GetEnabledPids(child));

        // 如果当前节点有子节点，则递归获取它们的已启用权限ID并添加到列表中
        if (root.Children != null)
            foreach (var child in root.Children)
                pids.AddRange(GetEnabledPids(child));

        // 返回已启用的权限ID列表
        return pids;
    }


    /// <summary>
    /// 表示分配视图的类，包含权限ID列表和角色ID
    /// </summary>
    public class AssignView
    {
        /// <summary>
        /// 权限ID列表
        /// </summary>
        public List<int> pids { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public int rid { get; set; }
    }

    /// <summary>
    /// 表示分配展示的类，包含权限ID列表和按钮权限名称列表
    /// </summary>
    public class AssignShow
    {
        /// <summary>
        /// 权限ID列表
        /// </summary>
        public List<int> permissionids { get; set; }

        /// <summary>
        /// 按钮权限名称列表
        /// </summary>
        public List<string> assignbtns { get; set; }
    }

}