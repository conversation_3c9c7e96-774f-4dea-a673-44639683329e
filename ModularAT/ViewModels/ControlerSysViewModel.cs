using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common.Attributes;
using ModularAT.Driver.Controller;
using ModularAT.Entity.Controller;

namespace ModularAT.ViewModels;

public partial class ControlerSysViewModel : ModularATViewModelBase, IRecipient<ValueChangedMessage<SysFeedBackModel>>
{
    #region Variables

    private readonly ControlerTcpClient client = App.Current.Services.GetRequiredService<ControlerTcpClient>();

    #endregion

    #region Constructor

    public ControlerSysViewModel(NavigationService navigationService) : base(navigationService)
    {
        IsActive = true;
        //ISysRunModeSelected = SysCtrlCmdEnum.EnableBelow;
    }

    #endregion

    #region Properties

    public ControlerDebugViewModel DebugViewModel => ViewModelLocator.ControlerDebug;

    public ObservableCollection<SysFeedBackModel> SysFeedBacks { get; set; } = new();

    [ObservableProperty] private short iDrvIDSelected; //从站节点

    [ObservableProperty] private short iSysRunModeSelected;

    [ObservableProperty] private short iSysAutoRunModeSelected;

    [ObservableProperty] private SysCtrlCmdEnum uiSysCtrlSelected;

    [ObservableProperty] private short iCtrlObjSelected;

    [ObservableProperty] private short iSysRunVelRatio;

    #endregion

    #region Commands

    [RelayCommand]
    private void ReadSystemFeedBack() //读取系统反馈信息
    {
        SysFeedBacks.Clear();
    }

    [Log]
    [RelayCommand]
    private void Execute()
    {
        var package = new SysCtrlCmdPackage
        {
            IDrvID = (short)(IDrvIDSelected - 1),
            ISysRunMode = ISysRunModeSelected,
            ISysAutoRunMode = ISysAutoRunModeSelected,
            UiSysCtrl = (ushort)UiSysCtrlSelected,
            ICtrlObj = ICtrlObjSelected,
            ISysRunVelRatio = ISysRunVelRatio
        };
        client.Send(package);
    }

    public void Receive(ValueChangedMessage<SysFeedBackModel> message)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            var index = SysFeedBacks.IndexOf(message.Value);
            if (index == -1)
                SysFeedBacks.Add(message.Value);
            else
                SysFeedBacks[index] = message.Value;
        });
    }

    #endregion
}