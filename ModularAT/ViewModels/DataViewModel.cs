using ModularAT.Localization.Resources;
﻿using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using ModularAT.Application.Interface;
using ModularAT.Common.Log;
using ModularAT.Driver.Controller;
using ModularAT.Driver.Servo;
using ModularAT.Entity;
using ModularAT.Entity.BaseManage;
using ModularAT.Entity.Controller;
using ModularAT.Entitys;
using ModularAT.Mapper;
using static ModularAT.Common.Attributes.LogAttribute;

namespace ModularAT.ViewModels;

/// <summary>
///     ViewModel公用的数据类
///     初始化在MainViewModel之后
/// </summary>
public partial class DataViewModel : ObservableRecipient, IRecipient<ValueChangedMessage<LogRecord>>
{
    private readonly IOperator _ioperator;
    private readonly IOperateLogServices _operateLogServices;
    private readonly MainViewModel _main = ViewModelLocator.MainViewModel;

    [ObservableProperty] private bool isConnectedControler;

    [ObservableProperty] private bool isConnectedServo;

    public DataViewModel(IOperateLogServices operateLogServices, IOperator ioperator)
    {
        _operateLogServices = operateLogServices;
        _ioperator = ioperator;
        IsActive = true;
        ListenStatus();
    }

    /// <summary>
    ///     选择的轴
    /// </summary>
    public int SelectedAxis
    {
        get => ServoContext.Instance.AxisSelect;
        set => ServoContext.Instance.AxisSelect = value;
    }

    public BindingList<ParameterModel> ParamItems
    {
        get => ServoContext.Instance.ParamItems;
        set => ServoContext.Instance.ParamItems = value;
    }

    public UdiSysStateStruct SysStatus { get; set; }

    /// <summary>
    ///     记录操作日志到数据库
    /// </summary>
    /// <param name="message"></param>
    public void Receive(ValueChangedMessage<LogRecord> message)
    {
        _operateLogServices.Add(
            new OperateLog
            {
                Area = message.Value.Area,
                Controller = message.Value.Controller,
                Action = message.Value.Action,
                Description = message.Value.Description,
                LogTime = message.Value.LogTime,
                UserId = _ioperator.UserId,
                LoginName = _ioperator.UserName,
                IsDeleted = false
            });
    }

    /// <summary>
    ///     监听设备状态
    /// </summary>
    private void ListenStatus()
    {
        //驱动器连接状态
        WeakReferenceMessenger.Default.Register<ValueChangedMessage<bool>, string>(this, "ServoSerialPort",
            (_, message) => { IsConnectedServo = message.Value; });
        //控制器连接状态
        WeakReferenceMessenger.Default.Register<ValueChangedMessage<bool>, string>(this, "ControllerTcpClient",
            (_, message) =>
            {
                IsConnectedControler = message.Value;
                if (!IsConnectedControler) //断开连接后，所有按钮置灰
                {
                    _main.IsControlPower = false;
                    _main.IsRunning = false;
                    _main.IsStop = false;
                    _main.IsAxisResetting = false;
                    _main.IsEmergency = false;
                    _main.IsDisableStation = false;
                    _main.IsEnableStation = false;
                    _main.IsInitializeAxisArray = false;
                    _main.IsInitializeSystemMove = false;
                    _main.IsResetting = false;
                    _main.IsRestartting = false;
                    _main.IsSaving = false;
                    MsgToUiHelper.SendMsgError(Lang.DataViewModel_Controller_disconnected);
                }
                else
                {
                    MsgToUiHelper.SendMsgInfo(Lang.DataViewModel_Controller_connected);
                }
            });
        //设备状态反馈
        WeakReferenceMessenger.Default.Register<ValueChangedMessage<FacilityStatusFeedback>>(this,
            (_, message) => { GetFacilityStatus(message.Value); });
        //系统状态
        WeakReferenceMessenger.Default.Register<ValueChangedMessage<SysFeedBackModel>>(this,
            (_, message) => { SysStatus = SysFeedBackMapping.GetUdiSysStateBits(message.Value.UdiSysState); });
    }

    /// <summary>
    ///     获取安灯状态
    /// </summary>
    public void GetFacilityStatus(FacilityStatusFeedback facilityStatus)
    {
        //按钮状态
        _main.IsManual = facilityStatus.IsManual;
        _main.IsInitialize = facilityStatus.IsInitializing;
        _main.IsRunning = facilityStatus.IsRunning;
        _main.IsStop = facilityStatus.IsStop;
        _main.IsEmergency = facilityStatus.IsEmergencyStopping;
        _main.IsResetting = facilityStatus.IsResetting;
        //安灯状态
        _main.IsError = facilityStatus.IsError;
        _main.IsMaintain = facilityStatus.IsMaintenance;
        _main.IsNormal = facilityStatus.IsNormal;
    }
}