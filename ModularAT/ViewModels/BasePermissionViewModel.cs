using ModularAT.Localization.Resources;
using System.Collections.ObjectModel;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MiniExcelLibs;
using ModularAT.Application.Interface;
using ModularAT.Common.Attributes;
using ModularAT.Common.Extensions;
using ModularAT.Common.Helper;
using ModularAT.Entity;
using ModularAT.Entity.BaseManage;
using ModularAT.Entity.Policy;
using ModularAT.Entitys;

namespace ModularAT.ViewModels;

public partial class BasePermissionViewModel : ModularATViewModelBase

{
    private readonly IOperator _ioperator;
    private readonly IModuleServices _moduleServices;
    private readonly IPermissionServices _permissionServices;
    private readonly PermissionRequirement _requirement;
    private readonly IRoleModulePermissionServices _roleModulePermissionServices;
    private readonly IServiceProvider _serviceProvider;
    private readonly IUserRoleServices _userRoleServices;

    [ObservableProperty] private ObservableCollection<KeyValuePair<int, string>> permPairs = [];

    public BasePermissionViewModel(
        IServiceProvider serviceProvider,
        NavigationService navigation,
        IPermissionServices permissionServices,
        IModuleServices moduleServices,
        IRoleModulePermissionServices roleModulePermissionServices,
        IUserRoleServices userRoleServices,
        IOperator ioperator,
        PermissionRequirement requirement
    ) : base(navigation)
    {
        _serviceProvider = serviceProvider;
        _permissionServices = permissionServices;
        _moduleServices = moduleServices;
        _roleModulePermissionServices = roleModulePermissionServices;
        _userRoleServices = userRoleServices;
        _ioperator = ioperator;
        _requirement = requirement;
        LoadData().GetAwaiter();
    }

    /// <summary>
    /// 表示是否处于添加模式的属性
    /// </summary>
    [ObservableProperty]
    public partial bool IsAdd { get; private set; }

    /// <summary>
    /// 当前选中的权限对象
    /// </summary>
    [ObservableProperty]
    public partial Permission Current { get; set; }

    /// <summary>
    /// 角色集合
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<Role> Roles { get; set; } = new();

    /// <summary>
    /// 权限数据集合
    /// </summary>
    [ObservableProperty]
    public partial ObservableCollection<Permission> Datas { get; set; } = new();

    /// <summary>
    /// 获取权限分页数据
    /// </summary>
    /// <param name="page">页码，默认为1</param>
    /// <param name="key">搜索关键字，默认为空字符串</param>
    /// <returns>包含权限分页数据的MessageModel</returns>
    public async Task<MessageModel<PageModel<Permission>>> Get(int page = 1, string key = "")
    {
        // 创建一个新的PageModel<Permission>实例
        PageModel<Permission> permissions = new();
        // 定义每页显示的记录数
        var intPageSize = 50;
        // 如果关键字为空或空白，则将其设置为空字符串
        if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key)) key = "";

        // 调用_permissionServices的QueryPage方法查询权限数据
        permissions = await _permissionServices.QueryPage(
            a => a.IsDeleted != true && a.Name != null && a.Name.Contains(key), page, intPageSize, " Id desc ");

        #region 单独处理

        // 查询所有未删除的模块数据
        var apis = await _moduleServices.Query(d => d.IsDeleted == false);
        // 获取权限视图数据
        var permissionsView = permissions.data;

        // 查询所有未删除的权限数据
        var permissionAll = await _permissionServices.Query(d => d.IsDeleted != true);
        // 遍历权限视图数据
        foreach (var item in permissionsView)
        {
            // 创建一个新的List<int>实例，用于存储父级ID
            var pidarr = new List<int>
            {
                item.Pid
            };
            // 如果父级ID大于0，则将0添加到pidarr中
            if (item.Pid > 0) pidarr.Add(0);

            // 获取当前权限的父级权限
            var parent = permissionAll.FirstOrDefault(d => d.Id == item.Pid);

            // 循环查找父级权限，直到找到根节点
            while (parent != null)
            {
                // 将父级权限的ID添加到pidarr中
                pidarr.Add(parent.Id);
                // 获取父级权限的父级权限
                parent = permissionAll.FirstOrDefault(d => d.Id == parent.Pid);
            }

            // 将pidarr排序并去重后赋值给item.PidArr
            item.PidArr = pidarr.OrderBy(d => d).Distinct().ToList();
            // 遍历item.PidArr
            foreach (var pid in item.PidArr)
            {
                // 获取当前ID对应的权限
                var per = permissionAll.FirstOrDefault(d => d.Id == pid);
                // 将权限名称添加到item.PnameArr中，如果权限为空，则添加Lang.BasePermissionViewModel_Root_node
                item.PnameArr.Add((per != null ? per.Name : Lang.BasePermissionViewModel_Root_node) + "/");
                //var par = Permissions.Where(d => d.Pid == item.Id ).ToList();
                //item.PCodeArr.Add((per != null ? $"/{per.Code}/{item.Code}" : ""));
                //if (par.Count == 0 && item.Pid == 0)
                //{
                //    item.PCodeArr.Add($"/{item.Code}");
                //}
            }

            // 获取当前权限对应的模块链接地址
            item.MName = apis.FirstOrDefault(d => d.Id == item.Mid)?.LinkUrl;
        }

        // 将处理后的权限视图数据赋值给permissions.data
        permissions.data = permissionsView;

        #endregion

        // 返回包含权限分页数据的MessageModel
        return new MessageModel<PageModel<Permission>>
        {
            msg = Lang.BasePermissionViewModel_Get_success,
            success = permissions.dataCount >= 0,
            response = permissions
        };
    }


    /// <summary>
    ///     添加一个菜单
    /// </summary>
    /// <param name="permission"></param>
    /// <returns></returns>
    public async Task<MessageModel<string>> Post(Permission permission)
    {
        var data = new MessageModel<string>();

        permission.CreateId = _ioperator.UserId;
        permission.CreateBy = _ioperator.UserName;
        permission.IsDeleted = false;

        var id = await _permissionServices.Add(permission);
        data.success = id > 0;
        if (data.success)
        {
            data.response = id.ObjToString();
            data.msg = Lang.BasePermissionViewModel_Add_success;
        }

        return data;
    }


    /// <summary>
    ///     获取菜单树
    /// </summary>
    /// <param name="pid">父级ID，默认为0</param>
    /// <param name="level">层级，默认为1</param>
    /// <param name="needbtn">是否需要按钮，默认为false</param>
    /// <returns>包含权限树的MessageModel</returns>
    public async Task<MessageModel<PermissionTree>> GetPermissionTree(int pid = 0, int level = 1, bool needbtn = false)
    {
        var data = new MessageModel<PermissionTree>();

        // 查询所有未删除且层级为指定层级的权限数据
        var permissions =
            await _permissionServices.Query(d => d.IsDeleted == false && d.OrderSort == level);

        // 将查询结果转换为PermissionTree列表
        var permissionTrees = (from child in permissions
            where child.IsDeleted == false
            orderby child.Id
            select new PermissionTree
            {
                Value = child.Id,
                Label = child.Name,
                Pid = child.Pid,
                IsBtn = child.IsButton,
                Order = child.OrderSort
            }).ToList();

        // 创建根节点
        var rootRoot = new PermissionTree
        {
            Value = 0,
            Pid = 0,
            Label = Lang.BasePermissionViewModel_Root_node
        };

        // 对权限树列表进行排序
        permissionTrees = permissionTrees.OrderBy(d => d.Order).ToList();

        // 递归构建权限树
        RecursionHelper.LoopToAppendChildren(permissionTrees, rootRoot, pid, needbtn);

        // 设置返回结果的成功标志
        data.success = true;
        if (data.success)
        {
            // 设置返回结果的响应数据
            data.response = rootRoot;
            // 设置返回结果的消息
            data.msg = Lang.BasePermissionViewModel_Get_success;
        }

        // 返回包含权限树的MessageModel
        return data;
    }


    /// <summary>
    ///     获取路由树
    /// </summary>
    /// <param name="uid"></param>
    /// <returns></returns>
    public async Task<MessageModel<NavigationBar>> GetNavigationBar(int uid)
    {
        var data = new MessageModel<NavigationBar>();

        var uidInHttpcontext1 = 0;
        var roleIds = (await _userRoleServices.Query(d => d.IsDeleted == false && d.UserId == uid))
            .Select(d => d.RoleId.ObjToInt()).Distinct().ToList();

        if (uid > 0 && uid == uidInHttpcontext1)
            if (roleIds.Any())
            {
                var pids =
                    (await _roleModulePermissionServices.Query(d => d.IsDeleted == false && roleIds.Contains(d.RoleId)))
                    .Select(d => d.PermissionId.ObjToInt()).Distinct();
                if (pids.Any())
                {
                    var rolePermissionMoudles =
                        (await _permissionServices.Query(d => pids.Contains(d.Id))).OrderBy(c => c.OrderSort);
                    var permissionTrees = (from child in rolePermissionMoudles
                        where child.IsDeleted == false
                        orderby child.Id
                        select new NavigationBar
                        {
                            id = child.Id,
                            name = child.Name,
                            pid = child.Pid,
                            order = child.OrderSort,
                            path = child.Code,
                            iconCls = child.Icon,
                            Func = child.Func,
                            IsHide = child.IsHide.ObjToBool(),
                            IsButton = child.IsButton.ObjToBool(),
                            meta = new NavigationBarMeta
                            {
                                requireAuth = true,
                                title = child.Name,
                                NoTabPage = child.IsHide.ObjToBool(),
                                keepAlive = child.IskeepAlive.ObjToBool()
                            }
                        }).ToList();


                    var rootRoot = new NavigationBar
                    {
                        id = 0,
                        pid = 0,
                        order = 0,
                        name = Lang.BasePermissionViewModel_Root_node,
                        path = "",
                        iconCls = "",
                        meta = new NavigationBarMeta()
                    };

                    permissionTrees = permissionTrees.OrderBy(d => d.order).ToList();

                    RecursionHelper.LoopNaviBarAppendChildren(permissionTrees, rootRoot);

                    data.success = true;
                    if (data.success)
                    {
                        data.response = rootRoot;
                        data.msg = Lang.BasePermissionViewModel_Get_success;
                    }
                }
            }

        return data;
    }

    /// <summary>
    ///     更新菜单
    /// </summary>
    /// <param name="permission"></param>
    /// <returns></returns>
    [Log]
    public async Task<MessageModel<string>> Put(Permission permission)
    {
        var data = new MessageModel<string>();
        if (permission != null && permission.Id > 0)
        {
            data.success = await _permissionServices.Update(permission);
            await _roleModulePermissionServices.UpdateModuleId(permission.Id, permission.Mid);
            if (data.success)
            {
                data.msg = Lang.BasePermissionViewModel_Update_success;
                data.response = permission?.Id.ObjToString();
            }
        }

        return data;
    }

    /// <summary>
    ///     删除菜单
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [Log]
    public async Task<MessageModel<string>> Delete(int id)
    {
        var data = new MessageModel<string>();
        if (id > 0)
        {
            var userDetail = await _permissionServices.QueryById(id);
            userDetail.IsDeleted = true;
            data.success = await _permissionServices.Update(userDetail);
            if (data.success)
            {
                data.msg = Lang.BasePermissionViewModel_Delete_success;
                data.response = userDetail?.Id.ObjToString();
            }
        }

        return data;
    }

    /// <summary>
    /// 加载数据命令
    /// </summary>
    [RelayCommand]
    public async Task LoadData()
    {
        // 调用Get方法获取权限数据
        var data = await Get();
        // 将获取到的权限数据转换为ObservableCollection<Permission>并赋值给Datas属性
        Datas = new ObservableCollection<Permission>(data.response.data);
        //Roles = new ObservableCollection<Role>(await _roleServices.Query());
        // 将获取到的权限数据中的第一个元素赋值给Current属性
        Current = data.response.data.FirstOrDefault();
# if DEBUG
        // ExportSomething(Datas.ToList());
#endif
    }

    private void ExportSomething(List<Permission> parameters)
    {
        if (parameters == null || !parameters.Any())
        {
            return; // 如果参数列表为空，直接返回
        }

        // 判断文件是否存在
        if (!File.Exists("Db_Lang.csv"))
        {
            // 文件不存在，创建新的数据列表
            var newRows = parameters.Select(p => new
            {
                Path = "Perm",
                Comment = p.Name ?? string.Empty,
                Name = p.Code ?? string.Empty
            }).ToList();
            // 保存新数据
            MiniExcel.SaveAs("Db_Lang.csv", newRows, overwriteFile: true);
            return;
        }

        // 文件存在，读取现有的 CSV 文件
        var existingRows = MiniExcel.Query<dynamic>("Db_Lang.csv").ToList();

        // 合并数据，如果 Name 相同则更新，否则追加
        foreach (var newParam in parameters)
        {
            var existingRow = existingRows.FirstOrDefault(r => r.Name == newParam.Code);
            if (existingRow != null)
            {
                // 更新现有行
                existingRow.Comment = newParam.Name;
            }
            else
            {
                // 追加新行
                existingRows.Add(new
                {
                    Path = "Perm",
                    Comment = newParam.Name ?? string.Empty,
                    Name = newParam.Code ?? string.Empty
                });
            }
        }

        // 保存更新后的数据
        MiniExcel.SaveAs("Db_Lang.csv", existingRows, overwriteFile: true);
    }


    /// <summary>
    /// 删除一个权限命令
    /// </summary>
    /// <param name="id">要删除的权限ID</param>
    [RelayCommand]
    public async Task<bool> DeleteOne(int id)
    {
        // 调用Delete方法删除指定ID的权限，并返回删除结果的success属性
        return Delete(id).Result.success;
    }

    /// <summary>
    /// 编辑权限命令
    /// </summary>
    /// <param name="role">要编辑的权限对象</param>
    [Log]
    [RelayCommand]
    public void Edit(Permission role)
    {
        // 异步加载权限对
        LoadPermPairs().GetAwaiter();
        // 设置IsAdd属性为false，表示当前处于编辑模式
        IsAdd = false;
        // 将传入的权限对象赋值给Current属性
        Current = role;
    }

    /// <summary>
    /// 添加权限命令
    /// </summary>
    [RelayCommand]
    public void Add()
    {
        // 设置IsAdd属性为true，表示当前处于添加模式
        IsAdd = true;
        // 创建一个新的Permission对象，并设置Enabled属性为true，然后赋值给Current属性
        Current = new Permission { Enabled = true };
    }

    /// <summary>
    /// 保存权限命令
    /// </summary>
    [RelayCommand]
    public async Task<bool> Save()
    {
        // 获取当前编辑的权限对象
        var role = Current;
        if (IsAdd)
        {
            // 如果处于添加模式，则调用Post方法添加权限，并将返回的ID赋值给当前权限对象的Id属性
            var result = Post(Current);
            var id = result.Result.response.ObjToInt();
            Current.Id = id;
        }

        // 调用Put方法更新当前权限对象，并返回更新结果的success属性
        return Put(Current).Result.success;
    }

    /// <summary>
    /// 取消编辑命令
    /// </summary>
    [RelayCommand]
    public void Cancel()
    {
        // 重新加载数据
        LoadData().GetAwaiter();
    }

    /// <summary>
    /// 搜索权限命令
    /// </summary>
    /// <param name="key">搜索关键字</param>
    [RelayCommand]
    public async Task Search(string key)
    {
        if (!string.IsNullOrEmpty(key))
        {
            // 如果关键字不为空，则调用Get方法根据关键字搜索权限数据，并将结果赋值给Datas属性
            var data = await Get(key: key);
            Datas = new ObservableCollection<Permission>(data.response.data);
        }
    }

    /// <summary>
    /// 加载权限对命令
    /// </summary>
    [RelayCommand]
    public async Task LoadPermPairs()
    {
        // 查询当前权限对象的上一级权限
        var pair = await _permissionServices.Query(r => r.OrderSort == Current.OrderSort - 1);
        // 将查询结果转换为KeyValuePair<int, string>类型的集合
        var data = pair.Select(r => new KeyValuePair<int, string>(r.Id, r.Name));
        // 将转换后的集合转换为ObservableCollection<KeyValuePair<int, string>>并赋值给PermPairs属性
        PermPairs = new ObservableCollection<KeyValuePair<int, string>>(data);
    }
}