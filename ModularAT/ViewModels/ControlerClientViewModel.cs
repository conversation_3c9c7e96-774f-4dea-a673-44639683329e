using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common.Attributes;
using ModularAT.Common.Helper;
using ModularAT.Driver.Controller;
using ModularAT.Entity;
using ModularAT.Entity.Config;
using TouchSocket.Core;

namespace ModularAT.ViewModels;

public partial class ControlerClientViewModel : ModularATViewModelBase,
    IRecipient<ValueChangedMessage<InitializationStepEnum>>
{
    #region Constructor

    public ControlerClientViewModel(NavigationService navigationService) : base(navigationService)
    {
        IsActive = true;
        Config = File.Exists(path) ? JsonHelper.LoadFromJson<TcpParamConfig>(path) : new TcpParamConfig();
        RequestGetInitStatus();
    }

    #endregion

    #region Variables

    private readonly ControlerTcpClient client = App.Current.Services.GetService<ControlerTcpClient>();
    public TcpParamConfig Config { get; set; }
    private readonly string path = "settings/Controler.json";

    [ObservableProperty] public partial int CurrentStepIndex { get; set; }

    public bool IsCurrentView => navigation.CurrentViewModel is ControlerClientViewModel;

    #endregion

    #region Functions

    [Log]
    [RelayCommand]
    private async Task ConnectPort()
    {
        await Task.Run(() =>
        {
            client.Setup(Config);
            var ret = client.Connnect();
            if (ret.ResultCode == ResultCode.Success)
                client.Send((ushort)CmdTypeEnum.CMD_REQUEST_CONNECT); //连接指令
            else
                App.Log.Warn($"控制器连接：{ret.Message}");
            JsonHelper.SaveToJson(Config, path);
        });
    }

    [Log]
    [RelayCommand]
    private void ClosePort()
    {
        client.Send((ushort)CmdTypeEnum.CMD_REQUEST_DISCONNECT); //断开指令
        client.Close();
    }

    [Log]
    [RelayCommand]
    private void InitReset()
    {
        client.Send((ushort)CmdTypeEnum.CMD_REQUEST_MaglevInit_Reset, BitConverter.GetBytes(1), true);
    }

    [RelayCommand]
    private void Save()
    {
        JsonHelper.SaveToJson(Config, path);
    }

    [RelayCommand]
    private void RequestGetInitStatus()
    {
        Task.Run(() =>
        {
            while (true)
            {
                if (IsCurrentView && client.IsConnected)
                    client.RequestGetInitStatus();
                Task.Delay(50).Wait();
            }
        });
    }

    public void Receive(ValueChangedMessage<InitializationStepEnum> message)
    {
        CurrentStepIndex = message.Value switch
        {
            InitializationStepEnum.InitSuccess => 8, // 初始化成功显示全部完成
            InitializationStepEnum.Step1GlobalDataReset => 0, // 第一步
            InitializationStepEnum.Step2PlatformCheck => 1, // 第二步
            InitializationStepEnum.Step3SystemParamInit => 2,
            InitializationStepEnum.Step4SlaveInfoGet => 3,
            InitializationStepEnum.Step5SlaveAddrMap => 4,
            InitializationStepEnum.Step6MasterSlaveCheck => 5,
            InitializationStepEnum.Step7BusSystemInitDone => 6,
            InitializationStepEnum.Step7BusSystemInitDone2 => 6,
            InitializationStepEnum.Step8MotionParamInit => 7,
            _ => 0 // 默认返回第一步
        };
    }

    #endregion
}