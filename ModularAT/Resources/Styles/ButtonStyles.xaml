<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style
        x:Key="DefaultButton"
        BasedOn="{StaticResource MahApps.Styles.Button.Flat}"
        TargetType="{x:Type Button}">
        <Setter Property="Margin" Value="5,5,5,5" />
        <Setter Property="MinWidth" Value="55" />
        <!-- <Setter Property="MinHeight" Value="25" /> -->
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.ThemeBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.ThemeForeground}" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Accent2}" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Gray10}" />
            </Trigger>
            <Trigger Property="Button.IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Gray9}" />
                <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.ThemeForeground}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="HighlightedButton"
        BasedOn="{StaticResource DefaultButton}"
        TargetType="{x:Type Button}">
        <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Accent}" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Accent2}" />
            </Trigger>
            <Trigger Property="Button.IsPressed" Value="True">
                <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Accent3}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="ImageWithTextButton"
        BasedOn="{StaticResource DefaultButton}"
        TargetType="{x:Type Button}">
        <Setter Property="Padding" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
    </Style>

    <Style
        x:Key="ImageButton"
        BasedOn="{StaticResource DefaultButton}"
        TargetType="{x:Type Button}">
        <Setter Property="MinWidth" Value="35" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
    </Style>

    <Style x:Key="CleanButton" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid Background="{TemplateBinding Background}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SettingsButton" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid Background="{TemplateBinding Background}">
                        <ContentPresenter VerticalAlignment="Center" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource DefaultButton}" TargetType="{x:Type Button}" />
</ResourceDictionary>