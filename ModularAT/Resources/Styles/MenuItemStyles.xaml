<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks">
    <!--
        Set x:Shared="False" to avoid that only the MenuItem has an icon.
        See: https://stackoverflow.com/questions/6177550/menuitem-style-with-icon-creates-only-one-icon
    -->
    <Style
        x:Key="CopyMenuItem"
        x:Shared="False"
        BasedOn="{StaticResource MahApps.Styles.MenuItem}"
        TargetType="{x:Type MenuItem}">
        <Setter Property="Icon">
            <Setter.Value>
                <Rectangle
                    Width="16"
                    Height="16"
                    Fill="{DynamicResource MahApps.Brushes.Gray3}">
                    <Rectangle.OpacityMask>
                        <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=ContentCopy}" />
                    </Rectangle.OpacityMask>
                </Rectangle>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>