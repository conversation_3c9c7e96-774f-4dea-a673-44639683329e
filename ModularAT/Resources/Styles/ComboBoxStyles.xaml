<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <Style
        x:Key="DefaultComboBox"
        BasedOn="{StaticResource MahApps.Styles.ComboBox}"
        TargetType="{x:Type ComboBox}">
        <Setter Property="FontSize" Value="14" />
        <!--<Setter Property="BorderThickness" Value="0,0,0,1" />-->
        <!-- <Setter Property="HorizontalContentAlignment" Value="Center" /> -->
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="mah:ControlsHelper.FocusBorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource DefaultErrorTemplate}" />
    </Style>

    <Style BasedOn="{StaticResource DefaultComboBox}" TargetType="{x:Type ComboBox}" />

    <Style
        x:Key="EditableComboBox"
        BasedOn="{StaticResource DefaultComboBox}"
        TargetType="{x:Type ComboBox}">
        <Setter Property="IsEditable" Value="True" />
        <Setter Property="ContextMenu" Value="{StaticResource CutCopyPasteContextMenu}" />
    </Style>
    <Style
        x:Key="HistoryComboBox"
        BasedOn="{StaticResource DefaultComboBox}"
        TargetType="{x:Type ComboBox}">
        <Setter Property="IsEditable" Value="True" />
        <Setter Property="ContextMenu" Value="{StaticResource CutCopyPasteContextMenu}" />
    </Style>
</ResourceDictionary>