<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mahAppsControls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro">
    <Style
        x:Key="DefaultWindow"
        BasedOn="{StaticResource MahApps.Styles.MetroWindow.Clean}"
        TargetType="{x:Type mahAppsControls:MetroWindow}">
        <Setter Property="ShowIconOnTitleBar" Value="True" />
        <Setter Property="GlowBrush" Value="{DynamicResource MahApps.Brushes.}" />
        <Setter Property="NonActiveGlowBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="TitleForeground" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="ShowTitleBar" Value="True" />
        <Setter Property="Width" Value="1366" />
        <Setter Property="Height" Value="768" />
    </Style>
</ResourceDictionary>