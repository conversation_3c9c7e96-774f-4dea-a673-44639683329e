<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <Style x:Key="DefaultNumericUpDown" TargetType="{x:Type mah:NumericUpDown}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource DefaultErrorTemplate}" />
    </Style>

    <Style BasedOn="{StaticResource DefaultNumericUpDown}" TargetType="{x:Type mah:NumericUpDown}" />
</ResourceDictionary>