<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks">
    <Style x:Key="ButtonWithImageRectangle" TargetType="{x:Type Rectangle}">
        <Setter Property="Width" Value="20" />
        <Setter Property="Height" Value="20" />
        <Setter Property="Margin" Value="10,5,0,5" />
        <Setter Property="Fill" Value="{DynamicResource MahApps.Brushes.Gray3}" />
    </Style>

    <Style x:Key="HelpImageRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=HelpCircleOutline}" />
            </Setter.Value>
        </Setter>
        <Setter Property="ToolTipService.InitialShowDelay" Value="0" />
        <Setter Property="ToolTipService.ShowDuration" Value="600000" />
    </Style>

    <Style x:Key="InfoImageRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=InformationOutline}" />
            </Setter.Value>
        </Setter>
        <Setter Property="ToolTipService.InitialShowDelay" Value="0" />
        <Setter Property="ToolTipService.ShowDuration" Value="600000" />
    </Style>

    <Style x:Key="WarnImageRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="Orange" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=AlertOutline}" />
            </Setter.Value>
        </Setter>
        <Setter Property="ToolTipService.InitialShowDelay" Value="0" />
        <Setter Property="ToolTipService.ShowDuration" Value="600000" />
    </Style>

    <Style x:Key="ErrorImageRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="Red" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=AlertOctagonOutline}" />
            </Setter.Value>
        </Setter>
        <Setter Property="ToolTipService.InitialShowDelay" Value="0" />
        <Setter Property="ToolTipService.ShowDuration" Value="600000" />
    </Style>

    <Style x:Key="ReloadAnimation" TargetType="{x:Type Rectangle}">
        <Setter Property="Height" Value="24" />
        <Setter Property="RenderTransform">
            <Setter.Value>
                <RotateTransform CenterX="12" CenterY="12" />
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation
                                Storyboard.TargetProperty="RenderTransform.Angle"
                                From="0"
                                To="720"
                                Duration="0:0:2" />
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "ConnectionStateToRectangleStyleConverter" returns to the x:Key  -->
    <Style x:Key="CheckRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="#badc58" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Check}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "ConnectionStateToRectangleStyleConverter" returns to the x:Key  -->
    <Style x:Key="AlertRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="Orange" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=AlertOutline}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "ConnectionStateToRectangleStyleConverter" returns to the x:Key  -->
    <Style x:Key="ErrorRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="Red" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Close}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "ConnectionStateToRectangleStyleConverter" returns to the x:Key  -->
    <!--
    <Style x:Key="HelpRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style TargetType="{x:Type ToolTip}" BasedOn="{StaticResource DefaultToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="{DynamicResource MahApps.Brushes.Gray5}" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=HelpCircleOutline}"/>
            </Setter.Value>
        </Setter>
    </Style>
    -->

    <!--  @FutureMe Don't touch this! Converter "ConnectionStateToRectangleStyleConverter" returns to the x:Key  -->
    <Style x:Key="InfoRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="{DynamicResource MahApps.Brushes.Gray5}" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=InformationOutline}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "ConnectionStateToRectangleStyleConverter" returns to the x:Key  -->
    <Style x:Key="HiddenRectangle" TargetType="{x:Type Rectangle}">
        <Setter Property="Visibility" Value="Hidden" />
    </Style>


    <!--  @FutureMe Don't touch this! Converter "HostUpStyleConverter" returns to the x:Key  -->
    <Style x:Key="HostUpRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="#badc58" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=LanConnect}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "HostUpStyleConverter" returns to the x:Key  -->
    <Style x:Key="HostDownRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="Red" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=LanDisconnect}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "PortOpenStyleConverter" returns to the x:Key  -->
    <Style x:Key="PortOpenRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="#badc58" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Modern Kind=NetworkPort}" />
            </Setter.Value>
        </Setter>
    </Style>

    <!--  @FutureMe Don't touch this! Converter "PortOpenStyleConverter" returns to the x:Key  -->
    <Style x:Key="PortClosedRectangle" TargetType="{x:Type Rectangle}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="Fill" Value="Red" />
        <Setter Property="OpacityMask">
            <Setter.Value>
                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Modern Kind=NetworkPortDisconnect}" />
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>