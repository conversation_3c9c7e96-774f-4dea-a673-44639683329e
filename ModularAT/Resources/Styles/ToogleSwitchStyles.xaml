<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <Style
        x:Key="DefaultToggleSwitch"
        BasedOn="{StaticResource MahApps.Styles.ToggleSwitch}"
        TargetType="{x:Type mah:ToggleSwitch}">
        <Setter Property="FontSize" Value="14" />
        <!--<Setter Property="mah:ControlsHelper.HeaderFontSize" Value="14" />-->
        <!--<Setter Property="ThumbIndicatorBrush" Value="{DynamicResource MahApps.Brushes.Gray3}" />-->
        <Setter Property="OffContent" Value="Off" />
        <Setter Property="OnContent" Value="On" />
        <Style.Resources>
            <SolidColorBrush x:Key="MahApps.Metro.Brushes.ToggleSwitchButton.OffBorderBrush.Win10"
                             Color="{DynamicResource Gray8}" />
        </Style.Resources>
    </Style>

    <Style BasedOn="{StaticResource DefaultToggleSwitch}" TargetType="{x:Type mah:ToggleSwitch}" />
</ResourceDictionary>