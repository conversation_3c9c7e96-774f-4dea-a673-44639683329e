<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <!--  xmlns:localization="clr-namespace:Modular.Localization.Resources;assembly=Modular.Localization">  -->
    <Style
        x:Key="DefaultPasswordBox"
        BasedOn="{StaticResource MahApps.Styles.PasswordBox.Button.Revealed}"
        TargetType="{x:Type PasswordBox}">
        <Style.Resources>
            <Style BasedOn="{StaticResource DefaultToolTip}" TargetType="{x:Type ToolTip}" />
        </Style.Resources>
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="mah:PasswordBoxHelper.CapsLockWarningToolTip" Value="CapsLockIsEnabled" />
        <Setter Property="mah:PasswordBoxHelper.CapsLockIcon">
            <Setter.Value>
                <Rectangle
                    Width="16"
                    Height="16"
                    Fill="{DynamicResource MahApps.Brushes.Gray3}">
                    <Rectangle.OpacityMask>
                        <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=AppleKeyboardShift}" />
                    </Rectangle.OpacityMask>
                </Rectangle>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource DefaultPasswordBox}" TargetType="{x:Type PasswordBox}" />
</ResourceDictionary>