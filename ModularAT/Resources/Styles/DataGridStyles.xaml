<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:ModularAT.Controls"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro">
    <Style
        x:Key="DefaultDataGridTextBoxEditing"
        BasedOn="{StaticResource MahApps.Styles.TextBox.DataGrid.Editing}"
        TargetType="{x:Type TextBox}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource DefaultErrorTemplate}" />
    </Style>


    <Style
        x:Key="DefaultDataGrid"
        BasedOn="{StaticResource MahApps.Styles.DataGrid}"
        TargetType="{x:Type DataGrid}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="CanUserResizeColumns" Value="True" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="AutoGenerateColumns" Value="False" />
        <Setter Property="GridLinesVisibility" Value="Vertical" />
        <Setter Property="ColumnWidth" Value="*" />
        <Setter Property="mah:DataGridHelper.AutoGeneratedTextColumnEditingStyle"
                Value="{StaticResource DefaultDataGridTextBoxEditing}" />
    </Style>

    <!--  Overwrite style from here to adjust the color of the gripper: https://github.com/MahApps/MahApps.Metro/blob/df8a3eb689caa921b936c4aeba15f5fe2bb57163/src/MahApps.Metro/Styles/Controls.DataGrid.xaml#L217  -->
    <Style
        x:Key="DefaultColumnHeaderGripper"
        BasedOn="{StaticResource MahApps.Styles.Thumb.ColumnHeaderGripper}"
        TargetType="{x:Type Thumb}">
        <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="Cursor" Value="SizeWE" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border
                        Margin="{TemplateBinding Padding}"
                        Background="Transparent"
                        BorderBrush="{TemplateBinding Background}"
                        BorderThickness="0,0,1,0"
                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Width" Value="8" />
    </Style>

    <Style
        x:Key="DefaultDataGridColumnHeader"
        BasedOn="{StaticResource MahApps.Styles.DataGridColumnHeader}"
        TargetType="{x:Type DataGridColumnHeader}">
        <Setter Property="mah:ControlsHelper.ContentCharacterCasing" Value="Normal" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <!--  Overwrite style from here to adjust the color of the gripper: https://github.com/MahApps/MahApps.Metro/blob/df8a3eb689caa921b936c4aeba15f5fe2bb57163/src/MahApps.Metro/Styles/Controls.DataGrid.xaml#L248  -->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridColumnHeader}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Border
                            x:Name="BackgroundBorder"
                            Grid.ColumnSpan="2"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}" />

                        <mah:ContentControlEx
                            x:Name="HeaderContent"
                            Grid.Column="0"
                            Margin="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                            Content="{TemplateBinding Content}"
                            ContentCharacterCasing="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=(mah:ControlsHelper.ContentCharacterCasing)}"
                            ContentStringFormat="{TemplateBinding ContentStringFormat}"
                            ContentTemplate="{TemplateBinding ContentTemplate}"
                            ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                            RecognizesAccessKey="{TemplateBinding mah:ControlsHelper.RecognizesAccessKey}"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />

                        <Path
                            x:Name="SortArrow"
                            Grid.Column="1"
                            Width="8"
                            Height="10"
                            Margin="0,0,8,2"
                            VerticalAlignment="Center"
                            Fill="{DynamicResource MahApps.Brushes.Gray3}"
                            RenderTransformOrigin="0.5,0.5"
                            Stretch="Fill"
                            Visibility="Collapsed" />

                        <Thumb
                            x:Name="PART_LeftHeaderGripper"
                            Grid.Column="0"
                            HorizontalAlignment="Left"
                            Background="Transparent"
                            Style="{StaticResource DefaultColumnHeaderGripper}" />

                        <Thumb
                            x:Name="PART_RightHeaderGripper"
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            Style="{StaticResource DefaultColumnHeaderGripper}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="SortDirection" Value="Ascending">
                            <Setter TargetName="SortArrow" Property="Data"
                                    Value="F1 M 34,57L 42,57L 42,32.25L 52,42.25L 52,31.75L 38,17.75L 24,31.75L 24,42.25L 34,32.25L 34,57 Z " />
                            <Setter TargetName="SortArrow" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="SortDirection" Value="Descending">
                            <Setter TargetName="SortArrow" Property="Data"
                                    Value="F1 M 42,19.0002L 34,19.0002L 34,43.7502L 24,33.7502L 24,44.2502L 38,58.2502L 52,44.2502L 52,33.7502L 42,43.7502L 42,19.0002 Z " />
                            <Setter TargetName="SortArrow" Property="Margin" Value="0,0,8,0" />
                            <Setter TargetName="SortArrow" Property="Visibility" Value="Visible" />
                        </Trigger>
                        <Trigger Property="DisplayIndex" Value="0">
                            <Setter TargetName="PART_LeftHeaderGripper" Property="Visibility" Value="Collapsed" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource DefaultDataGrid}" TargetType="{x:Type DataGrid}" />
    <Style BasedOn="{StaticResource DefaultDataGrid}" TargetType="{x:Type controls:MultiSelectDataGrid}" />
    <Style BasedOn="{StaticResource DefaultDataGrid}" TargetType="{x:Type controls:MultiSelectScrollingDataGrid}" />
    <Style BasedOn="{StaticResource DefaultDataGridColumnHeader}" TargetType="{x:Type DataGridColumnHeader}" />
</ResourceDictionary>