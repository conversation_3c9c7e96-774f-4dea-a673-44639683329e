<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style
        x:Key="DefaultToolTip"
        BasedOn="{StaticResource ResourceKey=MahApps.Styles.ToolTip}"
        TargetType="{x:Type ToolTip}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderBrush" Value="{DynamicResource ResourceKey=MahApps.Brushes.Gray8}" />
        <Setter Property="Foreground" Value="{DynamicResource ResourceKey=MahApps.Brushes.Text}" />
        <Setter Property="Background" Value="{DynamicResource ResourceKey=MahApps.Brushes.Window.Background}" />
    </Style>

    <Style BasedOn="{StaticResource ResourceKey=DefaultToolTip}" TargetType="{x:Type ToolTip}" />

    <Style
        x:Key="HelpToolTip"
        BasedOn="{StaticResource DefaultToolTip}"
        TargetType="{x:Type ToolTip}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToolTip}">
                    <Border
                        x:Name="Root"
                        MaxWidth="400"
                        Padding="5"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Opacity="0"
                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                        <Border.Resources>
                            <Storyboard x:Key="Visible State" />
                            <Storyboard x:Key="Normal State" />
                        </Border.Resources>
                        <TextBlock
                            VerticalAlignment="Center"
                            Foreground="{TemplateBinding Foreground}"
                            Style="{StaticResource WrapTextBlock}"
                            Text="{TemplateBinding Content}" />
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="OpenStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition
                                        GeneratedDuration="0:0:0.3"
                                        From="Open"
                                        To="Closed" />
                                </VisualStateGroup.Transitions>
                                <VisualState x:Name="Closed">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                       Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="0" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Open">
                                    <Storyboard>
                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Root"
                                                                       Storyboard.TargetProperty="(UIElement.Opacity)">
                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="1" />
                                        </DoubleAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="WarnToolTip"
        BasedOn="{StaticResource HelpToolTip}"
        TargetType="{x:Type ToolTip}" />

    <Style
        x:Key="ErrorToolTip"
        BasedOn="{StaticResource HelpToolTip}"
        TargetType="{x:Type ToolTip}" />
</ResourceDictionary>