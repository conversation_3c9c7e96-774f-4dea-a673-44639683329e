using ModularAT.Localization.Resources;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using System.Windows;
using System.Windows.Threading;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using AutoMapper;
using log4net;
using log4net.Config;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ModularAT.Common;
using ModularAT.Common.Helper;
using ModularAT.Common.Log;
using ModularAT.Driver.Controller;
using ModularAT.Driver.Servo;
using ModularAT.Entitys;
using ModularAT.Mapper;
using ModularAT.Service.Setting;
using ModularAT.Service.Simulation;
using ModularAT.Setup;
using ModularAT.ViewModels;
using ModularAT.Views;

namespace ModularAT;

/// <summary>
///     Interaction logic for App.xaml
/// </summary>
public partial class App : System.Windows.Application
{
    private static readonly IHost _host = Host.CreateDefaultBuilder()
        .ConfigureAppConfiguration(c =>
        {
            var basePath =
                Path.GetDirectoryName(AppContext.BaseDirectory)
                ?? throw new DirectoryNotFoundException(
                    "Unable to find the base directory of the application."
                );
            _ = c.SetBasePath(basePath);
        })
        .UseServiceProviderFactory(new AutofacServiceProviderFactory())
        .ConfigureContainer<ContainerBuilder>(
            builder => { builder.RegisterModule(new AutofacModuleRegister()); })
        .ConfigureServices(
            services =>
            {
                // 配置json编码，解决中文乱码问题。
                services.AddOptions<JsonSerializerOptions>()
                    .Configure(option => option.Encoder = JavaScriptEncoder.Create(UnicodeRanges.All));

                services.AddSingleton(new Appsettings());

                services.AddSingleton<NavigationService>();
                services.AddSingleton<ServoSerialPortClient>();
                services.AddSingleton<ControlerTcpClient>();

                //功能性服务
                services.AddSingleton<ServoSettingService>();
                services.AddSingleton<SimulationProtocolServiceImpl>();
                services.AddSingleton<IUserConfig, UserConfig>();
                services.AddSingleton<IOperator, Operator>();
                services.AddSingleton<NoticeListViewModel>();


                //数据库
                services.AddSqlsugarSetup();

                services.AddAuthorizationSetup();

                #region automapper

                //List<Assembly> myAssembly = RuntimeHelper.GetAllMyAssemblies().ToList();
                services.AddAutoMapper(typeof(MappingProfile));
                services.AddTransient<IMapper, AutoMapper.Mapper>();

                #endregion

                //  注册Viewmodels
                services.AddSingleton<DataViewModel>();
                services.AddSingleton<LoginViewModel>();
                services.AddSingleton(sp => new LoginWindow { DataContext = sp.GetRequiredService<LoginViewModel>() });
                services.AddSingleton<MainViewModel>();
                services.AddSingleton(sp => new MainWindow { DataContext = sp.GetRequiredService<MainViewModel>() });
                services.AddSingleton<ServoSettingView>();
                services.AddSingleton<ServoSettingViewModel>();
                services.AddSingleton<ServoSerialPortView>();
                services.AddSingleton<ServoSerialPortViewModel>();
                services.AddSingleton<ControlerClientView>();
                services.AddSingleton<ControlerClientViewModel>();
                services.AddSingleton<ScopeView>();
                services.AddSingleton<ControlerDebugViewModel>();
                services.AddSingleton<ControlerAxisViewModel>();
                services.AddSingleton(sp => new ControlerAxisView
                { DataContext = sp.GetRequiredService<ControlerAxisViewModel>() });
                services.AddSingleton<FtpClientViewModel>();
                services.AddSingleton(sp => new FtpClientView()
                { DataContext = sp.GetRequiredService<FtpClientViewModel>() });
                services.AddSingleton<ControlerSysViewModel>();
                services.AddSingleton(sp => new ControlerSysView
                { DataContext = sp.GetRequiredService<ControlerSysViewModel>() });
                services.AddSingleton<ControlerTranStatusViewModel>();
                services.AddSingleton(sp => new ControlerTranStatusView
                { DataContext = sp.GetRequiredService<ControlerTranStatusViewModel>() });
                services.AddSingleton<SimulationViewModel>();
                services.AddSingleton(sp => new SimulationView
                { DataContext = sp.GetRequiredService<SimulationViewModel>() });
                services.AddSingleton<ControlerGenerateConfigViewModel>();
                services.AddSingleton(sp => new ControlerGenerateConfigView
                { DataContext = sp.GetRequiredService<ControlerGenerateConfigViewModel>() });
                services.AddSingleton<ControlerOnlineConfigViewModel>();
                services.AddSingleton(sp => new ControlerOnlineConfigView
                { DataContext = sp.GetRequiredService<ControlerOnlineConfigViewModel>() });
                //基本管理
                services.AddSingleton<BaseRoleViewModel>();
                services.AddSingleton(sp => new BaseRoleView
                { DataContext = sp.GetRequiredService<BaseRoleViewModel>() });
                services.AddSingleton<BaseUserViewModel>();
                services.AddSingleton(sp => new BaseUserView
                { DataContext = sp.GetRequiredService<BaseUserViewModel>() });
                services.AddSingleton<BasePermissionViewModel>();
                services.AddSingleton(sp => new BasePermissionView
                { DataContext = sp.GetRequiredService<BasePermissionViewModel>() });
                services.AddSingleton<BasePermAssignViewModel>();
                services.AddSingleton(sp => new BasePermAssignView
                { DataContext = sp.GetRequiredService<BasePermAssignViewModel>() });
                //数据追溯
                services.AddSingleton<OperateLogViewModel>();
                services.AddSingleton(sp => new OperateLogView
                { DataContext = sp.GetRequiredService<OperateLogViewModel>() });
            })
        .Build();

    //等待log4net配置文件加载完毕

    /// <summary>
    ///     Gets the current<see cref="App" /> instance in use
    /// </summary>
    public new static App Current => (App)System.Windows.Application.Current;

    public static ILog Log { get; } = LogHelper.GetInstance();

    /// <summary>
    ///     Gets the <see cref="IServiceProvider" /> instance to resolve application services.
    /// </summary>
    public IServiceProvider Services => _host.Services;

    /// <summary>
    ///     Gets registered service.
    /// </summary>
    /// <typeparam name="T">Type of the service to get.</typeparam>
    /// <returns>Instance of the service or <see langword="null" />.</returns>
    public static T? GetService<T>() where T : class
    {
        return _host.Services.GetService(typeof(T)) as T;
    }

    /// <summary>
    ///     程序启动事件
    /// </summary>
    /// <param name="e"></param>
    protected override void OnStartup(StartupEventArgs e)
    {
        // 检查是否已经有实例在运行
        if (IsApplicationAlreadyRunning())
        {
            MessageBox.Show("ModularAT Is Runing.", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
            Current.Shutdown();
            return;
        }

        base.OnStartup(e);

        // 配置日志
        XmlConfigurator.Configure(new FileInfo("log4net.config"));

        //配置映射扩展方法
        AutoMapperSetup.UsePack(_host.Services);

        //启动主机
        _host.RunAsync().ConfigureAwait(false);


        //配置全局服务提供者
        ServiceLocator.Initialize(_host.Services);

        //UI线程未捕获异常处理事件
        DispatcherUnhandledException += App_DispatcherUnhandledException;

        //Task线程内未捕获异常处理事件
        TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

        //非UI线程未捕获异常处理事件
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

        ShutdownMode = ShutdownMode.OnExplicitShutdown;

        //启动登陆界面
        var login = Current.Services.GetService<LoginWindow>();
        if (login?.ShowDialog() == false)
        {
            Current.Shutdown();
        }
        else
        {
            MainWindow = _host.Services.GetRequiredService<MainWindow>();
            MainWindow.Visibility = Visibility.Visible;
            MainWindow.Closed += (s, e) => Current.Shutdown();
        }
    }

    private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            e.Handled = true; //把 Handled 属性设为true，表示此异常已处理，程序可以继续运行，不会强制退出      
#if DEBUG
            MessageBox.Show(Lang.App_xaml_Ui_thread + e.Exception.Message);
#endif
            MsgToUiHelper.SendMsgError($"UI线程：{e.Exception.Message}".Replace(Environment.NewLine, string.Empty));
            Log.Error(Lang.App_xaml_Ui_thread_exception + e.Exception.Message, e.Exception);
        }
        catch (Exception ex)
        {
            //此时程序出现严重异常，将强制结束退出
            MessageBox.Show(Lang.App_xaml_Ui_thread_fatal_error);
            //log.Fatal(Lang.App_xaml_Ui_thread_fatal_error, ex);
        }
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var sbEx = new StringBuilder();
        if (e.IsTerminating) sbEx.Append(Lang.App_xaml_Non_ui_thread_fatal_error);
        sbEx.Append(Lang.App_xaml_Non_ui_thread_exception);
        if (e.ExceptionObject is Exception)
            sbEx.Append(((Exception)e.ExceptionObject).Message);
        else
            sbEx.Append(e.ExceptionObject);
        Log.Fatal(sbEx.ToString(), e.ExceptionObject as Exception);
        MessageBox.Show(sbEx.ToString());
    }

    private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
    {
        //task线程内未处理捕获
#if DEBUG
        MessageBox.Show(Lang.App_xaml_Task_thread + e.Exception.Message);
#endif
        if (e.Exception.InnerException != null)
            MsgToUiHelper.SendMsgError($"Task线程：{e.Exception.InnerException.Message}");
        Log.Error(Lang.App_xaml_Task_thread_exception + e.Exception.Message, e.Exception);
        e.SetObserved(); //设置该异常已察觉（这样处理后就不会引起程序崩溃）
    }

    protected override void OnExit(ExitEventArgs e)
    {
        Log.Info("ModularAT Exited");
        base.OnExit(e);
        _host.StopAsync().GetAwaiter().GetResult();
        ;
        _host.Dispose();
        Process.GetCurrentProcess().Kill();
    }


    private bool IsApplicationAlreadyRunning()
    {
        var appName = Process.GetCurrentProcess().ProcessName;
        Process[] processes = Process.GetProcessesByName(appName);

        // 如果有多个实例在运行（包括当前实例）
        if (processes.Length > 1) return true;
        return false;
    }
}