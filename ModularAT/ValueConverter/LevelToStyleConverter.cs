using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using log4net.Core;

namespace ModularAT.ValueConverter
{
    public class LevelToStyleConverter : IValueConverter
    {
        private static readonly Dictionary<string, (string Background, string BorderBrush)> _colorMappings = 
            new(StringComparer.OrdinalIgnoreCase)
        {
            ["ERROR"] = ("#FFEBEE", "#FFCDD2"),
            ["WARN"] = ("#FFF8E1", "#FFECB3"),
            ["NOTICE"] = ("#E8F5E9", "#C8E6C9"),
            ["INFO"] = ("#E3F2FD", "#BBDEFB")
        };
        
        public object? Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (parameter is string param && value is Level level)
            {
                if (_colorMappings.TryGetValue(level.Name, out var colors))
                {
                    return param switch
                    {
                        "Background" => new SolidColorBrush((Color)ColorConverter.ConvertFromString(colors.Background)),
                        "BorderBrush" => new SolidColorBrush((Color)ColorConverter.ConvertFromString(colors.BorderBrush)),
                        _ => null
                    };
                }
                
                // 默认颜色
                return param switch
                {
                    "Background" => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#E3F2FD")),
                    "BorderBrush" => new SolidColorBrush((Color)ColorConverter.ConvertFromString("#BBDEFB")),
                    _ => null
                };
            }
            return "InfoNoticeStyle";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}