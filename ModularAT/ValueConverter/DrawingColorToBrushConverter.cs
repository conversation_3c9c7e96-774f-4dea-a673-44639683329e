using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using ScottPlot;
using Color = ScottPlot.Color;
using DrawingColor = System.Drawing.Color;

namespace ModularAT.ValueConverter;

public class DrawingColorToBrushConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is DrawingColor drawingColor)
            return new SolidColorBrush(System.Windows.Media.Color.FromArgb(drawingColor.A, drawingColor.R,
                drawingColor.G, drawingColor.B));
        if (value is Color scottColor)
            return new SolidColorBrush(System.Windows.Media.Color.FromArgb(
                scottColor.A, scottColor.R, scottColor.G, scottColor.B));
        return Brushes.Transparent;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is SolidColorBrush brush && targetType == typeof(Color))
        {
            return new Color(brush.Color.A, brush.Color.R, brush.Color.G, brush.Color.B);
        }
        if (value is SolidColorBrush wpfBrush && targetType == typeof(DrawingColor))
        {
            return DrawingColor.FromArgb(wpfBrush.Color.A, wpfBrush.Color.R, wpfBrush.Color.G, wpfBrush.Color.B);
        }
        throw new NotImplementedException();
    }
}