using System.Globalization;
using System.Windows.Data;

namespace ModularAT.ValueConverter;

public class DynamicDivideConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int intValue && parameter is string parameterString &&
            double.TryParse(parameterString, out var divisor))
        {
            var result = intValue / divisor;
            return result.ToString("F2", culture); // 保留两位小数
        }

        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string stringValue && double.TryParse(stringValue, out var doubleValue) &&
            parameter is string parameterString &&
            double.TryParse(parameterString, out var divisor)) return (int)(doubleValue * divisor);
        return value;
    }
}