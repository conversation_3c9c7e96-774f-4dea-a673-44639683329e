using ModularAT.Localization.Resources;
﻿using System.Globalization;
using System.Windows.Data;

namespace ModularAT.ValueConverter;

public class InputConverter : IMultiValueConverter
{
    object IMultiValueConverter.Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values == null || values.Length != 3 || !double.TryParse(values[0].ToString(), out var value))
            return null;

        var min = double.Parse(values[0].ToString());
        var max = double.Parse(values[1].ToString());
        var coefficient = int.Parse(values[2].ToString());

        if (min <= value && value <= max) return value.ToString("F2", culture);

        throw new ArgumentException(Lang.InputConverter_Input_value_range);
    }

    object[] IMultiValueConverter.ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}