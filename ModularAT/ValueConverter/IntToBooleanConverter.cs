using System.Globalization;
using System.Windows.Data;

namespace ModularAT.ValueConverter;

public class IntToBooleanConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value.ToString() == parameter.ToString();
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var isChecked = (bool)value;
        if (!isChecked) return null;
        return int.Parse(parameter.ToString());
    }
}