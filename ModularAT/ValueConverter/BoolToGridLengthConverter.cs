using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace ModularAT.ValueConverter
{
    /// <summary>
    /// 将布尔值转换为GridLength的转换器
    /// </summary>
    public class BoolToGridLengthConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                if (parameter is string paramString)
                {
                    var parts = paramString.Split(',');
                    if (parts.Length == 2 && double.TryParse(parts[0], out double trueValue) && double.TryParse(parts[1], out double falseValue))
                    {
                        return new GridLength(boolValue ? trueValue : falseValue);
                    }
                }

                // 默认值：true为250，false为0
                return new GridLength(boolValue ? 250 : 0);
            }

            return new GridLength(0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is GridLength gridLength)
            {
                if (parameter is string paramString)
                {
                    var parts = paramString.Split(',');
                    if (parts.Length == 2 && double.TryParse(parts[0], out double trueValue))
                    {
                        return Math.Abs(gridLength.Value - trueValue) < 0.1;
                    }
                }

                // 默认值：大于0为true
                return gridLength.Value > 0;
            }

            return false;
        }
    }
}