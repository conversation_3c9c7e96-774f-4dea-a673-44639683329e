using System.Globalization;
using System.Windows.Data;

namespace ModularAT.ValueConverter;

public class MultiplyByHundredConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double doubleValue) return doubleValue / 100;
        return value;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        double v = 0;
        double.TryParse(value.ToString(), out v);
        return v * 100;
    }
}