using System.Globalization;
using System.Windows.Data;

namespace ModularAT.ValueConverter;

public class HexConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int intValue) return intValue.ToString("X");
        return null;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string strValue && int.TryParse(strValue, NumberStyles.HexNumber, CultureInfo.InvariantCulture,
                out var intValue)) return intValue;
        return null;
    }
}