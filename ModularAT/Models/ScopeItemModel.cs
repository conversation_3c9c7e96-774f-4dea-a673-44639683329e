using CommunityToolkit.Mvvm.ComponentModel;
using ModularAT.Driver.Servo;
using ScottPlot;
using ScottPlot.Plottables;

namespace ModularAT.Entitys;

[INotifyPropertyChanged]
public partial class ScopeItemModel
{
    [ObservableProperty] Color color;

    [ObservableProperty] bool isSelected;


    [ObservableProperty] bool isVisible;


    [ObservableProperty] long value;

    public ScopeItemModel(string name)
    {
        Name = name;
    }

    public int Id { get; set; }

    public string Name { get; }

    /// <summary>
    ///     通道参数索引,2字节
    /// </summary>
    public ushort ParamIndex
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
                //重新选择后更新主图lable
                if (Plot is DataLogger dataLogger)
                    dataLogger.Label = ScopeConst.ChannelParams.FirstOrDefault(p => p.Item3 == value).Item2;
                if (Plot is Signal signalPlot)
                    signalPlot.Label = ScopeConst.ChannelParams.FirstOrDefault(p => p.Item3 == value).Item2;
            }
        }
    }

    /// <summary>
    /// 单个曲线对象
    /// </summary>
    public IPlottable Plot
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// 缩放值
    /// </summary>
    public int Scale
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
                if (Plot is Signal signalPlot)
                    signalPlot.Data.YScale = value;
            }
        }
    }

    /// <summary>
    /// 偏移值
    /// </summary>
    public int Offset
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
                if (Plot is Signal signalPlot) signalPlot.Data.YOffset = value;
            }
        }
    }
}