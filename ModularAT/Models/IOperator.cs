using System.Collections.ObjectModel;
using ModularAT.Entity.BaseManage;

namespace ModularAT.Entitys;

public interface IOperator
{
    /// <summary>
    ///     当前操作者UserId
    /// </summary>
    int UserId { get; }

    string UserName { get; set; }

    /// <summary>
    ///     当前操作者
    /// </summary>
    sysUserInfo Property { get; set; }

    List<Role> Roles { get; set; }

    //菜单树
    ObservableCollection<AMenuItem> MenuTrees { get; set; }

    //打平用于查询的菜单
    ObservableCollection<AMenuItem> Menus { get; set; }
}