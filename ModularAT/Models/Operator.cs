using System.Collections.ObjectModel;
using ModularAT.Entity.BaseManage;

namespace ModularAT.Entitys;

/// <summary>
///     操作者
/// </summary>
public class Operator : IOperator
{
    public int UserId => Property.uID;

    public string UserName { get; set; }

    public List<Role> Roles { get; set; }

    public sysUserInfo Property { get; set; } = new();

    ////菜单树
    public ObservableCollection<AMenuItem> MenuTrees { get; set; }

    ////打平用于查询的菜单
    public ObservableCollection<AMenuItem> Menus { get; set; }
}