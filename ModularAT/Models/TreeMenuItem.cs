using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;

namespace ModularAT.Entitys;

/// <summary>
///     The HamburgerTreeMenuItem provides an abstract implementation for HamburgerMenu entries.
/// </summary>
public class TreeMenuItem : INotifyPropertyChanged
{
    private ObservableCollection<TreeMenuItem> children = new();

    private ICommand command;

    private string commandParameter;
    private string glyph;

    private string label;

    private object tag;


    private Type targetPageType;

    public string Glyph
    {
        get => glyph;
        set
        {
            if (value == glyph) return;
            glyph = value;
            OnPropertyChanged("Glyph");
        }
    }

    [Browsable(true)]
    public string Label
    {
        get => label;
        set
        {
            if (value == label) return;
            label = value;
            OnPropertyChanged("Label");
        }
    }

    public Type TargetPageType
    {
        get => targetPageType;
        set
        {
            if (value == targetPageType) return;
            targetPageType = value;
            OnPropertyChanged("TargetPageType");
        }
    }

    public object Tag
    {
        get => tag;
        set
        {
            if (value == tag) return;
            tag = value;
            OnPropertyChanged("Tag");
        }
    }

    public ICommand Command
    {
        get => command;
        set
        {
            if (value == command) return;
            command = value;
            OnPropertyChanged("Command");
        }
    }

    public string CommandParameter
    {
        get => commandParameter;
        set
        {
            if (value == commandParameter) return;
            commandParameter = value;
            OnPropertyChanged("CommandParameter");
        }
    }

    public ObservableCollection<TreeMenuItem> Children
    {
        get => children;
        set
        {
            if (value == children) return;
            children = value;
            OnPropertyChanged("Children");
        }
    }


    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged(string propertyName)
    {
        var handler = PropertyChanged;
        if (handler != null) handler(this, new PropertyChangedEventArgs(propertyName));
    }
}