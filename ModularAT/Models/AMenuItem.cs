using System.ComponentModel;
using AutoMapper;

namespace ModularAT.Entitys;

[AutoMap(typeof(AToolItem))]
public class AMenuItem : TreeMenuItem
{
    private string icon;

    private bool isChecked;

    [Browsable(true)]
    public string Icon
    {
        get => icon;
        set
        {
            if (value == icon) return;
            icon = value;
            OnPropertyChanged("Icon");
        }
    }

    public string Code { get; set; }

    public string Value { get; set; }

    public string WpfName
    {
        get
        {
            if (WpfCode == null)
                return null;

            return WpfCode.Substring(WpfCode.LastIndexOf(".") + 1).Replace("View", "");
        }
    }

    public string WpfCode
    {
        get
        {
            if (Code == null)
                return null;



            var subcode = Code.Replace("/Index", "IndexView").Replace("/TreeList", "TreeView").Replace("/List", "View")
                .Split(new[] { "/" }, StringSplitOptions.RemoveEmptyEntries);
            if (subcode.Length == 1)
                return Code;

            subcode[subcode.Length - 1] = $"Views.{subcode[subcode.Length - 1]}";

            if (!subcode[subcode.Length - 1].EndsWith("View"))
                subcode[subcode.Length - 1] = subcode[subcode.Length - 1] + "View";

            return $"AIStudio.Wpf.{string.Join(".", subcode)}";
        }
    }

    public int Type { get; set; } = -1;

    public AMenuItem Parent { get; set; }
    public string Id { get; set; }
    public string ParentId { get; set; }

    public bool NeedAction { get; set; }
    public List<string> PermissionValues { get; set; }

    public bool IsChecked
    {
        get => isChecked;
        set
        {
            if (value == isChecked) return;
            isChecked = value;
            OnPropertyChanged("IsChecked");
        }
    }

    public void AddChildren(AMenuItem child)
    {
        child.Parent = this;
        Children.Add(child);
    }
}