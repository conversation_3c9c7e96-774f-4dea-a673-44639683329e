using System.ComponentModel;
using AutoMapper;
using AutoMapper.Configuration.Annotations;

namespace ModularAT.Entitys;

[AutoMap(typeof(AMenuItem))]
public class AToolItem : AMenuItem
{
    private string _badge;

    private string _badgeBrush;

    private double _height = 48;

    private double _margin = 3;

    private string _parentCode;
    private double _width = 48;

    [Browsable(true)]
    public string Badge
    {
        get => _badge;
        set
        {
            if (_badge == value) return;
            _badge = value;
            OnPropertyChanged("Badge");
        }
    }

    [Browsable(true)]
    public string BadgeBrush
    {
        get => _badgeBrush;
        set
        {
            if (_badgeBrush == value) return;
            _badgeBrush = value;
            OnPropertyChanged("BadgeBrush");
        }
    }

    public int Sort { get; set; }

    [Browsable(true)]
    public double Width
    {
        get => _width;
        set
        {
            if (value == _width) return;
            _width = value;
            OnPropertyChanged("Width");
        }
    }

    [Browsable(true)]
    public double Height
    {
        get => _height;
        set
        {
            if (value == _height) return;
            _height = value;
            OnPropertyChanged("Height");
        }
    }

    [Browsable(true)]
    public double Margin
    {
        get => _margin;
        set
        {
            if (value == _margin) return;
            _margin = value;
            OnPropertyChanged("Margin");
        }
    }

    [SourceMember(nameof(AMenuItem.Parent.Code))]
    public string ParentCode
    {
        get => _parentCode;
        set
        {
            if (value == _parentCode) return;
            _parentCode = value;
            OnPropertyChanged("ParentCode");
        }
    }
}