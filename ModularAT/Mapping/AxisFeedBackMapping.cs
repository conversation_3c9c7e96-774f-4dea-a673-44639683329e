using ModularAT.Localization.Resources;
﻿using System.Text;

namespace ModularAT.Mapper;

public class AxisFeedBackMapping
{
    /// <summary>
    ///     轴运行状态
    ///     bit0: 单轴使能状态
    ///     bit1: 单轴运行状态
    ///     bit2: 单轴报警状态
    ///     bit3: 单轴错误状态
    ///     bit4: 单轴左碰撞
    ///     bit5: 单轴右碰撞
    ///     bit6: 单轴正限位
    ///     bit7: 单轴负限位
    ///     bit8: 单轴在工位上
    ///     bit9: 单轴已到达目标位置
    ///     bit10-bit31:预留
    /// </summary>
    private static readonly Dictionary<uint, string> UdiAxisRunStateDict = new()
    {
        { 1 << 0, Lang.AxisFeedBackMapping_Single_axis_enable_status },
        { 1 << 1, Lang.AxisFeedBackMapping_Single_axis_running_status },
        { 1 << 2, Lang.AxisFeedBackMapping_Single_axis_alarm_status },
        { 1 << 3, Lang.AxisFeedBackMapping_Single_axis_error_status },
        { 1 << 4, Lang.AxisFeedBackMapping_Single_axis_left_collision },
        { 1 << 5, Lang.AxisFeedBackMapping_Single_axis_right_collision },
        { 1 << 6, Lang.AxisFeedBackMapping_Single_axis_positive_limit },
        { 1 << 7, Lang.AxisFeedBackMapping_Single_axis_negative_limit },
        { 1 << 8, Lang.AxisFeedBackMapping_Single_axis_on_workstation },
        { 1 << 9, Lang.AxisFeedBackMapping_Single_axis_reached_target }
    };

    [Obsolete]
    public static string GetUdiAxisRunStateDescription(uint state)
    {
        if (UdiAxisRunStateDict.TryGetValue(state, out var description)) return description;
        return state.ToString();
    }

    public static string GetUdiAxisRunStateOctDesc(uint state)
    {
        var binaryString = Convert.ToString(state, 2).PadLeft(32, '0');
        string[] descriptions =
        [
            Lang.AxisFeedBackMapping_Single_axis_enable_status,
            Lang.AxisFeedBackMapping_Single_axis_running_status,
            Lang.AxisFeedBackMapping_Single_axis_alarm_status,
            Lang.AxisFeedBackMapping_Single_axis_error_status,
            Lang.AxisFeedBackMapping_Single_axis_left_collision,
            Lang.AxisFeedBackMapping_Single_axis_right_collision,
            Lang.AxisFeedBackMapping_Single_axis_positive_limit,
            Lang.AxisFeedBackMapping_Single_axis_negative_limit,
            Lang.AxisFeedBackMapping_Single_axis_on_workstation,
            Lang.AxisFeedBackMapping_Single_axis_reached_target
        ];

        StringBuilder result = new();
        for (var i = 0; i < 10; i++) // 只处理前10位
        {
            var bitValue = (state & (1u << i)) != 0;
            result.Append($"bit{i}={bitValue}，{descriptions[i]}{Environment.NewLine}");
        }

        return result.ToString();
    }
}