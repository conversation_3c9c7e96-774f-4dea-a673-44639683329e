using AutoMapper;
using ModularAT.Driver.Controller;
using ModularAT.Driver.Servo;
using ModularAT.Driver.Servo.Packages;
using ModularAT.Entity;
using ModularAT.Entity.BaseManage;
using ModularAT.Entity.Controller;
using ModularAT.Entity.Dtos;
using ModularAT.Entity.OnlineConfig;

namespace ModularAT.Mapper;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<SysUserInfoDto, sysUserInfo>();

        //ViewModel
        CreateMap<MotorParamModel, MotorParamPackage>();
        CreateMap<SystemParamModel, SystemParamPackage>();
        CreateMap<EncoderParamModel, EncoderParamPackage>();
        CreateMap<ProtectParamModel, ProtectParamPackage>();
        CreateMap<ControlStateParamModel, ControlStateParamPackage>();
        CreateMap<PositionParamModel, PositionParamPackage>();
        CreateMap<SpeedParamModel, SpeedParamPackage>();
        CreateMap<TorqueParamModel, TorqueParamPackage>();
        CreateMap<DriverModeSetModel, DriverModeSetPackage>();

        //控制器
        //反馈指令映射
        CreateMap<AxisFeedBackItem, AxisFeedBackModel>();
        CreateMap<SysFeedBackItem, SysFeedBackModel>();
        CreateMap<TransStateFeedBackItem, TransStateFeedBackModel>();
        CreateMap<RoAxisFeedBackItem, RoAxisFeedBackModel>();
        //控制指令映射
        CreateMap<AxisCtrlCmdModel, AxisCtrlCmdPackage>();
        CreateMap<SysCtrlCmdModel, SysCtrlCmdPackage>();
        CreateMap<TransStateModel, TransStatePackage>();
        CreateMap<RoAxisCtrlCmdModel, RoAxisCtrlCmd>();

        //在线配置
        CreateMap<OnlineAxisOffsetCfgPara, Online_AxisOffsetCfgDto>();
        CreateMap<OnlineLineBodyCfgPara, Online_LineBodyCfgDto>();
        CreateMap<OnlineMotorCfgPara, Online_MotorCfgDto>();
        CreateMap<OnlineMoverArrayCfgPara, Online_MoverArrayCfgDto>();
        CreateMap<OnlineMoverCfgPara, Online_MoverCfgDto>();
        CreateMap<OnlineMoverOffsetCfgPara, Online_MoverOffsetCfgDto>();
        CreateMap<OnlinePIDCfgPara, Online_PIDCfgDto>();
        CreateMap<OnlineSlaveNodeCfgPara, Online_SlaveNodeCfgDto>();
        CreateMap<Online_StationCfgDto, OnlineStationCfgPara>();
        CreateMap<OnlineStationCfgPara, Online_StationCfgDto>();
        CreateMap<OnlineSysCfgPara, Online_SysCfgDto>();
        CreateMap<Online_SysCfgDto, OnlineSysCfgPara>();
    }
}