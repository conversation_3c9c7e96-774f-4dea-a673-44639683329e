using ModularAT.Localization.Resources;
using System.Text;
using ModularAT.Entity;
using ModularAT.Service.Setting;

namespace ModularAT.Mapper;

public static class SysFeedBackMapping
{
    ///// <summary>
    ///// 系统状态
    ///// bit0:    系统已准备好
    ///// bit1:    系统使能状态
    ///// bit2:    系统错误状态
    ///// bit3:    系统运行状态
    ///// bit4:    系统总线状态
    ///// bit5:    系统平台校验状态
    ///// bit6:    轴配置完成，可进行轴序列初始化
    ///// bit7:    运动参数配置完成，可进行系统旧状态恢复
    ///// bit8:    系统恢复旧状态完成
    ///// bit8-31: 预留
    ///// </summary>
    public enum UdiSysStateEnum : uint
    {
        Ready = 1 << 0,
        Enabled = 1 << 1,
        Error = 1 << 2,
        Running = 1 << 3,
        BusState = 1 << 4,
        PlatformCheck = 1 << 5,
        AxisConfigured = 1 << 6,
        MotionParametersConfigured = 1 << 7,
        StateRestored = 1 << 8
    }

    private static readonly Dictionary<uint, string> dic = new()
    {
        { (uint)UdiSysStateEnum.Ready, Lang.SysFeedBackMapping_System_ready },
        { (uint)UdiSysStateEnum.Enabled, Lang.SysFeedBackMapping_System_enable_status },
        { (uint)UdiSysStateEnum.Error, Lang.SysFeedBackMapping_System_error_status },
        { (uint)UdiSysStateEnum.Running, Lang.SysFeedBackMapping_System_running_status },
        { (uint)UdiSysStateEnum.BusState, Lang.SysFeedBackMapping_System_bus_status },
        { (uint)UdiSysStateEnum.PlatformCheck, Lang.SysFeedBackMapping_System_platform_verification },
        { (uint)UdiSysStateEnum.AxisConfigured, Lang.SysFeedBackMapping_Axis_config_completed },
        { (uint)UdiSysStateEnum.MotionParametersConfigured, Lang.SysFeedBackMapping_Motion_param_config_completed },
        { (uint)UdiSysStateEnum.StateRestored, Lang.SysFeedBackMapping_System_state_restored }
    };

    [Obsolete]
    public static string GetUdiSysStateDescription(uint state)
    {
        if (dic.TryGetValue(state, out var description)) return description;
        return state.ToString();
    }

    public static string GetUdiSysErrorCodeOctDesc(uint code)
    {
        StringBuilder result = new();
        string[] statusDescriptions =
        [
            Lang.SysFeedBackMapping_System_ready,
            Lang.SysFeedBackMapping_System_enable_status,
            Lang.SysFeedBackMapping_System_error_status,
            Lang.SysFeedBackMapping_System_running_status,
            Lang.SysFeedBackMapping_System_bus_status,
            Lang.SysFeedBackMapping_System_platform_verification,
            Lang.SysFeedBackMapping_Axis_config_completed,
            Lang.SysFeedBackMapping_Motion_param_config_completed,
            Lang.SysFeedBackMapping_System_state_restored
        ];

        for (var i = 0; i < statusDescriptions.Length; i++)
        {
            // 检查每一位的状态
            var bitValue = (code & (1u << i)) != 0;
            result.Append($"bit{i}: {bitValue}，{statusDescriptions[i]}{Environment.NewLine}");
        }

        // 预留位不需要输出描述，但可以添加一条信息说明
        result.Append(Lang.SysFeedBackMapping_Bit8_31_reserved);

        return result.ToString();
    }


    /// <summary>
    ///     获取 UdiSysState 每个 bit 的 bool 值，并返回包含所有 bool 的结构体
    /// </summary>
    /// <param name="sysState">系统状态的 uint 值</param>
    /// <returns>包含系统状态每个位的布尔值的结构体</returns>
    public static UdiSysStateStruct GetUdiSysStateBits(uint sysState)
    {
        return new UdiSysStateStruct
        {
            Ready = (sysState & (1 << 0)) != 0,
            Enabled = (sysState & (1 << 1)) != 0,
            Error = (sysState & (1 << 2)) != 0,
            Running = (sysState & (1 << 3)) != 0,
            BusStatus = (sysState & (1 << 4)) != 0,
            PlatformVerification = (sysState & (1 << 5)) != 0,
            AxisConfigurationComplete = (sysState & (1 << 6)) != 0,
            MotionParametersConfigured = (sysState & (1 << 7)) != 0,
            StateRestorationComplete = (sysState & (1 << 8)) != 0
        };
    }
}