using System.Windows;
using System.Windows.Controls;
using ModularAT.ViewModels;

namespace ModularAT.Views;

public partial class NoticeListView : UserControl
{
    public NoticeListView()
    {
        InitializeComponent();
    }

    private void ClearAllButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is NoticeListViewModel viewModel)
        {
            viewModel.ClearAllNotices();
        }
    }
}