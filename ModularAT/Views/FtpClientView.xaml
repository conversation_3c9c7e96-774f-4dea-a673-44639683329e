<UserControl
    x:Class="ModularAT.Views.FtpClientView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:ModularAT.ViewModels"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    d:DataContext="{d:DesignInstance viewModels:FtpClientViewModel}"
    d:DesignHeight="600"
    d:DesignWidth="1000"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <conv:BoolInverter x:Key="BoolReverseConverter" />
            <conv:ValueConverterGroup x:Key="BoolReverseToVisibilityConverter">
                <conv:BoolInverter />
                <conv:BoolToVisibilityConverter />
            </conv:ValueConverterGroup>
            <!-- 文件图标模板 -->
            <DataTemplate x:Key="FileIconTemplate">
                <Grid>
                    <iconPacks:PackIconMaterial Kind="File" Visibility="{Binding IsDirectory, Converter={StaticResource BoolReverseToVisibilityConverter}}" />
                    <iconPacks:PackIconMaterial Kind="Folder" Visibility="{Binding IsDirectory, Converter={StaticResource BooleanToVisibilityConverter}}" />
                </Grid>
            </DataTemplate>

            <!-- 文件列表模板 -->
            <DataTemplate x:Key="FileItemTemplate">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <ContentControl Grid.Column="0" ContentTemplate="{StaticResource FileIconTemplate}" Content="{Binding}" Margin="0,0,5,0" />
                    <TextBlock Grid.Column="1" Text="{Binding Name}" VerticalAlignment="Center" />
                    <TextBlock Grid.Column="2" Text="{Binding SizeFormatted}" VerticalAlignment="Center" Margin="5,0" />
                    <TextBlock Grid.Column="3" Text="{Binding ModifiedFormatted}" VerticalAlignment="Center" />
                </Grid>
            </DataTemplate>

            <!-- 传输日志模板 -->
            <DataTemplate x:Key="LogItemTemplate">
                <Grid Margin="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding TimestampFormatted}" Margin="0,0,10,0" />
                    <TextBlock Grid.Column="1" Text="{Binding Operation}" Margin="0,0,10,0" FontWeight="Bold" />
                    <TextBlock Grid.Column="2" Text="{Binding Message}" TextWrapping="Wrap" />
                    <iconPacks:PackIconMaterial Grid.Column="3" Kind="CheckCircle" Foreground="Green" Visibility="{Binding Success, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <iconPacks:PackIconMaterial Grid.Column="3" Kind="CloseCircle" Foreground="Red" Visibility="{Binding Success, Converter={StaticResource BoolReverseToVisibilityConverter}}" />
                </Grid>
            </DataTemplate>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="150" />
        </Grid.RowDefinitions>

        <!-- 连接参数区域 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10" >
            <TextBlock Text="{Binding LanResources.FtpClient_Host, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Text="{Binding Config.Hostname, UpdateSourceTrigger=PropertyChanged}" 
                     Width="120"
                     VerticalAlignment="Center" 
                     IsEnabled="{Binding IsConnected, Converter={StaticResource BoolReverseConverter}}"
                     Margin="0,0,10,0"/>

            <TextBlock Text="{Binding LanResources.FtpClient_Port, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <mah:NumericUpDown Value="{Binding Config.Port}" 
                             Width="80"
                             Minimum="1" Maximum="65535" 
                             VerticalAlignment="Center" 
                             IsEnabled="{Binding IsConnected, Converter={StaticResource BoolReverseConverter}}"
                             Margin="0,0,10,0"/>

            <TextBlock Text="{Binding LanResources.FtpClient_Username, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Text="{Binding Config.Username, UpdateSourceTrigger=PropertyChanged}" 
                     Width="100"
                     VerticalAlignment="Center" 
                     IsEnabled="{Binding IsConnected, Converter={StaticResource BoolReverseConverter}}"
                     Margin="0,0,10,0"/>

            <TextBlock Text="{Binding LanResources.FtpClient_Password, Source={x:Static localization:LocalizationService.Current}}" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <PasswordBox x:Name="PasswordBox" 
                       Width="100"
                       VerticalAlignment="Center" 
                       IsEnabled="{Binding IsConnected, Converter={StaticResource BoolReverseConverter}}"
                       Margin="0,0,10,0"/>

            <Button Command="{Binding ConnectCommand}" 
                  Width="100" 
                  Margin="10,0,0,0">
                <StackPanel Orientation="Horizontal">
                    <iconPacks:PackIconMaterial Kind="ServerNetwork" Margin="0,5,5,0" />
                    <TextBlock Text="{Binding LanResources.FtpClient_Connect, Source={x:Static localization:LocalizationService.Current}}" Visibility="{Binding IsConnected, Converter={StaticResource BoolReverseToVisibilityConverter}}" />
                    <TextBlock Text="{Binding LanResources.FtpClient_Disconnect, Source={x:Static localization:LocalizationService.Current}}" Visibility="{Binding IsConnected, Converter={StaticResource BooleanToVisibilityConverter}}" />
                </StackPanel>
            </Button>
        </StackPanel>

        <!-- 文件浏览区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!-- 远程文件区域 -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding LanResources.FtpClient_Remote_directory, Source={x:Static localization:LocalizationService.Current}}" FontWeight="Bold" />
                    <TextBlock Grid.Column="1" Text="{Binding CurrentRemoteDirectory}" />
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Command="{Binding BackCommand}" ToolTip="{Binding LanResources.FtpClient_Back, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="ArrowLeft" />
                        </Button>
                        <Button Command="{Binding ForwardCommand}" ToolTip="{Binding LanResources.FtpClient_Forward, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="ArrowRight" />
                        </Button>
                        <Button Command="{Binding UpCommand}" ToolTip="{Binding LanResources.FtpClient_Up, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="ArrowUp" />
                        </Button>
                        <Button Command="{Binding LoadRemoteFilesCommand}" ToolTip="{Binding LanResources.FtpClient_Refresh, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="Refresh" />
                        </Button>
                    </StackPanel>
                </Grid>

                <ListView Grid.Row="1" ItemsSource="{Binding RemoteFiles}" ItemTemplate="{StaticResource FileItemTemplate}" 
                          SelectedItem="{Binding SelectedRemoteFile}" BorderThickness="1" BorderBrush="#CCCCCC" Margin="0,5">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="MouseDoubleClick">
                            <b:InvokeCommandAction Command="{Binding OpenRemoteFileCommand}" CommandParameter="{Binding SelectedRemoteFile}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </ListView>

                <!--<StackPanel Grid.Row="2" Orientation="Horizontal">
                    <Button Command="{Binding CreateRemoteDirectoryCommand}" CommandParameter="NewFolder" ToolTip="{Binding LanResources.FtpClient_Create_folder, Source={x:Static localization:LocalizationService.Current}}">
                        <iconPacks:PackIconMaterial Kind="FolderPlus" />
                    </Button>
                    <Button Command="{Binding DeleteRemoteFileCommand}" CommandParameter="{Binding SelectedRemoteFile}" ToolTip="{Binding LanResources.FtpClient_Delete, Source={x:Static localization:LocalizationService.Current}}" >
                        <iconPacks:PackIconMaterial Kind="Delete" />
                    </Button>
                    <Button Command="{Binding DownloadFileCommand}" CommandParameter="{Binding SelectedRemoteFile}" ToolTip="{Binding LanResources.FtpClient_Download_to_local, Source={x:Static localization:LocalizationService.Current}}">
                        <iconPacks:PackIconMaterial Kind="Download" />
                    </Button>
                </StackPanel>-->
            </Grid>

            <!-- 中间传输按钮区域 -->
            <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10,0">
                <Button Command="{Binding DownloadFileCommand}" CommandParameter="{Binding SelectedRemoteFile}">
                    <iconPacks:PackIconMaterial Kind="ArrowRight" />
                </Button>
                <Button Command="{Binding UploadFileCommand}" CommandParameter="{Binding SelectedLocalFile}">
                    <iconPacks:PackIconMaterial Kind="ArrowLeft" />
                </Button>
            </StackPanel>

            <!-- 本地文件区域 -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="{Binding LanResources.FtpClient_Local_directory, Source={x:Static localization:LocalizationService.Current}}" FontWeight="Bold" />
                    <TextBlock Grid.Column="1" Text="{Binding CurrentLocalDirectory}" />
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button Command="{Binding BackLocalCommand}" ToolTip="{Binding LanResources.FtpClient_Back, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="ArrowLeft" />
                        </Button>
                        <Button Command="{Binding ForwardLocalCommand}" ToolTip="{Binding LanResources.FtpClient_Forward, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="ArrowRight" />
                        </Button>
                        <Button Command="{Binding UpLocalCommand}" ToolTip="{Binding LanResources.FtpClient_Up, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="ArrowUp" />
                        </Button>
                        <Button Command="{Binding LoadLocalFilesCommand}" ToolTip="{Binding LanResources.FtpClient_Refresh, Source={x:Static localization:LocalizationService.Current}}" Margin="5,0,0,0">
                            <iconPacks:PackIconMaterial Kind="Refresh" />
                        </Button>
                    </StackPanel>
                </Grid>

                <ListView Grid.Row="1" ItemsSource="{Binding LocalFiles}" ItemTemplate="{StaticResource FileItemTemplate}" 
                          SelectedItem="{Binding SelectedLocalFile}" BorderThickness="1" BorderBrush="#CCCCCC" Margin="0,5">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="MouseDoubleClick">
                            <b:InvokeCommandAction Command="{Binding OpenLocalFileCommand}" CommandParameter="{Binding SelectedLocalFile}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </ListView>
                

                <!--<StackPanel Grid.Row="2" Orientation="Horizontal">
                    <Button Command="{Binding CreateLocalDirectoryCommand}" CommandParameter="NewFolder" ToolTip="{Binding LanResources.FtpClient_Create_folder, Source={x:Static localization:LocalizationService.Current}}">
                        <iconPacks:PackIconMaterial Kind="FolderPlus" />
                    </Button>
                    <Button Command="{Binding DeleteLocalFileCommand}" CommandParameter="{Binding SelectedLocalFile}" ToolTip="{Binding LanResources.FtpClient_Delete, Source={x:Static localization:LocalizationService.Current}}">
                        <iconPacks:PackIconMaterial Kind="Delete" />
                    </Button>
                    <Button Command="{Binding UploadFileCommand}" CommandParameter="{Binding SelectedLocalFile}" ToolTip="{Binding LanResources.FtpClient_Upload_to_server, Source={x:Static localization:LocalizationService.Current}}">
                        <iconPacks:PackIconMaterial Kind="Upload" />
                    </Button>
                </StackPanel>-->
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <!--<TextBlock Grid.Row="2" Text="{Binding StatusMessage}" Margin="0,5" />-->

        <!-- 传输日志区域 -->
        <Grid Grid.Row="3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Text="{Binding LanResources.FtpClient_Transmission_log, Source={x:Static localization:LocalizationService.Current}}" FontWeight="Bold" Margin="0,5,0,5" />
            <ListView Grid.Row="1" ItemsSource="{Binding TransferLogs}" ItemTemplate="{StaticResource LogItemTemplate}" 
                      BorderThickness="1" BorderBrush="#CCCCCC" />
        </Grid>
    </Grid>
</UserControl>