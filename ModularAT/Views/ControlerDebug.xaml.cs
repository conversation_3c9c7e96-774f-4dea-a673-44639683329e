using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using ModularAT.ViewModels;
using TextBox = System.Windows.Controls.TextBox;

namespace ModularAT.Views;

/// <summary>
///     ControlerDebug.xaml 的交互逻辑
/// </summary>
public partial class ControlerDebug : UserControl
{
    public ControlerDebug()
    {
        InitializeComponent();
        DataContext = ViewModelLocator.ControlerDebug;
    }

    public ControlerDebugViewModel ViewModel => ViewModelLocator.ControlerDebug;

    private void TextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        var box = (TextBox)sender;
        box.ScrollToEnd();
    }

    private void TextBox_PreviewMouseWheelHande(object sender, MouseWheelEventArgs e)
    {
        var textBox = sender as TextBox;
        if (textBox != null)
        {
            // 获取滚动条
            var scrollViewer = GetDescendantByType(textBox, typeof(ScrollViewer)) as ScrollViewer;
            if (scrollViewer != null)
            {
                // 根据滚轮方向进行滚动
                if (e.Delta > 0)
                    scrollViewer.LineUp();
                else
                    scrollViewer.LineDown();

                e.Handled = true;
            }
        }
    }

    // 辅助方法：获取指定类型的子元素
    public static Visual GetDescendantByType(Visual element, Type type)
    {
        if (element == null) return null;

        if (element.GetType() == type) return element;

        Visual foundElement = null;
        if (element is FrameworkElement frameworkElement)
            frameworkElement.ApplyTemplate();

        for (var i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
        {
            var visual = VisualTreeHelper.GetChild(element, i) as Visual;
            foundElement = GetDescendantByType(visual, type);
            if (foundElement != null)
                break;
        }

        return foundElement;
    }
}