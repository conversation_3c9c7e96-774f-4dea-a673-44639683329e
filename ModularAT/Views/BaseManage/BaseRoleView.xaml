<UserControl
    x:Class="ModularAT.Views.BaseRoleView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <UserControl.Resources>
        <Thickness x:Key="ControlMargin">0 5 0 0</Thickness>
        <conv:BoolToStringConverter x:Key="IsAddConverter">
            <conv:BoolToStringConverter.TrueValue>添加</conv:BoolToStringConverter.TrueValue>
            <conv:BoolToStringConverter.FalseValue>编辑</conv:BoolToStringConverter.FalseValue>
        </conv:BoolToStringConverter>
    </UserControl.Resources>
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadDataCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Name="Column1" Width="*" />
            <ColumnDefinition Name="Column2" Width="3" />
            <ColumnDefinition Name="Column3" Width="Auto" />
        </Grid.ColumnDefinitions>
        <DockPanel Grid.Row="0" Grid.Column="0">
            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                <TextBox
                    Margin="4,3"
                    mah:TextBoxHelper.ButtonCommand="{Binding SearchCommand, Mode=OneWay}"
                    mah:TextBoxHelper.ButtonCommandParameter="{Binding Path=Text, RelativeSource={RelativeSource Mode=Self}}"
                    mah:TextBoxHelper.Watermark="{Binding LanResources.BaseRole_Enter_keywords, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{DynamicResource MahApps.Styles.TextBox.Search}" />
            </StackPanel>
            <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                <Button
                    Margin="3"
                    Command="{Binding AddCommand}"
                    Content="{Binding LanResources.BaseRole_New, Source={x:Static localization:LocalizationService.Current}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Click">
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column1}"
                                Value="6*" />
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column2}"
                                Value="3" />
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column3}"
                                Value="4*" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=EditPanel}"
                                Value="Visible" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=EditPanel}"
                                Value="Visible" />
                            <i:ChangePropertyAction
                                PropertyName="SelectedIndex"
                                TargetObject="{Binding ElementName=table}"
                                Value="-1" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </Button>
                <Button
                    Margin="3"
                    Command="{Binding LoadDataCommand}"
                    Content="{Binding LanResources.BaseRole_Refresh, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>
        </DockPanel>
        <DataGrid
            Name="table"
            Grid.Row="1"
            Grid.Column="0"
            Margin="3"
            AllowDrop="False"
            AutoGenerateColumns="False"
            CanUserAddRows="False"
            ItemsSource="{Binding Data}"
            Style="{StaticResource MahApps.Styles.DataGrid.Azure}">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Binding="{Binding Name}"
                    Header="{Binding LanResources.BaseRole_Role_name, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Description}"
                    Header="{Binding LanResources.BaseRole_Desc, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding OrderSort}"
                    Header="{Binding LanResources.BaseRole_Level, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding CreateBy}"
                    Header="{Binding LanResources.BaseRole_Creator, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding CreateTime}"
                    Header="{Binding LanResources.BaseRole_Create_time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding ModifyBy}"
                    Header="{Binding LanResources.BaseRole_Modifier, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding ModifyTime}"
                    Header="{Binding LanResources.BaseRole_Mod_time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Enabled}"
                    Header="{Binding LanResources.BaseRole_Is_enabled, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTemplateColumn Header="{Binding LanResources.BaseRole_Op, Source={x:Static localization:LocalizationService.Current}}">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Hyperlink
                                        Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding}"
                                        Foreground="{DynamicResource MahApps.Brushes.Text}">
                                        <TextBlock Text="{Binding LanResources.BaseRole_Edit, Source={x:Static localization:LocalizationService.Current}}" />
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Click">
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column1}"
                                                    Value="6*" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column2}"
                                                    Value="3" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column3}"
                                                    Value="4*" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Visibility"
                                                    TargetObject="{Binding ElementName=EditPanel}"
                                                    Value="Visible" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Hyperlink>
                                </TextBlock>
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Hyperlink
                                        Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding Id}"
                                        Foreground="{DynamicResource MahApps.Brushes.Text}">
                                        <TextBlock Text="{Binding LanResources.BaseRole_Delete, Source={x:Static localization:LocalizationService.Current}}" />
                                    </Hyperlink>
                                </TextBlock>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="MouseDoubleClick">
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column1}"
                        Value="6*" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column2}"
                        Value="3" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column3}"
                        Value="4*" />
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=EditPanel}"
                        Value="Visible" />
                    <i:InvokeCommandAction Command="{Binding EditCommand}"
                                           CommandParameter="{Binding SelectedItem, ElementName=table}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </DataGrid>
        <GridSplitter
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="1"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="Gray"
            ResizeBehavior="PreviousAndNext"
            ResizeDirection="Columns" />
        <StackPanel
            Name="EditPanel"
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="2"
            Orientation="Vertical"
            Visibility="Collapsed">
            <GroupBox Margin="4,2" Header="{Binding IsAdd, Converter={StaticResource IsAddConverter}}">
                <AdornerDecorator>
                    <StackPanel>
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseRole_Role_name, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            Text="{Binding CurrentRole.Name}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseRole_Desc, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            mah:TextBoxHelper.ClearTextButton="True"
                            AcceptsReturn="True"
                            Text="{Binding CurrentRole.Description}"
                            TextWrapping="Wrap" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseRole_Level, Source={x:Static localization:LocalizationService.Current}}" />
                        <mah:NumericUpDown
                            mah:TextBoxHelper.Watermark="{Binding LanResources.BaseRole_Pri_smaller_perm_bigger, Source={x:Static localization:LocalizationService.Current}}"
                            Maximum="10"
                            Minimum="1"
                            Value="{Binding CurrentRole.OrderSort}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseRole_Enable_curr_role, Source={x:Static localization:LocalizationService.Current}}" />
                        <mah:ToggleSwitch Margin="{StaticResource ControlMargin}" IsOn="{Binding CurrentRole.Enabled}" />
                        <Button
                            Width="50"
                            Command="{Binding SaveCommand}"
                            Content="{Binding LanResources.BaseRole_Save, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                        <Button
                            Width="50"
                            Command="{Binding CancelCommand}"
                            Content="{Binding LanResources.BaseRole_Cancel, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                    </StackPanel>
                </AdornerDecorator>
            </GroupBox>
        </StackPanel>
    </Grid>
</UserControl>