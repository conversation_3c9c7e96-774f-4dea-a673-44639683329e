<UserControl
    x:Class="ModularAT.Views.BasePermAssignView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:common="clr-namespace:ModularAT.Common.Helper;assembly=ModularAT.Common"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:mx="http://compiledbindings.com/x"
    mc:Ignorable="d mx">
    <UserControl.Resources>
        <Thickness x:Key="ControlMargin">0 5 0 0</Thickness>
    </UserControl.Resources>
    <!--<i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadDataCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>-->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Name="Column1" Width="30*" />
            <ColumnDefinition Name="Column2" Width="3" />
            <ColumnDefinition Name="Column3" Width="70*" />
        </Grid.ColumnDefinitions>
        <DockPanel Grid.Row="0" Grid.Column="0">
            <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermAssign_Role, Source={x:Static localization:LocalizationService.Current}}" />
            <Button
                Width="60"
                Margin="3"
                Command="{Binding LoadDataCommand}"
                Content="{Binding LanResources.BasePermAssign_Refresh, Source={x:Static localization:LocalizationService.Current}}" />
        </DockPanel>
        <ListView
            x:Name="PermList"
            Grid.Row="1"
            Grid.Column="0"
            Margin="{StaticResource ControlMargin}"
            DockPanel.Dock="Left"
            ItemsSource="{Binding Roles}">
            <ListView.ItemTemplate>
                <DataTemplate>
                    <TextBlock Text="{Binding Name}" />
                </DataTemplate>
            </ListView.ItemTemplate>
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="SelectionChanged">
                    <i:InvokeCommandAction Command="{Binding SelectedRoleCommand}"
                                           CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ListView}, Path=SelectedItem.Id}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </ListView>
        <GridSplitter
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="1"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="Gray"
            ResizeBehavior="PreviousAndNext"
            ResizeDirection="Columns" />
        <DockPanel Grid.Row="0" Grid.Column="2">
            <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermAssign_Perm, Source={x:Static localization:LocalizationService.Current}}" />
            <Button
                Width="60"
                Margin="3"
                Command="{Binding SaveCommand}"
                Content="{Binding LanResources.BasePermAssign_Save, Source={x:Static localization:LocalizationService.Current}}" />
        </DockPanel>
        <TreeView
            x:Name="menuTreeView"
            Grid.Row="1"
            Grid.Column="2"
            Margin="{StaticResource ControlMargin}"
            ItemsSource="{x:Bind ViewModel.PermTree.Children}">
            <TreeView.ItemContainerStyle>
                <Style BasedOn="{StaticResource MahApps.Styles.TreeViewItem}" TargetType="TreeViewItem">
                    <Setter Property="IsExpanded" Value="True" />
                    <Setter Property="Margin" Value="0,10,0,0" />
                </Style>
            </TreeView.ItemContainerStyle>
            <TreeView.ItemTemplate>
                <HierarchicalDataTemplate DataType="common:PermissionTree" ItemsSource="{Binding Children}">
                    <StackPanel>
                        <CheckBox Content="{x:Bind Label}" IsChecked="{x:Bind IsChecked, Mode=TwoWay}" />
                        <WrapPanel>
                            <ItemsControl ItemsSource="{x:Bind Btns}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal" />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate DataType="common:PermissionTree">
                                        <CheckBox
                                            Margin="20,0,0,0"
                                            Content="{x:Bind Label}"
                                            IsChecked="{x:Bind IsChecked, Mode=TwoWay}" />
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </WrapPanel>
                    </StackPanel>
                </HierarchicalDataTemplate>
            </TreeView.ItemTemplate>
        </TreeView>
    </Grid>
</UserControl>