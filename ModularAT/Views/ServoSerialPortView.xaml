<UserControl
    x:Class="ModularAT.Views.ServoSerialPortView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:ModularAT.ViewModels"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    d:DataContext="{d:DesignInstance viewModels:ServoSerialPortViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <StackPanel HorizontalAlignment="Left" >
        <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}"
                   Text="{Binding LanResources.ServoSerialPort_Driver_conn, Source={x:Static localization:LocalizationService.Current}}" />
        <StackPanel MinWidth="180" Margin="10">

            <TextBlock 
                       Text="{Binding LanResources.ServoSerialPort_Serial_port, Source={x:Static localization:LocalizationService.Current}}" />
            <ComboBox ItemsSource="{Binding SerialPortConfig.PortList}"
                      SelectedItem="{Binding SerialPortConfig.SelectedPort}">
                <b:Interaction.Triggers>
                    <b:EventTrigger EventName="DropDownOpened">
                        <b:InvokeCommandAction Command="{Binding SerialPortConfig.RefreshPortsCommand}"/>
                    </b:EventTrigger>
                </b:Interaction.Triggers>
            </ComboBox>
            <TextBlock Margin="{StaticResource ControlMargin}"
                       Text="{Binding LanResources.ServoSerialPort_Baud_rate, Source={x:Static localization:LocalizationService.Current}}" />
            <ComboBox ItemsSource="{Binding SerialPortConfig.BaudRateList}"
                      SelectedItem="{Binding SerialPortConfig.SelectedBaudRate}" />
            <TextBlock Margin="{StaticResource ControlMargin}"
                       Text="{Binding LanResources.ServoSerialPort_Data_bits, Source={x:Static localization:LocalizationService.Current}}" />
            <ComboBox ItemsSource="{Binding SerialPortConfig.DataBitsList}"
                      SelectedItem="{Binding SerialPortConfig.SelectedDataBits}" />
            <TextBlock Margin="{StaticResource ControlMargin}"
                       Text="{Binding LanResources.ServoSerialPort_Parity_bit, Source={x:Static localization:LocalizationService.Current}}" />
            <ComboBox ItemsSource="{Binding SerialPortConfig.ParityBitsList}"
                      SelectedItem="{Binding SerialPortConfig.SelectedParity}" />
            <TextBlock Margin="{StaticResource ControlMargin}"
                       Text="{Binding LanResources.ServoSerialPort_Stop_bits, Source={x:Static localization:LocalizationService.Current}}" />
            <ComboBox ItemsSource="{Binding SerialPortConfig.StopBitsList}"
                      SelectedItem="{Binding SerialPortConfig.SelectedStopBits}" />
            <Button  Command="{Binding Path=ConnectPortCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=Data.IsConnectedServo}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Connection}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSerialPort_Connect, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button  Command="{Binding Path=ClosePortCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=Data.IsConnectedServo}" Value="False">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=LanDisconnect}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSerialPort_Disconnect, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
        </StackPanel>
    </StackPanel>
</UserControl>