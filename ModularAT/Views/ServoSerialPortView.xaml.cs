using System.Windows.Controls;
using ModularAT.ViewModels;

namespace ModularAT.Views;

/// <summary>
///     ServoSerialPort.xaml 的交互逻辑
/// </summary>
public partial class ServoSerialPortView : UserControl
{
    public ServoSerialPortView()
    {
        InitializeComponent();
        DataContext = ViewModelLocator.ServoSerialPortSetting;
    }

    public ServoSerialPortViewModel ViewModel => (ServoSerialPortViewModel)DataContext;
}