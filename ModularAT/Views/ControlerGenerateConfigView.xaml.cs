using System.Windows.Controls;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.ViewModels;

namespace ModularAT.Views;

/// <summary>
///     ControlerGenerateConfig.xaml 的交互逻辑
/// </summary>
public partial class ControlerGenerateConfigView : UserControl
{
    public ControlerGenerateConfigView()
    {
        InitializeComponent();
    }

    public ControlerGenerateConfigViewModel ViewModel =>
        App.Current.Services.GetRequiredService<ControlerGenerateConfigViewModel>();
}