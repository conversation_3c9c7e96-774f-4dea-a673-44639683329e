using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using ModularAT.Driver.Servo;
using ModularAT.Entity;
using ModularAT.ViewModels;

namespace ModularAT.Views;

/// <summary>
///     ServoSetting.xaml 的交互逻辑
/// </summary>
public partial class ServoSettingView : UserControl
{
    public ServoSettingView()
    {
        InitializeComponent();
        DataContext = ViewModelLocator.ServoSetting;
        //DataGrid分组

        var cvs = CollectionViewSource.GetDefaultView(ViewModel.Data.ParamItems);
        if (cvs.GroupDescriptions.Count == 0) cvs.GroupDescriptions.Add(new PropertyGroupDescription("Group"));
    }


    public ServoSettingViewModel ViewModel => (ServoSettingViewModel)DataContext;

    private void paraDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (sender is DataGrid dataGrid && dataGrid.SelectedItem is ParameterModel select && select != null)
        {
            var group = Convert.ToByte(select.Group.Substring(0, 1));
            if (group >= 5) //错误记录跳过，具体看SecondDeviceDataSetCode就懂了
                group--;
            ViewModel.SelectFirmwareParaSet = (SecondDeviceDataSetCode)group;
        }
    }

    /// <summary>
    /// 滚动到指定的参数项
    /// </summary>
    /// <param name="parameter">要滚动到的参数项</param>
    public void ScrollToParameter(ParameterModel parameter)
    {
        if (parameter != null)
        {
            paraDataGrid.ScrollIntoView(parameter);
            paraDataGrid.SelectedItem = parameter;

            // 延迟一点时间确保滚动完成后再聚焦
            Dispatcher.BeginInvoke(new Action(() =>
            {
                var row = paraDataGrid.ItemContainerGenerator.ContainerFromItem(parameter) as DataGridRow;
                if (row != null)
                {
                    row.Focus();
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }
    }
}