using System.Windows.Controls;
using ModularAT.ViewModels;

namespace ModularAT.Views;

/// <summary>
///     ControlerAxis.xaml 的交互逻辑
/// </summary>
public partial class ControlerTranStatusView : UserControl
{
    public ControlerTranStatusView()
    {
        InitializeComponent();
        DataContext = ViewModelLocator.ControlerTranStatus;
    }

    public ControlerTranStatusViewModel ViewModel => ViewModelLocator.ControlerTranStatus;
}