<UserControl
    x:Class="ModularAT.Views.ServoSettingView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vc="clr-namespace:ModularAT.Converters"
    xmlns:vm="clr-namespace:ModularAT.ViewModels"
    xmlns:toolkit="http://schemas.xceed.com/wpf/xaml/toolkit"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:valueConverter="clr-namespace:ModularAT.ValueConverter"
    d:DataContext="{d:DesignInstance Type=vm:ServoSettingViewModel}"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--<vc:TransposeParaConverter x:Key="TransposeParaConverter" />-->
            <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                   TargetType="{x:Type TypeName=Button}">
                <Setter Property="Margin"
                        Value="0,10,0,0" />
                <Setter Property="Width"
                        Value="180" />
                <Setter Property="HorizontalAlignment"
                        Value="Left" />
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding RequestAuthCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}"
                   Text="{Binding LanResources.ServoSetting_Driver_params, Source={x:Static localization:LocalizationService.Current}}" />
        <ScrollViewer Grid.Row="1"
                      Grid.Column="0">
            <StackPanel
                HorizontalAlignment="Left"

                MinWidth="180"
                Margin="5">

                <TextBlock
                    Margin="2"
                    Text="{Binding LanResources.ServoSetting_Sel_op, Source={x:Static localization:LocalizationService.Current}}" />
                <Button
                    helper:PermissionHelper.HasPerm="/ServoSetting/SetPara"
                    Command="{Binding Path=SetParaCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=Select}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.ServoSetting_Sel_write, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/SetParamsAll"
                        Command="{Binding Path=SetParamsAllCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=SelectAll}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.ServoSetting_Write_all, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/SetParamsAll"
                        Command="{Binding Path=ImportCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=Download}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.Scope_Import, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/SetParamsAll"
                        Command="{Binding Path=ExportCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=Upload}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.Scope_Export, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/ParaClear"
                        Command="{Binding Path=ParaClearCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=KeyboardReturn}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.ServoSetting_Restore_def_params, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/ErrorReset"
                        Command="{Binding Path=ErrorResetCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=Refresh}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.ServoSetting_Err_reset, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/SoftReset"
                        Command="{Binding Path=SoftResetCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=BackupRestore}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.ServoSetting_System_soft_reset, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <Button helper:PermissionHelper.HasPerm="/ServoSetting/ErrorRecordClear"
                        Command="{Binding Path=ErrorRecordClearCommand}">
                    <Button.Content>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                Grid.Row="0"
                                Grid.Column="0"
                                Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform"
                                                 Visual="{iconPacks:Material Kind=NotificationClearAll}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="1"
                                Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                                Text="{Binding LanResources.ServoSetting_Fault_rec_clear, Source={x:Static localization:LocalizationService.Current}}" />
                        </Grid>
                    </Button.Content>
                </Button>

                <!--  驱动模式设置  -->
                <StackPanel helper:PermissionHelper.HasPerm="/ServoSetting/DriveMode" Margin="0,20,0,0">

                    <TextBlock
                        Margin="2,10"
                        Style="{StaticResource TextBlockSubTitle}"
                        HorizontalAlignment="Left"
                        Text="{Binding LanResources.ServoSetting_Drive_mode_set, Source={x:Static localization:LocalizationService.Current}}" />

                    <Label
                        Content="{Binding LanResources.ServoSetting_Ctrl_right, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox
                        DisplayMemberPath="Key"
                        ItemsSource="{Binding DriverControlBy}"
                        SelectedValue="{Binding DriverMode.ControlBy}"
                        SelectedValuePath="Value">
                        <i:Interaction.Triggers>
                            <i:EventTrigger EventName="SelectionChanged">
                                <i:CallMethodAction MethodName="SetDriverControlBy"
                                                    TargetObject="{Binding}" />
                            </i:EventTrigger>
                        </i:Interaction.Triggers>
                    </ComboBox>

                    <Label
                        Content="{Binding LanResources.ServoSetting_Local_ctrl_mode, Source={x:Static localization:LocalizationService.Current}}">
                        <Label.Style>
                            <Style TargetType="Label">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="0">
                                        <Setter Property="Visibility"
                                                Value="Collapsed" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="1">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Label.Style>
                    </Label>
                    <ComboBox
                        DisplayMemberPath="Key"
                        ItemsSource="{Binding DriverLocalMode}"
                        SelectedValue="{Binding DriverMode.Mode}"
                        SelectedValuePath="Value">
                        <ComboBox.Style>
                            <Style BasedOn="{StaticResource DefaultComboBox}"
                                   TargetType="ComboBox">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="0">
                                        <Setter Property="Visibility"
                                                Value="Collapsed" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="1">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ComboBox.Style>
                    </ComboBox>

                    <Label
                        Content="{Binding LanResources.ServoSetting_Sub_mode, Source={x:Static localization:LocalizationService.Current}}">
                        <Label.Style>
                            <Style TargetType="Label">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="0">
                                        <Setter Property="Visibility"
                                                Value="Collapsed" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="1">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Label.Style>
                    </Label>
                    <ComboBox
                        Name="submode"
                        DisplayMemberPath="Key"
                        SelectedIndex="{Binding DriverMode.SubMode}"
                        SelectedValuePath="Value">
                        <ComboBox.Style>
                            <Style BasedOn="{StaticResource DefaultComboBox}"
                                   TargetType="ComboBox">
                                <Setter Property="ItemsSource"
                                        Value="{Binding DriverSubModeNomal}" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="0">
                                        <Setter Property="Visibility"
                                                Value="Collapsed" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding DriverMode.ControlBy}"
                                                 Value="1">
                                        <Setter Property="Visibility"
                                                Value="Visible" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding DriverMode.Mode}"
                                                 Value="0">
                                        <Setter Property="ItemsSource"
                                                Value="{Binding DriverSubModeNomal}" />
                                        <Setter Property="SelectedIndex"
                                                Value="0" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding DriverMode.Mode}"
                                                 Value="1">
                                        <Setter Property="ItemsSource"
                                                Value="{Binding DriverSubModeTest}" />
                                        <Setter Property="SelectedIndex"
                                                Value="0" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ComboBox.Style>
                        <i:Interaction.Triggers>
                            <i:EventTrigger EventName="SelectionChanged">
                                <i:InvokeCommandAction Command="{Binding SetDriverModeCommand}" />
                            </i:EventTrigger>
                        </i:Interaction.Triggers>
                    </ComboBox>
                </StackPanel>
                <!-- CDM驱动器参数设置(高速混合线-驱动器) -->
                <StackPanel helper:PermissionHelper.HasPerm="/ServoSetting/CDM" Margin="0,20,0,0">

                    <TextBlock
                        Margin="2,10"
                        Text="CDM驱动器参数设置:" Style="{StaticResource TextBlockSubTitle}" HorizontalAlignment="Left" />

                    <Label
                        Content="目标停止位置:" />
                    <mah:NumericUpDown x:Name="cdmNumericUpDown" Value="0" Minimum="0">
                    </mah:NumericUpDown>
                    <!-- 添加发送按钮 -->
                    <Button Content="发送"
                            Command="{Binding Path=SetCdmTargetPositionCommand}"
                            Style="{StaticResource DefaultButton}"
                            CommandParameter="{Binding Value, ElementName=cdmNumericUpDown}"
                            Margin="0,5,0,0">
                    </Button>

                    <Label
                        Content="动子检测位置:" />
                    <mah:NumericUpDown x:Name="movercheckPos" Value="0" Minimum="0">
                    </mah:NumericUpDown>
                    <!-- 添加发送按钮 -->
                    <Button Content="发送"
                            Command="{Binding Path=SetCdmMoverCheckPositionCommand}"
                            Style="{StaticResource DefaultButton}"
                            CommandParameter="{Binding Value, ElementName=movercheckPos}"
                            Margin="0,5,0,0">
                    </Button>


                    <Label
                        Content="使能电机位置:" />
                    <mah:NumericUpDown x:Name="motorPos" Value="0" Minimum="0">
                    </mah:NumericUpDown>
                    <!-- 添加发送按钮 -->
                    <Button Content="发送"
                            Command="{Binding Path=SetCdmFocEnsPositionCommand}"
                            Style="{StaticResource DefaultButton}"
                            CommandParameter="{Binding Value, ElementName=motorPos}"
                            Margin="0,5,0,0">
                    </Button>

                </StackPanel>
            </StackPanel>
        </ScrollViewer>
        <DataGrid
            x:Name="paraDataGrid"
            Grid.Row="1"
            Grid.Column="1"
            Margin="10,0,10,0"
            AutoGenerateColumns="False"
            CanUserAddRows="False"
            CanUserDeleteRows="False"
            CanUserReorderColumns="False"
            CanUserResizeRows="False"
            CanUserSortColumns="False"
            GridLinesVisibility="All"
            ItemsSource="{Binding Data.ParamItems}"
            RowHeaderWidth="0"
            SelectionChanged="paraDataGrid_SelectionChanged"
            SelectionMode="Single"
            SelectionUnit="FullRow"
            ScrollViewer.CanContentScroll="True">
            <!--  Height="{Binding ActualHeight, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=Border}}"  -->
            <DataGrid.Resources>
                <!-- DataGrid单元格样式 -->
                <Style TargetType="{x:Type DataGridCell}">
                    <Style.Triggers>
                        <Trigger Property="Validation.HasError" Value="True">
                            <Setter Property="BorderBrush" Value="Red" />
                            <Setter Property="BorderThickness" Value="2" />
                            <Setter Property="Background" Value="#FFFFE6E6" />
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="3x">
                                        <ColorAnimation
                                            Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                            From="Red"
                                            To="Transparent"
                                            Duration="0:0:0.5"
                                            AutoReverse="True" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                        </Trigger>
                    </Style.Triggers>
                </Style>

                <!-- TextBox样式用于EditString列 -->
                <Style x:Key="EditStringTextBoxStyle" TargetType="{x:Type TextBox}">
                    <Setter Property="Padding" Value="2" />
                    <Setter Property="VerticalAlignment" Value="Center" />
                    <Setter Property="BorderThickness" Value="1" />
                    <Setter Property="BorderBrush" Value="LightGray" />
                    <Setter Property="Background" Value="White" />
                    <Setter Property="Validation.ErrorTemplate">
                        <Setter.Value>
                            <ControlTemplate>
                                <DockPanel LastChildFill="True">
                                    <Border BorderBrush="Red" BorderThickness="2">
                                        <AdornedElementPlaceholder />
                                    </Border>
                                </DockPanel>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <Trigger Property="Validation.HasError" Value="True">
                            <Setter Property="ToolTip">
                                <Setter.Value>
                                    <Binding RelativeSource="{RelativeSource Self}"
                                             Path="(Validation.Errors).CurrentItem.ErrorContent" />
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Background" Value="#FFFFE6E6" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter Property="BorderBrush" Value="Blue" />
                            <Setter Property="BorderThickness" Value="2" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.Resources>
            <DataGrid.GroupStyle>
                <GroupStyle>
                    <GroupStyle.ContainerStyle>
                        <Style TargetType="{x:Type GroupItem}">
                            <Setter Property="Margin"
                                    Value="0,0,0,5" />
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="{x:Type GroupItem}">
                                        <Expander
                                            Background="AliceBlue"
                                            BorderBrush="AliceBlue"
                                            BorderThickness="1,1,1,5"
                                            IsExpanded="True">
                                            <Expander.Header>
                                                <DockPanel>
                                                    <TextBlock VerticalAlignment="Center">
                                                        <TextBlock.Text>
                                                            <MultiBinding StringFormat="{}{0} - {1}">
                                                                <Binding Mode="OneWay"
                                                                         Path="Name" />
                                                                <Binding
                                                                    Converter="{valueConverter:ItemCountConverter}"
                                                                    Mode="OneWay"
                                                                    Path="ItemCount" />
                                                            </MultiBinding>
                                                        </TextBlock.Text>
                                                    </TextBlock>
                                                </DockPanel>
                                            </Expander.Header>
                                            <Expander.Content>
                                                <ItemsPresenter />
                                            </Expander.Content>
                                        </Expander>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </GroupStyle.ContainerStyle>
                </GroupStyle>
            </DataGrid.GroupStyle>
            <DataGrid.Columns>
                <!--<DataGridTemplateColumn Header="{Binding LanResources.ServoSetting_Select, Source={x:Static localization:LocalizationService.Current}}">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox VerticalAlignment="Center" IsChecked="{Binding IsSelected, Mode=TwoWay}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>-->
                <!--<DataGridCheckBoxColumn Binding="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Header="{Binding LanResources.ServoSetting_Select, Source={x:Static localization:LocalizationService.Current}}" />-->
                <DataGridTextColumn
                    Binding="{Binding No}"
                    Header="No."
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Name}"
                    Header="{Binding LanResources.ServoSetting_Param_name, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding DataType}"
                    Header="{Binding LanResources.ServoSetting_Set_type, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding MinValue}"
                    Header="{Binding LanResources.ServoSetting_Min_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding MaxValue}"
                    Header="{Binding LanResources.ServoSetting_Max_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Path=ReadValue}"
                    Header="{Binding LanResources.ServoSetting_Read_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTemplateColumn
                    Header="{Binding LanResources.ServoSetting_Set_val, Source={x:Static localization:LocalizationService.Current}}">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding EditString}"
                                       VerticalAlignment="Center"
                                       Padding="2">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Value="False">
                                                <DataTrigger.Binding>
                                                    <MultiBinding Converter="{x:Static vc:ParameterPermissionDisplayConverter.Instance}">
                                                        <Binding />
                                                        <Binding Path="DataContext" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                                                    </MultiBinding>
                                                </DataTrigger.Binding>
                                                <Setter Property="Foreground" Value="Gray" />
                                                <Setter Property="ToolTip" Value="当前用户没有权限修改此参数" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <TextBox
                                Text="{Binding EditString, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                x:Name="EditTextBox">
                                <TextBox.IsReadOnly>
                                    <MultiBinding Converter="{x:Static vc:ParameterPermissionConverter.Instance}">
                                        <Binding />
                                        <Binding Path="DataContext" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                                    </MultiBinding>
                                </TextBox.IsReadOnly>
                                <TextBox.Style>
                                     <Style TargetType="TextBox" BasedOn="{StaticResource EditStringTextBoxStyle}">
                                         <Style.Triggers>
                                             <DataTrigger Value="False">
                                                 <DataTrigger.Binding>
                                                     <MultiBinding Converter="{x:Static vc:ParameterPermissionDisplayConverter.Instance}">
                                                         <Binding />
                                                         <Binding Path="DataContext" RelativeSource="{RelativeSource AncestorType=UserControl}" />
                                                     </MultiBinding>
                                                 </DataTrigger.Binding>
                                                 <Setter Property="Background" Value="LightGray" />
                                                 <Setter Property="ToolTip" Value="当前用户没有权限修改此参数" />
                                             </DataTrigger>
                                         </Style.Triggers>
                                     </Style>
                                 </TextBox.Style>
                            </TextBox>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <!--<DataGridTemplateColumn Header="{Binding LanResources.ServoSetting_Set_val, Source={x:Static localization:LocalizationService.Current}}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBox
                                        VerticalAlignment="Center"
                                        Text="{Binding Int64Value, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        Visibility="{Binding Access, Converter={StaticResource PermissionConverter}}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>-->

                <!--<mah:DataGridNumericUpDownColumn Header="{Binding LanResources.ServoSetting_Set_val, Source={x:Static localization:LocalizationService.Current}}">
                        <mah:DataGridNumericUpDownColumn.Binding>
                            <MultiBinding Converter="{StaticResource InputConverter}">
                                <Binding Path="EditString" />
                                <Binding Path="MinValue" />
                                <Binding Path="MaxValue" />
                                <Binding Path="Coefficient" />
                            </MultiBinding>
                        </mah:DataGridNumericUpDownColumn.Binding>
                    </mah:DataGridNumericUpDownColumn>-->
                <DataGridTextColumn
                    Binding="{Binding Access}"
                    Header="{Binding LanResources.ServoSetting_Perm, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Coefficient}"
                    Header="{Binding LanResources.ServoSetting_Coeff, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <!--<DataGridCheckBoxColumn Binding="{Binding IsMonitorVar, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Header="{Binding LanResources.ServoSetting_Monitor, Source={x:Static localization:LocalizationService.Current}}" />-->
                <!--<DataGridTemplateColumn Header="{Binding LanResources.ServoSetting_Monitor, Source={x:Static localization:LocalizationService.Current}}">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox VerticalAlignment="Center" IsChecked="{Binding IsMonitorVar, Mode=TwoWay}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>-->
                <DataGridTextColumn
                    Binding="{Binding Description}"
                    Header="{Binding LanResources.ServoSetting_Desc, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />

            </DataGrid.Columns>
            <DataGrid.RowStyle>

                <Style BasedOn="{StaticResource MahApps.Styles.DataGridRow}"
                       TargetType="{x:Type DataGridRow}">
                    <Setter Property="Visibility"
                            Value="Visible" />
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsHide}"
                                     Value="1">
                            <Setter Property="Visibility"
                                    Value="Collapsed" />
                        </DataTrigger>
                        <!-- <DataTrigger Binding="{Binding Price, Mode=OneWay, Converter={StaticResource AlbumPriceIsTooMuchConverter}}" Value="True"> -->
                        <!--     <Setter Property="Background" Value="#FF8B8B" /> -->
                        <!--     <Setter Property="Foreground" Value="DarkRed" /> -->
                        <!-- </DataTrigger> -->
                        <!-- <MultiDataTrigger> -->
                        <!--     <MultiDataTrigger.Conditions> -->
                        <!--         <Condition Binding="{Binding Price, Mode=OneWay, Converter={StaticResource AlbumPriceIsTooMuchConverter}}" Value="True" /> -->
                        <!--         <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="true" /> -->
                        <!--     </MultiDataTrigger.Conditions> -->
                        <!--     <Setter Property="Background" Value="#FFBDBD" /> -->
                        <!-- </MultiDataTrigger> -->
                    </Style.Triggers>
                </Style>
            </DataGrid.RowStyle>
            <!--<DataGrid.RowValidationRules>
                  <vc:AlbumPriceIsReallyTooMuchValidation ValidatesOnTargetUpdated="True" ValidationStep="CommittedValue" />
                  <vc:AlbumPriceIsReallyTooMuchValidation ValidatesOnTargetUpdated="True" ValidationStep="UpdatedValue" />
              </DataGrid.RowValidationRules>-->
        </DataGrid>

    </Grid>
</UserControl>