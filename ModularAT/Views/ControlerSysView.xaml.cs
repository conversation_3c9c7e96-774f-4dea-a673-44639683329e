using System.ComponentModel;
using System.Windows.Controls;
using System.Windows.Data;
using ModularAT.Driver.Controller;
using ModularAT.ViewModels;

namespace ModularAT.Views;

/// <summary>
///     ControlerAxis.xaml 的交互逻辑
/// </summary>
public partial class ControlerSysView : UserControl
{
    public ControlerSysView()
    {
        InitializeComponent();
    }

    public ControlerSysViewModel ViewModel => ViewModelLocator.ControlerSys;
}