<UserControl
    x:Class="ModularAT.Views.ControlerSysView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:controller="clr-namespace:ModularAT.Driver.Controller;assembly=ModularAT.Driver.Controller"
    xmlns:setting="clr-namespace:ModularAT.Service.Setting;assembly=ModularAT.Service.Setting"
    xmlns:controllermodel="clr-namespace:ModularAT.Entity.Controller;assembly=ModularAT.Entity"
    xmlns:conv2="clr-namespace:ModularAT.ValueConverter"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:local="clr-namespace:ModularAT.Views"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mapper="clr-namespace:ModularAT.Mapper"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    xmlns:common="clr-namespace:ModularAT.UiCommon"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance Type=viewmodels:ControlerSysViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <CollectionViewSource x:Key="SysCtrlCmdsViewSource"
                                  Source="{x:Static controller:ControllerConst.SysCtrlCmds}" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition  Height="Auto" />
        </Grid.RowDefinitions>
        <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Style="{StaticResource ResourceKey=HeaderTextBlock}"
                       Text="{Binding LanResources.ControlerSys_Sys_ctrl, Source={x:Static localization:LocalizationService.Current}}" />

        <Grid Grid.Row="1" Grid.Column="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <StackPanel MinWidth="180" Grid.Column="0" Margin="10">
                <!--  动子  -->
                <TextBlock
                        Text="{Binding LanResources.ControlerSys_Ctrl_obj, Source={x:Static localization:LocalizationService.Current}}"
                        Visibility="Collapsed" />
                <ComboBox
                        IsReadOnly="True"
                        SelectedIndex="{Binding ICtrlObjSelected}"
                        Visibility="Collapsed">
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Mover, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Rotary_motor, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Linear_motor, Source={x:Static localization:LocalizationService.Current}}" />
                </ComboBox>

                <!--  系统运行模式  -->
                <TextBlock
                        Margin="5,0,5,5"
                        Text="{Binding LanResources.ControlerSys_Sys_op_mode, Source={x:Static localization:LocalizationService.Current}}" />
                <ComboBox
                        SelectedIndex="{Binding ISysRunModeSelected}">
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Axis_teach, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Conn_teach, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Auto_op, Source={x:Static localization:LocalizationService.Current}}" />
                </ComboBox>

                <!--  自动运行模式  -->
                <TextBlock
                        Text="{Binding LanResources.ControlerSys_Auto_op_mode, Source={x:Static localization:LocalizationService.Current}}" />
                <ComboBox
                        SelectedIndex="{Binding ISysAutoRunModeSelected}">
                    <ComboBoxItem Content="0" />
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Sync, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                            Content="{Binding LanResources.ControlerSys_Async, Source={x:Static localization:LocalizationService.Current}}" />
                </ComboBox>

                <!--  速度百分比  -->
                <TextBlock
                        Text="{Binding LanResources.ControlerSys_Speed_perc, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                        Value="{Binding ISysRunVelRatio}" />

                <!--  从站节点ID  -->
                <TextBlock
                        Text="{Binding LanResources.ControlerSys_Slave_node_id, Source={x:Static localization:LocalizationService.Current}}" />
                <ComboBox
                        SelectedIndex="{Binding IDrvIDSelected}"
                        ItemsSource="{x:Bind common:GlobalParamsHelper.GetSlaveNodes(true)}">
                </ComboBox>
                <!--  控制模式  -->
                <TextBlock
                        Text="{Binding LanResources.ControlerSys_Ctrl_mode, Source={x:Static localization:LocalizationService.Current}}" />
                <!-- ... existing code ... -->
                <ComboBox
                        ItemsSource="{x:Bind controller:ControllerConst.SysCtrlCmds}"
                        SelectedValuePath="Item2"
                        DisplayMemberPath="Item1"

                        SelectedValue="{Binding UiSysCtrlSelected}">
                    <i:Interaction.Triggers>
                        <!-- 鼠标悬停时刷新 CollectionViewSource -->
                        <i:EventTrigger EventName="MouseEnter">
                            <i:CallMethodAction
                                    MethodName="Refresh"
                                    TargetObject="{Binding Source={StaticResource SysCtrlCmdsViewSource}, Path=View}" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </ComboBox>
                <!-- ... existing code ... -->

                <TextBlock
                        Text="{Binding LanResources.ControlerSys_Sel_op, Source={x:Static localization:LocalizationService.Current}}" />
                <StackPanel
                        Orientation="Horizontal">
                    <Button
                            helper:PermissionHelper.HasPerm="/ControlerSys/Execute"
                            Command="{Binding ExecuteCommand}"
                            Content="{Binding LanResources.ControlerSys_Exec, Source={x:Static localization:LocalizationService.Current}}" />
                    <Button
                            Command="{Binding ReadSystemFeedBackCommand}"
                            Content="{Binding LanResources.ControlerSys_Read, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Hidden" />
                </StackPanel>

            </StackPanel>
        </Grid>

        <ListView
            Grid.Row="1"
            Grid.Column="1"
            Margin="2"
            Background="Transparent"
            ItemsSource="{x:Bind ViewModel.SysFeedBacks, Mode=OneWay}"
            SelectedIndex="0"
            HorizontalAlignment="Right"
            Style="{StaticResource MahApps.Styles.ListView.Virtualized}">
            <ListView.ItemContainerStyle>
                <Style BasedOn="{StaticResource MahApps.Styles.ListViewItem}" TargetType="{x:Type ListViewItem}">
                    <Setter Property="mah:ItemHelper.HoverBackgroundBrush" Value="Transparent" />
                    <Setter Property="mah:ItemHelper.HoverSelectedBackgroundBrush" Value="Transparent" />
                    <Setter Property="mah:ItemHelper.SelectedBackgroundBrush" Value="Transparent" />
                    <Setter Property="mah:ItemHelper.ActiveSelectionBackgroundBrush" Value="Transparent" />
                    <Setter Property="Background" Value="Transparent" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerSys_Sys_err_axis_id, Source={x:Static localization:LocalizationService.Current}}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controllermodel:SysFeedBackModel">
                                <TextBlock Text="{x:Bind ISysErrorAxisID, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerSys_Sys_err_driver, Source={x:Static localization:LocalizationService.Current}}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controllermodel:SysFeedBackModel">
                                <TextBlock Text="{x:Bind ISysErrorDrvID, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerSys_Sys_err_code, Source={x:Static localization:LocalizationService.Current}}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controllermodel:SysFeedBackModel">
                                <TextBlock Text="{x:Bind UdiSysDrvErrorCode, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerSys_System_drive_error, Source={x:Static localization:LocalizationService.Current}}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controllermodel:SysFeedBackModel">
                                <TextBlock Text="{x:Bind SSysDrvErrName, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerSys_Sys_err_num, Source={x:Static localization:LocalizationService.Current}}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controllermodel:SysFeedBackModel">
                                <TextBlock
                                    Text="{x:Bind setting:ControllerSettingService.GetErrorCodeDescription(UdiSysErrorCode), Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerSys_Sys_stat, Source={x:Static localization:LocalizationService.Current}}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controllermodel:SysFeedBackModel">
                                <TextBlock
                                    Text="{x:Bind UdiSysState, Mode=OneWay}"
                                    ToolTipService.HorizontalOffset="10"
                                    ToolTipService.Placement="Mouse"
                                    ToolTipService.ToolTip="{x:Bind mapper:SysFeedBackMapping.GetUdiSysErrorCodeOctDesc(UdiSysState)}"
                                    ToolTipService.VerticalOffset="10" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
        <local:ControlerDebug
            Grid.Row="2"
            Grid.ColumnSpan="2"
            DataContext="{Binding DebugViewModel}" />
    </Grid>
</UserControl>