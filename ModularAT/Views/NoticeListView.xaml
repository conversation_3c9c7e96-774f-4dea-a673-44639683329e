<UserControl
    x:Class="ModularAT.Views.NoticeListView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:ModularAT.ViewModels"
    xmlns:converter="clr-namespace:ModularAT.ValueConverter"
    d:DataContext="{d:DesignInstance Type=vm:NoticeListViewModel}"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <!-- 值转换器 -->
            <converter:LevelToStyleConverter x:Key="LevelToStyleConverter" />
            
            <!-- 通知项样式 -->
            <Style x:Key="BaseNoticeStyle" TargetType="Border">
                <Setter Property="Margin" Value="0,2,0,2" />
                <Setter Property="Padding" Value="10" />
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="BorderThickness" Value="1" />
            </Style>
            
            <!-- 错误通知样式 -->
            <Style x:Key="ErrorNoticeStyle" TargetType="Border" BasedOn="{StaticResource BaseNoticeStyle}">
                <Setter Property="Background" Value="#FFEBEE" />
                <Setter Property="BorderBrush" Value="#FFCDD2" />
            </Style>
            
            <!-- 警告通知样式 -->
            <Style x:Key="WarningNoticeStyle" TargetType="Border" BasedOn="{StaticResource BaseNoticeStyle}">
                <Setter Property="Background" Value="#FFF8E1" />
                <Setter Property="BorderBrush" Value="#FFECB3" />
            </Style>
            
            <!-- 信息通知样式 -->
            <Style x:Key="InfoNoticeStyle" TargetType="Border" BasedOn="{StaticResource BaseNoticeStyle}">
                <Setter Property="Background" Value="#E3F2FD" />
                <Setter Property="BorderBrush" Value="#BBDEFB" />
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10,5">
            <Grid>
                <TextBlock 
                    Text="{Binding LanResources.Main_Notifications, Source={x:Static localization:LocalizationService.Current}, FallbackValue=通知消息}" 
                    FontWeight="Bold" 
                    VerticalAlignment="Center" />
                <Button 
                    HorizontalAlignment="Right" 
                    Click="ClearAllButton_Click" 
                    ToolTip="{Binding LanResources.Main_Clear_all, Source={x:Static localization:LocalizationService.Current}, FallbackValue=清除所有}" 
                    Style="{StaticResource MahApps.Styles.Button.Circle}" 
                    Width="24" 
                    Height="24">
                    <iconPacks:PackIconMaterial Kind="NotificationClearAll" Width="16" Height="16" />
                </Button>
            </Grid>
        </Border>
        
        <!-- 通知列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Notices}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource {Binding Level, Converter={StaticResource LevelToStyleConverter}}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                
                                <!-- 通知图标 -->
                                <iconPacks:PackIconMaterial 
                                    Grid.Column="0" 
                                    Margin="0,0,10,0" 
                                    VerticalAlignment="Top" 
                                    Width="24" 
                                    Height="24">
                                    <iconPacks:PackIconMaterial.Style>
                                        <Style TargetType="iconPacks:PackIconMaterial">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Level}" Value="Error">
                                                    <Setter Property="Kind" Value="AlertCircle" />
                                                    <Setter Property="Foreground" Value="#F44336" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Level}" Value="Warn">
                                                    <Setter Property="Kind" Value="AlertOutline" />
                                                    <Setter Property="Foreground" Value="#FF9800" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Level}" Value="Info">
                                                    <Setter Property="Kind" Value="Information" />
                                                    <Setter Property="Foreground" Value="#2196F3" />
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </iconPacks:PackIconMaterial.Style>
                                </iconPacks:PackIconMaterial>
                                
                                <!-- 通知内容 -->
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding Message}" TextWrapping="Wrap" />
                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                        <TextBlock Text="{Binding From}" FontSize="11" Opacity="0.7" />
                                        <TextBlock Text=" - ID: " FontSize="11" Opacity="0.7" />
                                        <TextBlock Text="{Binding Id}" FontSize="11" Opacity="0.7" />
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>