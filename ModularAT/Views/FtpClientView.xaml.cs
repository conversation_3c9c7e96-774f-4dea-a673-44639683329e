using System.Windows.Controls;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.ViewModels;

namespace ModularAT.Views;

public partial class FtpClientView : UserControl
{
    public FtpClientView()
    {
        InitializeComponent();


        // 绑定密码框
        PasswordBox.PasswordChanged += (sender, e) =>
        {
            ClientViewModel.Config.Password = PasswordBox.Password;
        };
        
        // 初始化密码框
        if (!string.IsNullOrEmpty(ClientViewModel.Config.Password))
        {
            PasswordBox.Password = ClientViewModel.Config.Password;
        }
    }
    public FtpClientViewModel ClientViewModel => App.GetService<FtpClientViewModel>();
}