using System.Windows.Controls;
using System.Windows.Data;
using ModularAT.ViewModels;

namespace ModularAT.Views;

/// <summary>
///     ControlerGenerateConfig.xaml 的交互逻辑
/// </summary>
public partial class ControlerOnlineConfigView : UserControl
{
    public ControlerOnlineConfigView()
    {
        InitializeComponent();

        var cvTasks = CollectionViewSource.GetDefaultView(paraDataGrid.ItemsSource);
        if (cvTasks != null && cvTasks.CanGroup)
        {
            cvTasks.GroupDescriptions.Clear();
            cvTasks.GroupDescriptions.Add(new PropertyGroupDescription("Title"));
        }
    }

    public ControlerOnlineConfigViewModel ViewModel => App.GetService<ControlerOnlineConfigViewModel>()!;
}