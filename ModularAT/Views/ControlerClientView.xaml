<UserControl
    x:Class="ModularAT.Views.ControlerClientView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:ModularAT.ViewModels"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:enum="clr-namespace:ModularAT.Driver.Controller;assembly=ModularAT.Driver.Controller"
    xmlns:valueConverter="clr-namespace:ModularAT.ValueConverter"
    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
    d:DataContext="{d:DesignInstance viewModels:ControlerClientViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}" Grid.Row="0" Grid.ColumnSpan="2"
                   Text="{Binding LanResources.ControlerClient_Ctrl_conn, Source={x:Static localization:LocalizationService.Current}}" />
        <!-- 左侧栏 -->
        <StackPanel Grid.Row="1" Grid.Column="0" HorizontalAlignment="Left" MinWidth="180" Margin="10">
            <TextBlock Text="IP" />
            <TextBox Text="{Binding Config.Ip}" />
            <TextBlock
                Text="{Binding LanResources.ControlerClient_Port, Source={x:Static localization:LocalizationService.Current}}" />
            <TextBox Text="{Binding Config.Port}" />
            <Button Command="{Binding Path=ConnectPortCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=Data.IsConnectedControler}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Connection}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerClient_Connect, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
            <Button Command="{Binding Path=ClosePortCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=Data.IsConnectedControler}" Value="False">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=LanDisconnect}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerClient_Disconnect, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
            <Button
                Command="{Binding Path=InitResetCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}" />
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Restart}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.Main_Reset, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
        </StackPanel>
        <!-- 初始化步骤 -->
        <DockPanel Margin="50,0,0,0" Grid.Column="1" Grid.Row="1">
            <Viewbox Stretch="Uniform" VerticalAlignment="Stretch" HorizontalAlignment="Left">
                <hc:StepBar Dock="Left" HorizontalAlignment="Left"
                            Name="initStepBar"
                            StepIndex="{Binding CurrentStepIndex}">
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Global_data_reset, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Platform_verification, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_System_parameter_configuration_initialization, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Slave_station_information_acquisition, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Mapping_of_slave_station_address_to_control_address, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Master_slave_station_status_verification, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Completion_of_status_initialization_of_bus_system_etc, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Initialization_of_movement_related_parameters, Source={x:Static localization:LocalizationService.Current}}" />
                    <hc:StepBarItem Content="{Binding LanResources.ControlerClient_Successful_initialization_of_magnetic_drive, Source={x:Static localization:LocalizationService.Current}}" />
                </hc:StepBar>
            </Viewbox>
        </DockPanel>
    </Grid>

</UserControl>