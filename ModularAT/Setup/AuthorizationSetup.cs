using System.Security.Claims;
using Microsoft.Extensions.DependencyInjection;
using ModularAT.Entity.Policy;

namespace ModularAT.Setup;

public static class AuthorizationSetup
{
    public static void AddAuthorizationSetup(this IServiceCollection services)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));

        // 以下四种常见的授权方式。

        // 1、这个很简单，其他什么都不用做， 只需要在API层的controller上边，增加特性即可
        // [Authorize(Roles = "Admin,System")]


        #region 参数

        // 如果要数据库动态绑定，这里先留个空，后边处理器里动态赋值
        var permission = new List<PermissionItem>();

        // 角色与接口的权限要求参数
        var permissionRequirement = new PermissionRequirement(
            "/Login", // 拒绝授权的跳转地址（目前无用）
            permission,
            ClaimTypes.Role, //基于角色的授权
            "Issuer", //发行人
            "Audience" //听众
        );

        #endregion


        // 注入权限处理器
        // services.AddScoped<IAuthorizationHandler, PermissionHandler>();
        services.AddSingleton(permissionRequirement);
    }
}