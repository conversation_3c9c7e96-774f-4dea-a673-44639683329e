using System.IO;
using System.Reflection;
using Autofac;
using ModularAT.Repository;
using ModularAT.Repository.Interface;
using Module = Autofac.Module;

namespace ModularAT.Setup;

public class AutofacModuleRegister : Module
{
    protected override void Load(ContainerBuilder builder)
    {
        var basePath = AppContext.BaseDirectory;
        //builder.RegisterType<AdvertisementServices>().As<IAdvertisementServices>();


        #region 带有接口层的服务注入

        //var servicesDllFile = Path.Combine(basePath, "ModularAT.exe");
        var repositoryDllFile = Path.Combine(basePath, "ModularAT.Repository.dll");

        if (!File.Exists(repositoryDllFile))
        {
            var msg = "Repository.dll和service.dll 丢失，因为项目解耦了，所以需要先F6编译，再F5运行，请检查 bin 文件夹，并拷贝。";
            //log.Error(msg);
            throw new Exception(msg);
        }


        // AOP 开关，如果想要打开指定的功能，只需要在 appsettigns.json 对应对应 true 就行。


        builder.RegisterGeneric(typeof(BaseRepository<>)).As(typeof(IBaseRepository<>)).InstancePerDependency(); //注册仓储

        var modularAtAssembly = Assembly.Load(new AssemblyName("IMTS-Studio"));

        // 获取所有以"Services"结尾的类，并确保这些类实现了接口
        var serviceTypes = modularAtAssembly.GetTypes()
            .Where(t => t.Name.EndsWith("Services") && t.IsClass && !t.IsAbstract)
            .Where(t => t.GetInterfaces().Any());

        foreach (var serviceType in serviceTypes)
            // 为每个服务注册其实现的所有接口
            builder.RegisterType(serviceType)
                .AsImplementedInterfaces()
                .InstancePerDependency();

        // 获取 Service.dll 程序集服务，并注册
        //var assemblysServices = Assembly.LoadFrom(servicesDllFile);
        //builder.RegisterAssemblyTypes(assemblysServices)
        //          .AsImplementedInterfaces()
        //          .InstancePerDependency();

        // 获取 Repository.dll 程序集服务，并注册
        var assemblysRepository = Assembly.LoadFrom(repositoryDllFile);
        builder.RegisterAssemblyTypes(assemblysRepository)
            .AsImplementedInterfaces()
            .InstancePerDependency();

        #endregion

        #region 没有接口层的服务层注入

        //因为没有接口层，所以不能实现解耦，只能用 Load 方法。
        //注意如果使用没有接口的服务，并想对其使用 AOP 拦截，就必须设置为虚方法
        //var assemblysServicesNoInterfaces = Assembly.Load("ModularAT");
        //builder.RegisterAssemblyTypes(assemblysServicesNoInterfaces);

        #endregion

        #region 没有接口的单独类，启用class代理拦截

        //只能注入该类中的虚方法，且必须是public
        //这里仅仅是一个单独类无接口测试，不用过多追问
        //builder.RegisterAssemblyTypes(Assembly.GetAssembly(typeof(Love)))
        //    .EnableClassInterceptors()
        //    .InterceptedBy(cacheType.ToArray());

        #endregion

        #region 单独注册一个含有接口的类，启用interface代理拦截

        //不用虚方法
        //builder.RegisterType<AopService>().As<IAopService>()
        //   .AsImplementedInterfaces()
        //   .EnableInterfaceInterceptors()
        //   .InterceptedBy(typeof(BlogCacheAOP));

        #endregion
    }
}