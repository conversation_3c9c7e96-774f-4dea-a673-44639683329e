using ModularAT.Localization.Resources;
﻿using Microsoft.Extensions.DependencyInjection;
using ModularAT.Common;
using ModularAT.Common.DB;
using ModularAT.Common.Extensions;
using SqlSugar;

namespace ModularAT.Setup;

public static class SqlsugarSetup
{
    public static void AddSqlsugarSetup(this IServiceCollection services)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));

        // 默认添加主数据库连接
        MainDb.CurrentDbConnId = Appsettings.app("MainDB");

        // 把多个连接对象注入服务，这里必须采用Scope，因为有事务操作
        services.AddScoped<ISqlSugarClient>(o =>
        {
            // 连接字符串
            var listConfig = new List<ConnectionConfig>();
            // 从库
            var listConfig_Slave = new List<SlaveConnectionConfig>();
            BaseDBConfig.MutiConnectionString.slaveDbs.ForEach(s =>
            {
                listConfig_Slave.Add(new SlaveConnectionConfig
                {
                    HitRate = s.HitRate,
                    ConnectionString = s.Connection
                });
            });

            BaseDBConfig.MutiConnectionString.allDbs.ForEach(m =>
            {
                listConfig.Add(new ConnectionConfig
                    {
                        ConfigId = m.ConnId.ObjToString().ToLower(),
                        ConnectionString = m.Connection,
                        DbType = (DbType)m.DbType,
                        IsAutoCloseConnection = true,
                        // Check out more information: https://github.com/anjoy8/ModularAT/issues/122
                        AopEvents = new AopEvents
                        {
                            OnLogExecuting = (sql, p) =>
                            {
                                if (Appsettings.app("AppSettings", "SqlAOP", "Enabled").ObjToBool())
                                    Parallel.For(0, 1,
                                        e => { App.Log.Debug(new[] { GetParas(p), Lang.SqlsugarSetup_Sql_statement + sql }); });
                            }
                        },
                        MoreSettings = new ConnMoreSettings
                        {
                            //IsWithNoLockQuery = true,
                            IsAutoRemoveDataCache = true
                        },
                        // 从库
                        SlaveConnectionConfigs = listConfig_Slave,
                        // 自定义特性
                        ConfigureExternalServices = new ConfigureExternalServices
                        {
                            EntityService = (property, column) =>
                            {
                                if (column.IsPrimarykey && property.PropertyType == typeof(int))
                                    column.IsIdentity = true;
                            }
                        },
                        InitKeyType = InitKeyType.Attribute
                    }
                );
            });
            return new SqlSugarClient(listConfig);
        });
    }

    private static string GetParas(SugarParameter[] pars)
    {
        var key = Lang.SqlsugarSetup_Sql_parameters;
        foreach (var param in pars) key += $"{param.ParameterName}:{param.Value}\n";

        return key;
    }
}