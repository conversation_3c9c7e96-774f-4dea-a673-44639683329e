using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using GRpc.SimulationProtocol;
using ModularAT.Driver.Controller;
using static GRpc.SimulationProtocol.SimulationProtocolService;

namespace ModularAT.Service.Simulation;

public class SimulationProtocolServiceImpl : SimulationProtocolServiceBase
{
    private readonly Server _server;
    private AxisFeedbackList _axisFeedbackList = new();
    private RoAxisFeedbackList _roAxisFeedbackList = new();

    public SimulationProtocolServiceImpl()
    {
        _server = new Server
        {
            Ports = { new ServerPort("127.0.0.1", 11111, ServerCredentials.Insecure) },
            Services = { BindService(this) }
        };
        _server.Start();
    }

    public void Stop()
    {
        _server.ShutdownAsync().Wait();
    }

    public override async Task GetAxisFeedbacks(Empty request, IServerStreamWriter<AxisFeedbackList> responseStream,
        ServerCallContext context)
    {
        try
        {
            while (!context.CancellationToken.IsCancellationRequested)
            {
                await responseStream.WriteAsync(_axisFeedbackList);
                await Task.Delay(10, context.CancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 处理取消请求
        }
    }

    public override async Task GetRoAxisFeedbacks(Empty request, IServerStreamWriter<RoAxisFeedbackList> responseStream,
        ServerCallContext context)
    {
        try
        {
            while (!context.CancellationToken.IsCancellationRequested)
            {
                await responseStream.WriteAsync(_roAxisFeedbackList);
                await Task.Delay(10, context.CancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // 处理取消请求
        }
    }

    //获取LineConfig
    public override Task<LineConfig> GetLineConfig(Empty request, ServerCallContext context)
    {
        var config = ControllerContext.Instance.CurrentViewConfig;
        var lineConfig = new LineConfig
        {
            MoversCount = config.MoversCount,
            StatorLength = config.StatorLength,
            StatorsCount = config.StatorsCount,
            UpDownLinesDistance = config.UpDownLinesDistance,
            IsFaceEncoder = config.IsFaceEncoder,
            StatorLeftDirection = config.StatorLeftDirection,
            StatorRightDirection = config.StatorRightDirection,
            IsArcLine = config.IsArcLine,
            RunRadius = config.RunRadius,
            LinearMaxTravel = config.LinearMaxTravel
        };
        lineConfig.MoverIds.AddRange(config.MoverIds
            .Split(',')
            .Select(x => Convert.ToInt32(x))
            .ToArray());
        return Task.FromResult(lineConfig);
    }

    public override Task<StationConfigList> GetStationConfigs(Empty request, ServerCallContext context)
    {
        var stationConfigs = ControllerContext.Instance.CurrentStationConfigs.Select((item, index) =>
            new StationConfig()
            {
                IOnObj = item.IOnObj,
                IOnObjID = item.IOnObjID,
                IStationID = item.IStationID,
                IStnEnable = item.IStnEnable,
                LrStationPos = item.LrStationPos
            });
        var stationConfigList = new StationConfigList();
        stationConfigList.StationConfigs.AddRange(stationConfigs);
        return Task.FromResult(stationConfigList);
    }
    
    public override Task<ViewLineConfigList> GetViewLineConfigs(Empty request, ServerCallContext context)
    {
        var viewLineConfigs = ControllerContext.Instance.CurrentViewLineConfigs.Select((item, index) =>
            new ViewLineConfig()
            {
                AnchorPoint = item.AnchorPoint,
                IsArc = item.IsArc,
                IsRemovable = item.IsRemovable,
                LineId = item.LineId,
                MoveDirection = item.MoveDirection,
                Name = item.Name,
                PrefabName = item.PrefabName,
                TranspId = item.TranspId,
                RoAxisId = item.RoAxisId,
                RoAxisRunRadius = item.RoAxisRunRadius,
                TranspRunTravel = item.TranspRunTravel,
                RoAxisDirection = item.RoAxisDirection,
                TranspLength = item.TranspLength,
                StatorCount = item.StatorCount,
                StatorLength = item.StatorLength,
            });
        var viewLineConfigList = new ViewLineConfigList();
        viewLineConfigList.ViewLineConfigs.AddRange(viewLineConfigs);
        return Task.FromResult(viewLineConfigList);
    }

    public void SendAxisFeedbackDirect(ref AxisFeedbackList data)
    {
        _axisFeedbackList = data;
    }

    public void SendRoAxisFeedbackDirect(ref RoAxisFeedbackList data)
    {
        _roAxisFeedbackList = data;
    }
}