<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <OutputType>Library</OutputType>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>
    <ItemGroup>
        <Compile Remove="SimulationTcpService.cs"/>
    </ItemGroup>
    <ItemGroup>
        <None Remove="SimulationProtocol.proto"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Google.Protobuf" Version="3.19.4"/>
        <PackageReference Include="Grpc.Core" Version="2.45.0"/>
        <PackageReference Include="Grpc.Tools" Version="2.66.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <Protobuf Include="SimulationProtocol.proto" GrpcServices="Both"/>
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\ModularAT.Driver.Controller\ModularAT.Driver.Controller.csproj" />
      <ProjectReference Include="..\ModularAT.Entity\ModularAT.Entity.csproj" />
    </ItemGroup>
</Project>