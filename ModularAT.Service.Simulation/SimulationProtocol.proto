syntax = "proto3";

import "google/protobuf/empty.proto";

package SimulationProtocol;
option csharp_namespace = "GRpc.SimulationProtocol";

// 定义AxisFeedback消息格式
message AxisFeedback {
  int32 IAxisID = 1;  // 轴ID
  sint32 IAxisCurObject = 2;  // 动子所处对象，0：直线段 1：圆弧段
  sint32 IAxisCurObjectID = 3;  // 动子所处对象ID
  uint32 UiAxisDrvErrCode = 4;  // 轴驱动错误代码
  uint32 UiAxisMotionErrCode = 5;  // 轴运动规划错误代码
  int32 DiAxisCurPos = 6;  // 轴当前位置
  int32 DiAxisCurVel = 7;  // 轴当前速度
  uint32 UdiAxisRunState = 8;  // 轴运行状态
}

message RoAxisFeedback {
  // 轴的使能状态
  bool BPoweron = 1;
  // 运行状态
  bool BRunning = 2;
  // 回零完成
  bool BHomeDone = 3;
  // 轴的错误码
  uint32 DwAxisErrorID = 4;
  // 轴的实际速度
  double LrActVelocity = 5;
  // 轴的实际位置
  double LrActPosition = 6;
}

// 定义包含多个AxisFeedback消息的列表
message AxisFeedbackList {
  repeated AxisFeedback axisFeedbacks = 1;  // 使用repeated关键字定义列表
}

message RoAxisFeedbackList {
  repeated RoAxisFeedback roAxisFeedbacks = 1;  // 使用repeated关键字定义列表
}

message LineConfig {
  // 动子数量
  int32 MoversCount = 1;
  // 单侧导轨数量
  int32 StatorsCount = 2;
  // 直线段线体中单个导轨长度（单位：毫米）
  float StatorLength = 3;
  // 上下线体中心点间的距离（单位：毫米）
  float UpDownLinesDistance = 4;
  // 是否面向编码器（true表示从右向左移动）
  bool IsFaceEncoder = 5;
  // 左直线/弧形段动子移动方向（true表示从上向下）
  bool StatorLeftDirection = 6;
  // 右直线/弧形段动子移动方向（true表示向下） 
  bool StatorRightDirection = 7;
  // 是否是弧形段线体
  bool IsArcLine = 8;
  // 弧形段旋转半径（单位：毫米）
  float RunRadius = 9;
  // 最大行程（单位：毫米）
  float LinearMaxTravel = 10;
  // 动子ID数组（上下左右）
  repeated int32 MoverIds = 11;
}

message StationConfig {
  //所在对象类型
  int32 IOnObj = 1;
  //所在对象ID
  int32 IOnObjID = 2;
  //工位ID
  int32 IStationID = 3;
  //启用
  int32 IStnEnable = 4;
  //工位位置
  double LrStationPos = 5;
}

message StationConfigList {
  repeated StationConfig stationConfigs = 1;  // 使用repeated关键字定义列表
}


message ViewLineConfig {
  // 线体ID
  int32 line_id = 1 ;

  // 名称
  string name = 2 ;

  // 预制体名称
  string prefab_name = 3 ;

  // 定位点 (x,y,z)
  string anchor_point = 4 ;

  // 移动正方向 (1-4)上下左右
  int32 move_direction = 5;

  // 定子长度
  float stator_length = 6 ;

  // 定子数量
  int32 stator_count = 7 ;

  // 是否可移动段
  bool is_removable = 8 ;

  // 是否弧形段
  bool is_arc = 9 ;

  // 接驳电机Id
  int32 transp_id = 10 ;

  // 接驳长度
  float transp_length = 11 ;

  // 旋转轴移动正方向 (1-4)上下左右
  int32 ro_axis_direction = 12 ;

  // 控制器反馈的接驳电机最大行程
  float transp_run_travel = 13;

  // 旋转轴Id
  int32 ro_axis_id = 14 ;

  // 弧形段旋转半径
  float ro_axis_run_radius = 15;
}

message ViewLineConfigList {
  repeated ViewLineConfig viewLineConfigs = 1;
}

service SimulationProtocolService {
  //获取直线段轴反馈信息
  rpc GetAxisFeedbacks (google.protobuf.Empty) returns  (stream AxisFeedbackList);
  //获取接驳段轴反馈信息
  rpc GetRoAxisFeedbacks (google.protobuf.Empty) returns  (stream RoAxisFeedbackList);
  // 获取线体配置
  rpc GetLineConfig (google.protobuf.Empty) returns (LineConfig);
  // 获取工位配置
  rpc GetStationConfigs (google.protobuf.Empty) returns (StationConfigList);
  // 获取视图线体配置
  rpc GetViewLineConfigs (google.protobuf.Empty) returns (ViewLineConfigList);
}
