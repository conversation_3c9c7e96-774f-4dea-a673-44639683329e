<UserControl x:Class="GaugeCtrl.Views.OscilloscopeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             xmlns:vm="clr-namespace:GaugeCtrl.ViewModels"
             xmlns:scottplot="clr-namespace:ScottPlot.WPF;assembly=ScottPlot.WPF"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance vm:OscilloscopeViewModel}">
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="示波器" FontSize="24" FontWeight="Bold" 
                   Foreground="#333" Margin="10,0,0,20" HorizontalAlignment="Left" />
        
        <!-- 控制按钮面板 -->
        <StackPanel Orientation="Horizontal" Margin="5,0,20,10" Grid.Row="1">
            <Button Content="启动" Command="{Binding StartCommand}" IsEnabled="{Binding CanStart}" Margin="5" Padding="10,5" 
                    Style="{StaticResource ButtonPrimary}" hc:BorderElement.CornerRadius="3"/>
            <Button Content="停止" Command="{Binding StopCommand}" IsEnabled="{Binding CanStop}" Margin="5" Padding="10,5" 
                    Style="{StaticResource ButtonWarning}" hc:BorderElement.CornerRadius="3"/>
            <Button Content="清除" Command="{Binding ClearCommand}" IsEnabled="{Binding CanClear}" Margin="5" Padding="10,5" 
                    Style="{StaticResource ButtonInfo}" hc:BorderElement.CornerRadius="3"/>
            <Button  Content="缩放" Command="{Binding AutoScaleCommand}" Margin="5" Padding="10,5" 
                    Style="{StaticResource ButtonDefault}" hc:BorderElement.CornerRadius="3"/>
            <Button Visibility="Collapsed" Content="导出" Command="{Binding SaveDataCommand}" Margin="5" Padding="10,5" 
                    Style="{StaticResource ButtonSuccess}" hc:BorderElement.CornerRadius="3"/>
            <Button Content="导出图片" Command="{Binding ExportImageCommand}" Margin="5" Padding="10,5" 
                    Style="{StaticResource ButtonDefault}" hc:BorderElement.CornerRadius="3"/>
            
            <!-- 采样间隔设置 -->
            <StackPanel Orientation="Horizontal" Margin="15,5" VerticalAlignment="Center">
                <TextBlock Text="采样间隔:" VerticalAlignment="Center" Margin="0,0,5,0" 
                          Foreground="{DynamicResource PrimaryTextBrush}"/>
                <hc:NumericUpDown Value="{Binding SampleIntervalMicroseconds}" 
                                 Minimum="10" Maximum="10000" Increment="10" 
                                 Width="80" Height="25" 
                                 hc:BorderElement.CornerRadius="3"/>
                <TextBlock Text="μs" VerticalAlignment="Center" Margin="5,0,0,0" 
                          Foreground="{DynamicResource PrimaryTextBrush}"/>
            </StackPanel>
            
            <CheckBox Visibility="Collapsed"  Content="十字线" IsChecked="{Binding IsCrosshairEnabled}" Margin="15,5" 
                      VerticalAlignment="Center" Foreground="{DynamicResource PrimaryTextBrush}"/>
        </StackPanel>
        
        <!-- 双通道图表显示区域 -->
        <Border Grid.Row="2" Margin="5,0,0,20" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" 
                Background="{DynamicResource RegionBrush}" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <scottplot:WpfPlot x:Name="MainPlot" Grid.Row="1" Margin="5"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>