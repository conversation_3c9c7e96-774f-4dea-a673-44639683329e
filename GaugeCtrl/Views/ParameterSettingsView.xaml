<UserControl x:Class="GaugeCtrl.Views.ParameterSettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             xmlns:converter="clr-namespace:GaugeCtrl.Converter"
             xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900">
    
    <UserControl.Resources>
        <!-- 转换器 -->
        <converter:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converter:ValidationErrorToForegroundConverter x:Key="ValidationErrorToForegroundConverter" />
        <converter:ParameterValueConverter x:Key="ParameterValueConverter" />
        <conv:BoolToFontWeightConverter x:Key="BooleanToFontWeightConverter" FalseValue="Normal" TrueValue="Bold"/>
        <!-- 参数表格样式 -->
        <Style x:Key="ParameterDataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False" />
            <Setter Property="CanUserAddRows" Value="False" />
            <Setter Property="CanUserDeleteRows" Value="False" />
            <Setter Property="CanUserReorderColumns" Value="False" />
            <Setter Property="CanUserResizeRows" Value="False" />
            <Setter Property="SelectionMode" Value="Single" />
            <Setter Property="GridLinesVisibility" Value="Horizontal" />
            <Setter Property="HorizontalGridLinesBrush" Value="#E0E0E0" />
            <Setter Property="HeadersVisibility" Value="Column" />
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderBrush" Value="#E0E0E0" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="RowHeight" Value="35" />
            <Setter Property="FontSize" Value="12" />
        </Style>

        <!-- 表格标题样式 -->
        <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#F5F5F5" />
            <Setter Property="Foreground" Value="#333" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Padding" Value="8,5" />
            <Setter Property="BorderBrush" Value="#E0E0E0" />
            <Setter Property="BorderThickness" Value="0,0,0,1" />
        </Style>

        <!-- 表格单元格样式 -->
        <Style x:Key="DataGridCellStyle" TargetType="DataGridCell">
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Padding" Value="8,5" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Style.Triggers>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="#E3F2FD" />
                    <Setter Property="Foreground" Value="#1976D2" />
                </Trigger>
            </Style.Triggers>
        </Style>


    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="参数设定" FontSize="24" FontWeight="Bold" 
                   Foreground="#333" Margin="10,0,0,20" HorizontalAlignment="Left" />

        <!-- TabControl -->
        <Grid Grid.Row="1" Margin="5,0,0,20">
            <TabControl Background="White" BorderBrush="#E0E0E0" BorderThickness="1" HorizontalAlignment="Center">
                <TabControl.Style>
                    <Style TargetType="TabControl">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabControl">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>
                                        <TabPanel Grid.Row="0" IsItemsHost="True" HorizontalAlignment="Center" Background="Transparent"/>
                                        <Border Grid.Row="1" Background="{TemplateBinding Background}" 
                                                BorderBrush="{TemplateBinding BorderBrush}" 
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter ContentSource="SelectedContent"/>
                                        </Border>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TabControl.Style>
            
            <!-- 电机参数 -->
            <TabItem Header="电机参数" Style="{StaticResource CustomTabItemStyle}">
                <Grid Margin="20">
                    <DataGrid ItemsSource="{Binding MotorParameters}"
                              Style="{StaticResource ParameterDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              CellStyle="{StaticResource DataGridCellStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="参数名称" Binding="{Binding Name}" Width="2*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="Padding" Value="8,0" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="数值" Width="1.5*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <!-- 数值/文本输入框 -->
                                            <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                                     BorderThickness="0" Background="Transparent"
                                                     HorizontalAlignment="Center" VerticalAlignment="Center"
                                                     HorizontalContentAlignment="Center"
                                                     FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                     Foreground="{Binding Converter={StaticResource ValidationErrorToForegroundConverter}}"
                                                     IsEnabled="{Binding IsEnabled}"
                                                     Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Numeric}" />

                                            <!-- 布尔值复选框 -->
                                            <CheckBox IsChecked="{Binding Value, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource ParameterValueConverter}}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Boolean}" />

                                            <!-- 枚举下拉框 -->
                                            <ComboBox ItemsSource="{Binding EnumValues}"
                                                      SelectedItem="{Binding Value, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                      HorizontalContentAlignment="Center"
                                                      BorderThickness="0" Background="Transparent"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Enum}" />
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最小值" Binding="{Binding MinValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最大值" Binding="{Binding MaxValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- 控制参数 -->
            <TabItem Header="控制参数" Style="{StaticResource CustomTabItemStyle}">
                <Grid Margin="20">
                    <DataGrid ItemsSource="{Binding ControlParameters}"
                              Style="{StaticResource ParameterDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              CellStyle="{StaticResource DataGridCellStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="参数名称" Binding="{Binding Name}" Width="2*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="Padding" Value="8,0" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="数值" Width="1.5*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <!-- 数值/文本输入框 -->
                                            <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                                     BorderThickness="0" Background="Transparent"
                                                     HorizontalAlignment="Center" VerticalAlignment="Center"
                                                     HorizontalContentAlignment="Center"
                                                     FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                     Foreground="{Binding Converter={StaticResource ValidationErrorToForegroundConverter}}"
                                                     IsEnabled="{Binding IsEnabled}"
                                                     Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Numeric}" />

                                            <!-- 布尔值复选框 -->
                                            <CheckBox IsChecked="{Binding Value, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource ParameterValueConverter}}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Boolean}" />

                                            <!-- 枚举下拉框 -->
                                            <ComboBox ItemsSource="{Binding EnumValues}"
                                                      SelectedItem="{Binding Value, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                      HorizontalContentAlignment="Center"
                                                      BorderThickness="0" Background="Transparent"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Enum}" />
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最小值" Binding="{Binding MinValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最大值" Binding="{Binding MaxValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- 保护参数 -->
            <TabItem Header="保护参数" Style="{StaticResource CustomTabItemStyle}">
                <Grid Margin="20">
                    <DataGrid ItemsSource="{Binding ProtectionParameters}"
                              Style="{StaticResource ParameterDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              CellStyle="{StaticResource DataGridCellStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="参数名称" Binding="{Binding Name}" Width="2*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="Padding" Value="8,0" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="数值" Width="1.5*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <!-- 数值/文本输入框 -->
                                            <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                                     BorderThickness="0" Background="Transparent"
                                                     HorizontalAlignment="Center" VerticalAlignment="Center"
                                                     HorizontalContentAlignment="Center"
                                                     FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                     Foreground="{Binding Converter={StaticResource ValidationErrorToForegroundConverter}}"
                                                     IsEnabled="{Binding IsEnabled}"
                                                     Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Numeric}" />

                                            <!-- 布尔值复选框 -->
                                            <CheckBox IsChecked="{Binding Value, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource ParameterValueConverter}}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Boolean}" />

                                            <!-- 枚举下拉框 -->
                                            <ComboBox ItemsSource="{Binding EnumValues}"
                                                      SelectedItem="{Binding Value, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                      HorizontalContentAlignment="Center"
                                                      BorderThickness="0" Background="Transparent"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Enum}" />
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最小值" Binding="{Binding MinValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最大值" Binding="{Binding MaxValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- 运动参数 -->
            <TabItem Header="运动参数" Style="{StaticResource CustomTabItemStyle}">
                <Grid Margin="20">
                    <DataGrid ItemsSource="{Binding MotionParameters}"
                              Style="{StaticResource ParameterDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              CellStyle="{StaticResource DataGridCellStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="参数名称" Binding="{Binding Name}" Width="2*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="Padding" Value="8,0" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="数值" Width="1.5*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <!-- 数值/文本输入框 -->
                                            <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}"
                                                     BorderThickness="0" Background="Transparent"
                                                     HorizontalAlignment="Center" VerticalAlignment="Center"
                                                     HorizontalContentAlignment="Center"
                                                     FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                     Foreground="{Binding Converter={StaticResource ValidationErrorToForegroundConverter}}"
                                                     IsEnabled="{Binding IsEnabled}"
                                                     Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Numeric}" />

                                            <!-- 布尔值复选框 -->
                                            <CheckBox IsChecked="{Binding Value, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource ParameterValueConverter}}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Boolean}" />

                                            <!-- 枚举下拉框 -->
                                            <ComboBox ItemsSource="{Binding EnumValues}"
                                                      SelectedItem="{Binding Value, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                      HorizontalContentAlignment="Center"
                                                      BorderThickness="0" Background="Transparent"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Enum}" />
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最小值" Binding="{Binding MinValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最大值" Binding="{Binding MaxValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- IO参数 -->
            <TabItem Header="IO参数" Style="{StaticResource CustomTabItemStyle}" Visibility="Collapsed">
                <Grid Margin="20">
                    <DataGrid ItemsSource="{Binding IoParameters}" 
                              Style="{StaticResource ParameterDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              CellStyle="{StaticResource DataGridCellStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="参数名称" Binding="{Binding Name}" Width="2*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="Padding" Value="8,0" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="数值" Width="1.5*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <!-- 数值/文本输入框 -->
                                            <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                                                     BorderThickness="0" Background="Transparent"
                                                     HorizontalAlignment="Center" VerticalAlignment="Center"
                                                     HorizontalContentAlignment="Center"
                                                     FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                     Foreground="{Binding Converter={StaticResource ValidationErrorToForegroundConverter}}"
                                                     IsEnabled="{Binding IsEnabled}"
                                                     Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Numeric}" />
                                            
                                            <!-- 布尔值复选框 -->
                                            <CheckBox IsChecked="{Binding Value, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource ParameterValueConverter}}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Boolean}" />
                                            
                                            <!-- 枚举下拉框 -->
                                            <ComboBox ItemsSource="{Binding EnumValues}" 
                                                      SelectedItem="{Binding Value, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                      HorizontalContentAlignment="Center"
                                                      BorderThickness="0" Background="Transparent"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      IsEnabled="{Binding IsEnabled}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Enum}" />
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最小值" Binding="{Binding MinValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最大值" Binding="{Binding MaxValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- 监控参数 -->
            <TabItem Header="监控参数" Style="{StaticResource CustomTabItemStyle}" Visibility="Collapsed">
                <Grid Margin="20">
                    <DataGrid ItemsSource="{Binding MonitoringParameters}" 
                              Style="{StaticResource ParameterDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                              CellStyle="{StaticResource DataGridCellStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="参数名称" Binding="{Binding Name}" Width="2*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="Padding" Value="8,0" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTemplateColumn Header="数值" Width="1.5*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Grid>
                                            <!-- 数值/文本输入框 -->
                                            <TextBox Text="{Binding Value, UpdateSourceTrigger=PropertyChanged, ValidatesOnDataErrors=True}" 
                                                     BorderThickness="0" Background="Transparent"
                                                     HorizontalAlignment="Center" VerticalAlignment="Center"
                                                     HorizontalContentAlignment="Center" IsReadOnly="True"
                                                     FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                     Foreground="{Binding Converter={StaticResource ValidationErrorToForegroundConverter}}"
                                                     Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Numeric}" />
                                            
                                            <!-- 布尔值复选框 -->
                                            <CheckBox IsChecked="{Binding Value, UpdateSourceTrigger=PropertyChanged, Converter={StaticResource ParameterValueConverter}}"
                                                      HorizontalAlignment="Center" VerticalAlignment="Center" IsEnabled="False"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Boolean}" />
                                            
                                            <!-- 枚举下拉框 -->
                                            <ComboBox ItemsSource="{Binding EnumValues}" 
                                                      SelectedItem="{Binding Value, UpdateSourceTrigger=PropertyChanged}"
                                                      HorizontalAlignment="Stretch" VerticalAlignment="Center"
                                                      HorizontalContentAlignment="Center"
                                                      BorderThickness="0" Background="Transparent" IsEnabled="False"
                                                      FontWeight="{Binding IsModified, Converter={StaticResource BooleanToFontWeightConverter}}"
                                                      Visibility="{Binding ParameterType, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Enum}" />
                                        </Grid>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最小值" Binding="{Binding MinValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="最大值" Binding="{Binding MaxValue}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center" />
                                        <Setter Property="VerticalAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="Normal" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            </TabControl>
        </Grid>
    </Grid>
</UserControl>