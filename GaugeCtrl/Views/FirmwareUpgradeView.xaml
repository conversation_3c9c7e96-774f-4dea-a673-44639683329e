<UserControl x:Class="GaugeCtrl.Views.FirmwareUpgradeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900">
    
    <UserControl.Resources>
        <!-- 按钮样式 -->        
        <Style x:Key="UpgradeButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Padding" Value="20,8" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1" />
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC" />
                                <Setter Property="Foreground" Value="#666666" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 文件选择按钮样式 -->
        <Style x:Key="FileSelectButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F5F5F5" />
            <Setter Property="Foreground" Value="#333" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Padding" Value="15,6" />
            <Setter Property="BorderBrush" Value="#E0E0E0" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E8E8E8" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D0D0D0" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 信息文本样式 -->
        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="#666" />
            <Setter Property="Margin" Value="0,5" />
        </Style>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="固件升级" FontSize="24" FontWeight="Bold" 
                   Foreground="#333" Margin="10,0,0,20" HorizontalAlignment="Left" />

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- 文件选择区域 -->
            <Border Grid.Row="0" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="1" 
                    CornerRadius="4" Padding="20" Margin="0,0,0,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- 选择升级文件标题 -->
                    <TextBlock Grid.Row="0" Text="选择升级文件:" FontSize="14" FontWeight="Bold" 
                               Foreground="#333" Margin="0,0,0,10" />

                    <!-- 文件信息和选择按钮 -->
                    <Grid Grid.Row="1" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" Content="打开" Style="{StaticResource FileSelectButtonStyle}" 
                        Command="{Binding SelectFileCommand}" Margin="0,0,15,0" />

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="{Binding SelectedFileName, FallbackValue='文件名称: bldc-driver-v22.bin'}" 
                                       FontSize="12" Foreground="#333" FontWeight="SemiBold" />
                            <TextBlock Text="{Binding SelectedFileSize, FallbackValue='文件大小: 40.44 KB'}" 
                                       Style="{StaticResource InfoTextStyle}" />
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>

            <!-- 升级进度区域 -->
            <Border Grid.Row="1" Background="#FAFAFA" BorderBrush="#E0E0E0" BorderThickness="1" 
                    CornerRadius="4" Padding="20" Margin="0,0,0,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- 升级进度标题 -->
                    <TextBlock Grid.Row="0" Text="升级进度:" FontSize="14" FontWeight="Bold" 
                               Foreground="#333" Margin="0,0,0,10" />

                    <!-- 进度条 -->
                    <ProgressBar Grid.Row="1" Height="20" Value="{Binding UpgradeProgress, FallbackValue=0}" 
                                 Maximum="100" Background="#E0E0E0" Foreground="#4CAF50" 
                                 BorderThickness="0" Margin="0,0,0,10" />
                    
                </Grid>
            </Border>

            <!-- 升级按钮 -->
            <Button Grid.Row="2" Content="开始" Style="{StaticResource UpgradeButtonStyle}" 
                    Command="{Binding StartUpgradeCommand}" HorizontalAlignment="Center" 
                    Margin="0,0,0,20" />

            <!-- 提示信息区域 -->
            <Border Grid.Row="3" Background="#FFF3E0" BorderBrush="#FFB74D" BorderThickness="1" 
                    CornerRadius="4" Padding="15" Margin="0,0,0,20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        
                        <!-- 警告图标 -->
                        <Ellipse Grid.Column="0" Width="20" Height="20" Fill="#FF9800" 
                                 VerticalAlignment="Top" Margin="0,2,10,0">
                        </Ellipse>
                        <TextBlock Grid.Column="0" Text="!" FontSize="14" FontWeight="Bold" 
                                   Foreground="White" HorizontalAlignment="Center" 
                                   VerticalAlignment="Top" Margin="0,2,10,0" />
                        
                        <!-- 提示文本 -->
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="请在厂家指导下操作" FontSize="12" Foreground="#BF360C" />
                            <TextBlock Text="更新固件过程中请勿断电" FontSize="11" Foreground="#BF360C" />
                            <TextBlock Text="更新固件完成后需要重新配置参数" FontSize="11" Foreground="#BF360C" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</UserControl>