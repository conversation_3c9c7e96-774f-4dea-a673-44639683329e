<Application x:Class="GaugeCtrl.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:hc="https://handyorg.github.io/handycontrol"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 自定义样式 -->
            <Style x:Key="PlotStyle" TargetType="Border">
                <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="4"/>
            </Style>

            <!-- TabItem样式 -->
            <Style x:Key="CustomTabItemStyle" TargetType="TabItem">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Padding" Value="20,10" />
                <Setter Property="Margin" Value="2,0" />
                <Setter Property="Background" Value="#F5F5F5" />
                <Setter Property="Foreground" Value="#666" />
                <Setter Property="BorderBrush" Value="#E0E0E0" />
                <Setter Property="BorderThickness" Value="1,1,1,0" />
                <Setter Property="HorizontalContentAlignment" Value="Center" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TabItem">
                            <Border Name="Border" 
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4,4,0,0"
                                    Padding="{TemplateBinding Padding}"
                                    TextBlock.Foreground="{TemplateBinding Foreground}">
                                <ContentPresenter ContentSource="Header" 
                                                HorizontalAlignment="Center" 
                                                VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="White" />
                                    <Setter TargetName="Border" Property="TextBlock.Foreground" Value="#2196F3" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#2196F3" />
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#E3F2FD" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>