namespace ModularAT.Entity.Controller;

public interface ITransStateFeedBack
{
    /// <summary>
    ///     线段主体段ID
    /// </summary>
    public short ILineBodySegID { get; set; }

    /// <summary>
    ///     左侧连接对象ID
    /// </summary>
    public short ILeftConnectedObjectID { get; set; }

    /// <summary>
    ///     右侧连接对象ID
    /// </summary>
    public short IRightConnectedObjectID { get; set; }
}