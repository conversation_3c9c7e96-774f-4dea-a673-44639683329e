namespace ModularAT.Entity.Controller;

public interface IAxisCtrlCmd
{
    /// <summary>
    ///     轴ID
    /// </summary>
    public short AxisID { get; set; }

    /// <summary>
    ///     轴类型
    ///     0: 动子 1：旋转电机 2：直线电机
    /// </summary>
    public short AxisType { get; set; }

    /// <summary>
    ///     轴目标对象ID
    ///     绝对运动时的线体ID
    /// </summary>
    public short AxisTargetObjectID { get; set; }

    /// <summary>
    ///     轴目标站ID
    ///     用于工位运动时的工位ID，不不进行工位运动置为-1即可
    /// </summary>
    public short AxisTargetStationID { get; set; }

    /// <summary>
    ///     单轴运动模式
    ///     0：示教Jog运动，1：绝对运动，2：相对运动 3：工位运动
    /// </summary>
    public short AxisRunMode { get; set; }

    /// <summary>
    ///     速度模式
    ///     0：T 1：S5
    /// </summary>
    public short VelMode { get; set; }

    /// <summary>
    ///     轴控制
    ///     bit0：动子停止，置1
    ///     bit1：错误复位
    ///     bit2：Jog正向运动
    ///     bit3：Jog反向运动
    ///     bit4：绝对运动
    ///     bit5：相对运动
    ///     bit6：工位运动
    ///     bit7: 设置零点
    ///     bit8-bit15：预留
    /// </summary>
    public ushort AxisCtrl { get; set; }

    /// <summary>
    ///     轴设定速度
    /// </summary>
    public double AxisSetVel { get; set; }

    /// <summary>
    ///     轴设定加速度
    /// </summary>
    public double AxisSetAcc { get; set; }

    /// <summary>
    ///     轴设定减速度
    /// </summary>
    public double AxisSetDec { get; set; }

    /// <summary>
    ///     轴设定加加速度
    /// </summary>
    public double AxisSetJerk { get; set; }

    /// <summary>
    ///     轴目标位置
    ///     绝对运动时对应绝对位置 相对运动时对应相对位置
    /// </summary>
    public double AxisTarPos { get; set; }

    /// <summary>
    /// 动子定位精度要求
    /// </summary>
    public double ILrSetLocationPrecision { get; set; }

    /// <summary>
    /// 动子防碰撞精度要求
    /// </summary>
    public double ILrSetAntiCollPrecision { get; set; }
}