namespace ModularAT.Entity.Controller;

public interface ITransState
{
    /// <summary>
    ///     当前对象ID
    /// </summary>
    public short ICurObjectID { get; set; }

    /// <summary>
    ///     左侧连接对象ID
    /// </summary>
    public short ILeftConnectedObjectID { get; set; }

    /// <summary>
    ///     左侧连接状态
    /// </summary>
    public short ILeftConnectState { get; set; }

    /// <summary>
    ///     右侧连接对象ID
    /// </summary>
    public short IRightConnectedObjectID { get; set; }

    /// <summary>
    ///     右侧连接状态
    /// </summary>
    public short IRightConnectState { get; set; }
}