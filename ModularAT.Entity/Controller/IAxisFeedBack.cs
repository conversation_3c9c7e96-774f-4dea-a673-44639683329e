namespace ModularAT.Entity.Controller;

public interface IAxisFeedBack
{
    /// <summary>
    ///     动子所处对象
    ///     0：直线段 1：圆弧段
    /// </summary>
    public short IAxisCurObject { get; set; }

    /// <summary>
    ///     动子所处对象ID
    /// </summary>
    public short IAxisCurObjectID { get; set; }

    /// <summary>
    ///     轴驱动错误代码
    /// </summary>
    public uint UiAxisDrvErrCode { get; set; }

    /// <summary>
    ///     轴运动规划错误代码
    /// </summary>
    public uint UiAxisMotionErrCode { get; set; }

    /// <summary>
    ///     轴当前位置
    /// </summary>
    public int DiAxisCurPos { get; set; }

    /// <summary>
    ///     轴当前速度
    /// </summary>
    public int DiAxisCurVel { get; set; }

    /// <summary>
    ///     轴运行状态
    ///     bit0: 单轴使能状态
    ///     bit1: 单轴运行状态
    ///     bit2: 单轴报警状态
    ///     bit3: 单轴错误状态
    ///     bit4: 单轴左碰撞
    ///     bit5: 单轴右碰撞
    ///     bit6: 单轴正限位
    ///     bit7: 单轴负限位
    ///     bit8: 单轴在工位上
    ///     bit9: 单轴已到达目标位置
    ///     bit10-bit31:预留
    /// </summary>
    public uint UdiAxisRunState { get; set; }
}