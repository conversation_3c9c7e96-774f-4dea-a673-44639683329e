namespace ModularAT.Entity.Controller;

public interface ISysCtrlCmd
{
    /// <summary>
    /// 控制对象
    /// 0: 动子 1：旋转电机 2：直线电机
    /// </summary>
    //public short ICtrlObj { get; set; }

    /// <summary>
    ///     系统运行模式
    ///     0：动子示教 1：接驳示教 2：自动运行
    /// </summary>
    public short ISysRunMode { get; set; }

    /// <summary>
    ///     系统自动运行模式
    ///     同步或异步->暂未使用赋0即可
    /// </summary>
    public short ISysAutoRunMode { get; set; }

    /// <summary>
    ///     系统运行速度比率
    ///     默认30  -> 暂未使用赋0即可
    /// </summary>
    public short ISysRunVelRatio { get; set; }

    /// <summary>
    ///     从站节点ID
    ///     -1：全部控制  0 - (MAX-1)：控制对应某个节点(后续改成某个电机)
    /// </summary>
    public short IDrvID { get; set; }

    /// <summary>
    ///     系统控制
    ///     bit0: 使能/禁能
    ///     bit1: 错误复位
    ///     bit2: 运行
    ///     bit3: 暂停
    ///     bit4: 紧急停止
    ///     bit5-bit15: 预留
    /// </summary>
    public ushort UiSysCtrl { get; set; }
}