namespace ModularAT.Entity.Controller;

public interface ISysFeedBack
{
    /// <summary>
    ///     系统错误轴ID
    /// </summary>
    public short ISysErrorAxisID { get; set; }

    /// <summary>
    ///     系统错误驱动器ID
    /// </summary>
    public short ISysErrorDrvID { get; set; }

    /// <summary>
    ///     系统驱动器错误代码
    /// </summary>
    public uint UdiSysDrvErrorCode { get; set; }

    /// <summary>
    ///     系统驱动错误名称,24byte
    /// </summary>
    public string SSysDrvErrName { get; set; }

    /// <summary>
    ///     系统错误代码
    /// </summary>
    public uint UdiSysErrorCode { get; set; }

    /// <summary>
    ///     系统状态
    ///     bit0:    系统已准备好
    ///     bit1:    系统使能状态
    ///     bit2:    系统错误状态
    ///     bit3:    系统运行状态
    ///     bit4:    系统总线状态
    ///     bit5:    系统平台校验状态
    ///     bit6:    轴配置完成，可进行轴序列初始化
    ///     bit7:    运动参数配置完成，可进行系统旧状态恢复
    ///     bit8:    系统恢复旧状态完成
    ///     bit8-31: 预留
    /// </summary>
    public uint UdiSysState { get; set; }
}