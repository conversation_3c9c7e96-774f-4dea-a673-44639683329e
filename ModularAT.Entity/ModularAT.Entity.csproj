<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <LangVersion>preview</LangVersion>
        <OutputType>Library</OutputType>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <UseWPF>true</UseWPF>
        <ImportWindowsDesktopTargets>true</ImportWindowsDesktopTargets>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Mvvm">
            <Version>8.4.0</Version>
        </PackageReference>
        <PackageReference Include="log4net">
            <Version>3.1.0</Version>
        </PackageReference>
        <PackageReference Include="Microsoft.CSharp" Version="4.7.0"/>
        <PackageReference Include="MiniExcel">
            <Version>1.41.3</Version>
        </PackageReference>
        <PackageReference Include="SqlSugar">
            <Version>5.1.4.171-preview11</Version>
        </PackageReference>
        <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0"/>
        <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0"/>
        <PackageReference Include="System.Text.Json">
            <Version>9.0.0</Version>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ModularAT.Common\ModularAT.Common.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="CustomDialogExampleContent.cs"/>
        <Compile Remove="Extension\ParameterModelExtension.cs"/>
        <Compile Remove="IServo\ParameterBase.cs"/>
        <Compile Remove="Struct\ScopeFeedback.cs"/>
    </ItemGroup>
    <ItemGroup>
        <None Remove="app.config"/>
    </ItemGroup>
    <ItemGroup>
        <None Include="Dtos\OnlineConfig\Online_SlaveNodeCfgDto.cs"/>
    </ItemGroup>
</Project>