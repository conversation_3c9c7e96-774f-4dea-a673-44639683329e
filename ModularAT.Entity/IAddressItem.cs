using System;
using System.Collections.Generic;
using ModularAT.Entity.Enum;

namespace ModularAT.Entity;

public interface IAddressItem : IComparable
{
    IList<IAddressItem> Children { get; set; }

    IList<IAddressItem> Neighbors { get; set; }

    IAddressItem Parent { get; set; }

    string No { get; set; }

    string Description { get; set; }

    string Unit { get; set; }

    string Index { get; set; }

    string Name { get; set; }

    int? SubIndex { get; set; }

    string SubNo { get; set; }

    string Access { get; set; }

    long? Value { get; set; }

    // string PointNumber { get; set; }

    long? Int64Value { get; set; }

    DataType DataType { get; set; }
}