using System;
using ModularAT.Entity.Enum;

namespace ModularAT.Entity.Extension;

public static class DataTypeExtension
{
    public static int ToBase(this DataType dataType)
    {
        return dataType switch
        {
            DataType.UINT16 => 10,
            DataType.INT16 => 10,
            DataType.UINT32 => 10,
            DataType.INT32 => 10,
            _ => throw new ArgumentOutOfRangeException("DataType")
        };
    }
}