using System;
using System.ComponentModel;
using ModularAT.Entity.Enum;

namespace ModularAT.Entity.Extension;

public static class EnumExtension
{
    public static string GetEnumDescription(this ParamTableEnum value)
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
        return attribute == null ? value.ToString() : attribute.Description;
    }
}