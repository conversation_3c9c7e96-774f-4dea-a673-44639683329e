using System;
using AutoMapper;
using CommunityToolkit.Mvvm.ComponentModel;
using ModularAT.Entity.BaseManage;

namespace ModularAT.Entity.Dtos;

[ObservableObject]
[AutoMap(typeof(sysUserInfo))]
public partial class SysUserInfoDto
{
    /// <summary>
    ///     登录账号
    /// </summary>
    [ObservableProperty]
    public partial string LoginName { get; set; }

    [ObservableProperty] public partial string LoginPWD { get; set; }

    /// <summary>
    ///     真实姓名
    /// </summary>
    [ObservableProperty]
    public partial string RealName { get; set; }

    //[ObservableProperty]
    //public partial string Role { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [ObservableProperty]
    public partial string Remark { get; set; }

    /// <summary>
    ///     状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    ///     最后登录时间
    /// </summary>
    public DateTime LastErrTime { get; set; }
}