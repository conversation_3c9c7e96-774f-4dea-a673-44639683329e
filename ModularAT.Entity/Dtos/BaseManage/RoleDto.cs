using ModularAT.Localization.Resources;
﻿using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;
using ModularAT.Entity.BaseManage;

namespace ModularAT.Entity.Dtos;

[ObservableObject]
public partial class RoleDto : Role
{
    public ObservableCollection<string> Actions = new();

    [Required(ErrorMessage = "Null Error")] public new string RoleName { get; set; }

    public string actionsJson { get; set; }
}