namespace ModularAT.Entity;

public class ErrorRecordParamModel : ParameterBase, IErrorRecordParam
{
    public short New_ErrIndex
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public short Pre_ErrIndex
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code0
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code1
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }


    public ushort His_Err_Code2
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code3
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code4
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code5
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code6
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code7
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code8
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code9
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code10
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code11
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code12
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code13
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code14
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code15
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code16
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code17
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }


    public ushort His_Err_Code18
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort His_Err_Code19
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}