namespace ModularAT.Entity;

public class DriverModeSetModel : ParameterBase
{
    /// <summary>
    ///     模式
    /// </summary>
    public short Mode
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     控制权
    /// </summary>
    public short ControlBy
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     子模式
    ///     本地控制模式在正常操作模式下：
    ///     0x00：无控制（停止）
    ///     0x01：双轴位置控制
    ///     0x02：轴0电角度辨识
    ///     本地控制模式在工厂测试模式下：
    ///     0x00：无控制（停止）
    ///     0x01：直流采样测试
    ///     0x02：交流采样测试
    /// </summary>
    public short SubMode
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}