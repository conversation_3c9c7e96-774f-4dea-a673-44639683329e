namespace ModularAT.Entity;

public class ControlStateParamModel : ParameterBase, IControlStateParam
{
    public ushort LL_Resistance
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort ControlWord
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort StatusWord
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort ModeOfOperation
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public ushort ModesOfOperationDisplay
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}