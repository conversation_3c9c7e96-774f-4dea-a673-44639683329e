using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ModularAT.Entity;

[Serializable]
public class ParameterBase : INotifyPropertyChanged
{
    public int AxisSelect { get; set; }

    public event PropertyChangedEventHandler PropertyChanged;

    public void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}