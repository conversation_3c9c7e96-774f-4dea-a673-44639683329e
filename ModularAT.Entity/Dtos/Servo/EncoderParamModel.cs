namespace ModularAT.Entity;

public class EncoderParamModel : ParameterBase, IEncodeParam
{

    // 编码器类型属性
    public ushort EncoderType
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器分辨率属性
    public uint EncoderResolution
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器主版本号属性
    public ushort EncVersion_Master
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器功能版本号属性
    public ushort EncVersion_Func
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器Bug版本号属性
    public ushort EncVersion_Bug
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器调试版本号属性
    public ushort EncVersion_Debug
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器调试功能属性
    public ushort EncDebugFunc
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器位置0属性
    public uint EncoderPos0
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // 编码器位置1属性
    public uint EncoderPos1
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}