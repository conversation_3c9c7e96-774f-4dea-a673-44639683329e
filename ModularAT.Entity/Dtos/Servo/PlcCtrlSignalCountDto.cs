using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Dtos.Servo;

/// <summary>
/// PLC控制信号计数数据传输对象
/// 用于在不同层之间传递PLC控制信号计数数据，避免直接传递驱动层的Package对象
/// </summary>
public partial class PlcCtrlSignalCountDto : ObservableObject
{
    /// <summary>
    /// 记录轴号
    /// </summary>
    [ObservableProperty]
    private int recordAxis;

    /// <summary>
    /// 刹车信号计数
    /// </summary>
    [ObservableProperty]
    private uint stopSignalCount;

    /// <summary>
    /// 加速信号计数
    /// </summary>
    [ObservableProperty]
    private uint accSignalCount;
}
