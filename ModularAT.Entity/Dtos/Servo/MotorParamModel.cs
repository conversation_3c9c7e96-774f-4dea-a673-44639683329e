namespace ModularAT.Entity;

/// <summary>
///     电机参数表
///     命名必须和DriveParamer.xlsx内的保持一致
/// </summary>
public class MotorParamModel : ParameterBase, IMotorParam
{
    // LL_Resistance 属性
    public ushort LL_Resistance
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // LL_Inductance 属性
    public ushort LL_Inductance
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Rate_Current 属性
    public ushort Rate_Current
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Rate_Torque 属性
    public ushort Rate_Torque
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Peak_Current 属性
    public ushort Peak_Current
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Torque_Constant 属性
    public ushort Torque_Constant
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Back_Emf_Coeff 属性
    public ushort Back_Emf_Coeff
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Electrode_Distance 属性
    public ushort Electrode_Distance
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Number_Of_Poles 属性
    public ushort Number_Of_Poles
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Elec_Offset 属性
    public uint Elec_Offset
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // U_Current 属性
    public short U_Current
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // V_Current 属性
    public short V_Current
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // W_Current 属性
    public short W_Current
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Bus_Voltage 属性
    public short Bus_Voltage
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // public uint Pos_Fit_Type
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
    //
    // public uint Ceg_1
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
    //
    // public uint Ceg_2
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
    //
    // public uint Enc0_ThL
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
    //
    // public uint Enc2_ThH
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
    //
    // public uint Cth_1
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
    //
    // public uint Cth_2
    // {
    //     get;
    //     set
    //     {
    //         field = value;
    //         OnPropertyChanged();
    //     }
    // }
}