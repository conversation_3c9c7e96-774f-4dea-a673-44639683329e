namespace ModularAT.Entity;

/// <summary>
///     保护参数表
///     命名必须和DriveParamer.xlsx内的保持一致
/// </summary>
public class ProtectParamModel : ParameterBase, IProtectParam
{
    public uint Short_OLD_Threshold
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Short_OLD_Time
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Sus_OLD_RateCur
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Sus_OLD_PeakCur
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Dur_Of_PeakCur
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Heat_Coeff 属性
    public uint Heat_Coeff
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Cool_Coeff 属性
    public uint Cool_Coeff
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Locked_rotor_Current 属性
    public uint Locked_rotor_Current
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Locked_rotor_Time 属性
    public uint Locked_rotor_Time
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Locked_rotor_Vel 属性
    public uint Locked_rotor_Vel
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // MOS_Temp 属性
    public ushort MOS_Temp
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Encoder_Commu_Err 属性
    public ushort Encoder_Commu_Err
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Stall_Dect 属性
    public uint Stall_Dect
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Over_Voltage 属性
    public uint Over_Voltage
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Under_Voltage 属性
    public uint Under_Voltage
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Thermal_Time_Const
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Ins_OCD_Threshold
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
    public uint Servo_Thermal_Time
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
    public uint Servo_Heat_Coeff
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
    public uint Servo_Cool_Coeff
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}