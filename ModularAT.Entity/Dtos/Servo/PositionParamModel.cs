namespace ModularAT.Entity;

public class PositionParamModel : ParameterBase, IPositionParam
{
    // Target_Position 属性
    public uint Target_Position
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Actual_Position 属性
    public uint Actual_Position
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Position_Kp 属性
    public uint Position_Kp
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Position_Ki 属性
    public uint Position_Ki
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Position_Kd 属性
    public uint Position_Kd
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // PILF_Cutoff_Freq 属性
    public uint PILF_Cutoff_Freq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // PosCtrl_ClamUp 属性
    public int PosCtrl_ClamUp
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // PosCtrl_ClamLow 属性
    public int PosCtrl_ClamLow
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // PISA_Cutoff 属性
    public uint PISA_Cutoff
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}