namespace ModularAT.Entity;

public class SystemParamModel : ParameterBase, ISystemParam
{
    public ushort DRIVER_VERSION_0
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    } = 1;

    public ushort DRIVER_VERSION_1
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort DRIVER_VERSION_2
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort DRIVER_VERSION_3
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort DRIVER_VERSION_4
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeCtl
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList0
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList1
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList2
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList3
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList4
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList5
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList6
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort ScopeMapList7
    {
        get;
        set
        {
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }
}