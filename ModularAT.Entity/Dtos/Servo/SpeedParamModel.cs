namespace ModularAT.Entity;

public class SpeedParamModel : ParameterBase, ISpeedParam
{
    // Target_Velocity 属性
    public int Target_Velocity
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Actual_Velocity 属性
    public int Actual_Velocity
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Velocity_Kp 属性
    public uint Velocity_Kp
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Velocity_Ki 属性
    public uint Velocity_Ki
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Velocity_Kd 属性
    public uint Velocity_Kd
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Velocity_Kc 属性
    public uint Velocity_Kc
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Vel_FF_Gain 属性
    public uint Vel_FF_Gain
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Vel_FFLPF_CutFreq 属性
    public uint Vel_FFLPF_CutFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Vel_FBLPF_CutFreq 属性
    public uint Vel_FBLPF_CutFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // VILP_Cutoff_Freq 属性
    public uint VILP_Cutoff_Freq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // VelCtrl_ClamUp 属性
    public int VelCtrl_ClamUp
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // VelCtrl_ClamLow 属性
    public int VelCtrl_ClamLow
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}