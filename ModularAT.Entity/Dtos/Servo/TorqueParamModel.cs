namespace ModularAT.Entity;

public class TorqueParamModel : ParameterBase, ITorqueParam
{
    // Iq_CMD 属性
    public short Iq_CMD
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Id_CMD 属性
    public short Id_CMD
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Iq_FB 属性
    public short Iq_FB
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Id_FB 属性
    public short Id_FB
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Current_Kp 属性
    public uint Current_Kp
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Current_Ki 属性
    public uint Current_Ki
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Current_Kd 属性
    public uint Current_Kd
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Current_Ke_D 属性
    public uint Current_Ke_D
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Current_Ke_Q 属性
    public uint Current_Ke_Q
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Current_Kf 属性
    public uint Current_Kf
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Cur_FB_CutFreq 属性
    public uint Cur_FB_CutFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // CILP_CutFreq 属性
    public uint CILP_CutFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Cur_FF_Gain 属性
    public uint Cur_FF_Gain
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // Cur_FFLPF_CutFreq 属性
    public uint Cur_FFLPF_CutFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // CINF_NotchFreq 属性
    public uint CINF_NotchFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // CINF_CutFreq 属性
    public uint CINF_CutFreq
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    // CINF_Depth 属性
    public uint CINF_Depth
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Cur_SA_Factor_U
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public uint Cur_SA_Factor_V
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}