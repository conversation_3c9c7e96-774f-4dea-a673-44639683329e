using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public class SysCtrlCmdModel : ObservableObject, ISysCtrlCmd
{
    /// <summary>
    ///     控制对象的索引
    /// </summary>
    private short _iCtrlObj;

    /// <summary>
    ///     从站节点ID
    ///     -1：全部控制  0 - (MAX-1)：控制对应某个节点(后续改成某个电机)
    /// </summary>
    private short _iDrvID;

    /// <summary>
    ///     系统自动运行模式
    ///     同步或异步->暂未使用赋0即可
    /// </summary>
    private short _iSysAutoRunMode;


    /// <summary>
    ///     系统运行模式
    ///     0：动子示教 1：接驳示教 2：自动运行
    /// </summary>
    private short _iSysRunMode;

    /// <summary>
    ///     系统运行速度比率
    ///     默认30  -> 暂未使用赋0即可
    /// </summary>
    private short _iSysRunVelRatio;

    /// <summary>
    ///     系统控制
    ///     bit0: 使能/禁能
    ///     bit1: 错误复位
    ///     bit2: 运行
    ///     bit3: 暂停
    ///     bit4: 紧急停止
    ///     bit5-bit15: 预留
    /// </summary>
    private ushort _uiSysCtrl;

    public short ICtrlObj
    {
        get => _iCtrlObj;
        set
        {
            if (_iCtrlObj != value)
            {
                _iCtrlObj = value;
                OnPropertyChanged();
            }
        }
    }

    public short ISysRunMode
    {
        get => _iSysRunMode;
        set
        {
            if (_iSysRunMode != value)
            {
                _iSysRunMode = value;
                OnPropertyChanged();
            }
        }
    }

    public short ISysAutoRunMode
    {
        get => _iSysAutoRunMode;
        set
        {
            if (_iSysAutoRunMode != value)
            {
                _iSysAutoRunMode = value;
                OnPropertyChanged();
            }
        }
    }

    public short ISysRunVelRatio
    {
        get => _iSysRunVelRatio;
        set
        {
            if (_iSysRunVelRatio != value)
            {
                _iSysRunVelRatio = value;
                OnPropertyChanged();
            }
        }
    }

    public short IDrvID
    {
        get => _iDrvID;
        set
        {
            if (_iDrvID != value)
            {
                _iDrvID = value;
                OnPropertyChanged();
            }
        }
    }

    public ushort UiSysCtrl
    {
        get => _uiSysCtrl;
        set
        {
            if (_uiSysCtrl != value)
            {
                _uiSysCtrl = value;
                OnPropertyChanged();
            }
        }
    }
}