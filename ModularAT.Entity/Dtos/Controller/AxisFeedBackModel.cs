using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public class AxisFeedBackModel : ObservableObject, IAxisFeedBack
{
    public int IAxisID
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public short IAxisCurObject
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public short IAxisCurObjectID
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public uint UiAxisDrvErrCode
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public uint UiAxisMotionErrCode
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public int DiAxisCurPos
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public int DiAxisCurVel
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public uint UdiAxisRunState
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public override bool Equals(object obj)
    {
        if (obj is AxisFeedBackModel other) return IAxisID == other.IAxisID;
        return false;
    }

    public override int GetHashCode()
    {
        return IAxisID.GetHashCode();
    }
}