using System;

namespace ModularAT.Entity.Controller;

/// <summary>
///     直线电机或旋转轴反馈
/// </summary>
public class RoAxisFeedBackModel : IRoAxisFeedBack
{
    public int Id { get; set; }
    /// <summary>
    ///     轴的使能状态
    /// </summary>
    public bool BPoweron { get; set; }

    /// <summary>
    ///     运行状态
    /// </summary>
    public bool BRunning { get; set; }

    /// <summary>
    ///     回零完成
    /// </summary>
    public bool BHomeDone { get; set; }

    /// <summary>
    ///     轴的错误码
    /// </summary>
    public uint DwAxisErrorID { get; set; }

    /// <summary>
    ///     轴的实际速度
    /// </summary>
    public double LrActVelocity
    {
        get => Math.Round(field, 3);
        set;
    }

    /// <summary>
    ///     轴的实际位置
    /// </summary>
    public double LrActPosition
    {
        get => Math.Round(field, 3);
        set;
    }
}