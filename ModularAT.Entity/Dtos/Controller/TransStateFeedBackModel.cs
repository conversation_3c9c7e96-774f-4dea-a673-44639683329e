using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public class TransStateFeedBackModel : ObservableObject, ITransStateFeedBack
{
    public short ILineBodySegID
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public short ILeftConnectedObjectID
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public short IRightConnectedObjectID
    {
        get;
        set
        {
            if (field != value)
            {
                field = value;
                OnPropertyChanged();
            }
        }
    }

    public override bool Equals(object obj)
    {
        if (obj is TransStateFeedBackModel other) return ILineBodySegID == other.ILineBodySegID;
        return false;
    }

    public override int GetHashCode()
    {
        return ILineBodySegID.GetHashCode();
    }
}