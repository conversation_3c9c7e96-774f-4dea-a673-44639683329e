using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public class TransStateModel : ObservableObject, ITransState
{
    public short ICurObjectID
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public short ILeftConnectedObjectID
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public short ILeftConnectState
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public short IRightConnectedObjectID
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public short IRightConnectState
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }
}