using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public class AxisCtrlCmdModel : ObservableObject, IAxisCtrlCmd
{
    public ushort Current_ErrCode
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴ID
    /// </summary>
    public short AxisID
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴类型？
    /// </summary>
    public short AxisType
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴目标对象ID
    /// </summary>
    public short AxisTargetObjectID
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴目标站ID
    /// </summary>
    public short AxisTargetStationID
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴运行模式
    /// </summary>
    public short AxisRunMode
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     速度模式
    /// </summary>
    public short VelMode
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴控制
    /// </summary>
    public ushort AxisCtrl
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    ///     轴设定速度
    /// </summary>
    public double AxisSetVel
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    } = 500;

    /// <summary>
    ///     轴设定加速度
    /// </summary>
    public double AxisSetAcc
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    } = 10000;

    /// <summary>
    ///     轴设定减速度
    /// </summary>
    public double AxisSetDec
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    } = 10000;

    /// <summary>
    ///     轴设定加加速度
    /// </summary>
    public double AxisSetJerk
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    } = 10000;

    /// <summary>
    ///     轴目标位置
    /// </summary>
    public double AxisTarPos
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    }

    public double ILrSetLocationPrecision
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    } = 0.05;

    public double ILrSetAntiCollPrecision
    {
        get;
        set
        {
            field = value;
            OnPropertyChanged();
        }
    } = 2;
}