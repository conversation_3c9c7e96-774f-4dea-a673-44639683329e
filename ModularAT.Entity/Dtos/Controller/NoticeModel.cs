using CommunityToolkit.Mvvm.ComponentModel;
using log4net.Core;
using ModularAT.Entity.Enum;

namespace ModularAT.Entity.Controller;

public partial class NoticeModel : ObservableObject
{
    [ObservableProperty]
    public partial string From { get; set; }
    [ObservableProperty]
    public partial int Id { get; set; }
    [ObservableProperty]
    public partial Level Level { get; set; }
    [ObservableProperty]
    public partial string Message { get; set; }
}