using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public partial class FacilityStatusDto : ObservableObject
{
    [ObservableProperty] public partial bool IsInitializing { get; set; }

    [ObservableProperty] public partial bool IsRunning { get; set; }

    [ObservableProperty] public partial bool IsStopping { get; set; }

    [ObservableProperty] public partial bool IsResetting { get; set; }

    [ObservableProperty] public partial bool IsEmergencyStopping { get; set; }

    [ObservableProperty] public partial bool IsError { get; set; }

    [ObservableProperty] public partial bool IsMaintenance { get; set; }

    [ObservableProperty] public partial bool IsNormal { get; set; }

    [ObservableProperty] public partial bool IsAuto { get; set; }


    public override string ToString()
    {
        return $"Initializing: {IsInitializing}, " +
               $"Running: {IsRunning}, " +
               $"Stopping: {IsStopping}, " +
               $"Resetting: {IsResetting}, " +
               $"EmergencyStopping: {IsEmergencyStopping}, " +
               $"Error: {IsError}, " +
               $"Maintenance: {IsMaintenance}, " +
               $"Normal: {IsNormal}, " +
               $"Auto: {IsAuto}";
    }
}