using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.Controller;

public partial class RoAxisCtrlCmdModel : ObservableObject
{
    [ObservableProperty] ushort uServoCtrlCmd; // 控制命令
    [ObservableProperty] uint uiStationID; // 目标工位
    [ObservableProperty] int iTranID; // 接驳ID
    [ObservableProperty] double lrSetVel; // 设置速度
    [ObservableProperty] double lrSetAcc; // 设置加速度
    [ObservableProperty] double lrSetDec; // 设置减速度
    [ObservableProperty] double lrTarPos; // 目标位置
}