using System;
using CommunityToolkit.Mvvm.ComponentModel;
using ModularAT.Entity.Controller;

namespace ModularAT.Entity.OnlineConfig;

public partial class Online_SysCfgDto : ObservableObject
{
    // 线体段数组成
    [ObservableProperty] short iSysLineBodySegNum;

    // 系统动子数量
    [ObservableProperty] short iSysAxisNums;

    // 系统工位数量
    [ObservableProperty] short iSysStationNums;

    // 系统从站节点数量
    [ObservableProperty] short iSysSlaveNodeNums;

    // 系统驱动电机数量
    [ObservableProperty] short iSysDrvMotorNums;

    // 系统从站驱动控制模式
    [ObservableProperty] short iSysSlaveNodeDrvMode;

    // 磁板类型 (0: 288, 1: 144, 2: 240)
    [ObservableProperty] short iSysMagneticPlateType;

    // 系统反馈模式 (0: 过程数据, 1: 一问一答)
    [ObservableProperty] short iSysFeedbackMode;
}