using System;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.OnlineConfig;

[Serializable]
public partial class Online_LineBodyCfgDto(int index) : ObservableObject
{
    [ObservableProperty] public short iLineBodySegDir;

    [ObservableProperty] public short iLineBodySegDrvMotorNums;

    [ObservableProperty] public short iLineBodySegInitMoverNums;

    [ObservableProperty] public short iLineBodySegType;

    [ObservableProperty] public double lrNegativeLimitLen;

    [ObservableProperty] public double lrPositiveLimitLen;
}