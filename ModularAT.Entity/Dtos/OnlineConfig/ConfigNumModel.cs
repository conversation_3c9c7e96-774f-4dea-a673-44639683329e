using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.OnlineConfig;

public partial class ConfigNumModel : ObservableObject
{
    [ObservableProperty] private int ioCount;

    [ObservableProperty] private int lineSegmentCount = 4;

    [ObservableProperty] private int motorCount;

    [ObservableProperty] private int moverCount = 1;

    [ObservableProperty] private int rotaryAxisCount;

    [ObservableProperty] private int slaveNodeCount;

    [ObservableProperty] private int stationCount;

    [ObservableProperty] private int systemCount = 1;
}