using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Entity.OnlineConfig;

public partial class Online_ViewLineConfig : ObservableObject
{
    
    /// <summary>
    /// 动子数量
    /// </summary>
    [ObservableProperty]
    public partial int MoversCount { get; set; } = 8;

    /// <summary>
    /// 单侧导轨数量
    /// </summary>
    [ObservableProperty]
    public partial int StatorsCount { get; set; } = 2;

    /// <summary>
    /// 直线段线体中单个导轨长度
    /// </summary>
    [ObservableProperty]
    public partial float StatorLength { get; set; } = 480;

    /// <summary>
    /// 上下线体中心点间的距离，
    /// </summary>
    [ObservableProperty]
    public partial float UpDownLinesDistance { get; set; } = 443.3f;

    /// <summary>
    /// 是否面向编码器
    /// 用于确定移动方向，true从右向左移动
    /// </summary>
    [ObservableProperty]
    public partial bool IsFaceEncoder { get; set; } = true;


    /// <summary>
    /// 左直线电机或弧形段动子移动方向
    /// true为从上向下
    /// </summary>
    [ObservableProperty]
    public partial bool StatorLeftDirection { get; set; } = true;

    /// <summary>
    /// 右直线电机或弧形段动子移动方向
    /// true为向下
    /// </summary>
    [ObservableProperty]
    public partial bool StatorRightDirection { get; set; } = false;

    /// <summary>
    /// 左右线体是否是弧形段
    /// </summary>
    [ObservableProperty]
    public partial bool IsArcLine { get; set; } = true;

    /// <summary>
    /// 弧形段旋转半径
    /// </summary>
    [ObservableProperty]
    public partial float RunRadius { get; set; } = 221.65f;

    /// <summary>
    /// 直线电机或弧形段最大行程
    /// </summary> 
    [ObservableProperty]
    public partial float LinearMaxTravel { get; set; } = 180f;

    /// <summary>
    /// 动子ID,上下左右
    /// </summary>
    [ObservableProperty]
    public partial string MoverIds { get; set; } = "1, 3, 0, 2";
}