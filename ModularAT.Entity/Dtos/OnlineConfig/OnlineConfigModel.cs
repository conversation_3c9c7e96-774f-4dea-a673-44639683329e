using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using CommunityToolkit.Mvvm.ComponentModel;
using ModularAT.Common;
using ModularAT.Common.Log;

namespace ModularAT.Entity.OnlineConfig;

public partial class OnlineConfigModel : ObservableObject
{
    [ObservableProperty] public partial string GroupName { get; set; }

    [ObservableProperty] public partial string EnglishName { get; set; }

    [ObservableProperty] public partial string DataType { get; set; }

    [ObservableProperty] public partial string Describe { get; set; }

    [ObservableProperty] public partial string ReadValue { get; set; } = "0";

    [ObservableProperty] public partial string Value { get; set; }

    [ObservableProperty] public partial string Title { get; set; }

    [ObservableProperty] public partial bool IsExpand { get; set; }
    
    /// <summary>
    ///     根据属性类型给对象的属性赋值
    /// </summary>
    /// <param name="obj">被赋值对象</param>
    /// <param name="propertyName">属性名</param>
    /// <param name="value">值</param>
    /// <param name="dataType">属性的类型</param>
    /// <exception cref="ArgumentException"></exception>
    public static void SetTypedPropertyValue(object obj, string propertyName, string value, string dataType)
    {
        switch (dataType)
        {
            case "Int16":
                ObjectUtil<short>.SetPropertyValue(obj, propertyName, Convert.ToInt16(value));
                break;
            case "Int32":
                ObjectUtil<int>.SetPropertyValue(obj, propertyName, Convert.ToInt32(value));
                break;
            case "Int64":
                ObjectUtil<long>.SetPropertyValue(obj, propertyName, Convert.ToInt64(value));
                break;
            case "Single":
                ObjectUtil<float>.SetPropertyValue(obj, propertyName, Convert.ToSingle(value));
                break;
            case "Double":
                ObjectUtil<double>.SetPropertyValue(obj, propertyName, Convert.ToDouble(value));
                break;
            case "Decimal":
                ObjectUtil<decimal>.SetPropertyValue(obj, propertyName, Convert.ToDecimal(value));
                break;
            case "Boolean":
                ObjectUtil<bool>.SetPropertyValue(obj, propertyName, Convert.ToBoolean(value));
                break;
            case "String":
                ObjectUtil<string>.SetPropertyValue(obj, propertyName, Convert.ToString(value));
                break;
            default:
                throw new ArgumentException("Unsupported type: " + dataType);
        }
    }
    
    
    public static T ListToGeneric<T>(IEnumerable<OnlineConfigModel> configModels) where T : new()
    {
        var config = new T();
        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
        
        foreach (var model in configModels)
        {
            var prop = properties.FirstOrDefault(p => 
                p.Name.Equals(model.EnglishName, StringComparison.OrdinalIgnoreCase));

            if (prop == null || !prop.CanWrite) continue;
            try
            {
                var converter = TypeDescriptor.GetConverter(prop.PropertyType);
                if (converter.IsValid(model.Value))
                {
                    prop.SetValue(config, converter.ConvertFromString(model.Value));
                }
            }
            catch (Exception ex)
            {
                LogHelper.GetInstance().Error($"SetValue {prop.Name} Error: {ex.Message}");
            }
        }
        return config;
    }
    
}

