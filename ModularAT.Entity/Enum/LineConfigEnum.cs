using System;
using System.ComponentModel;

namespace ModularAT.Entity.Enum;

/// <summary>
/// 控制器配置参数枚举
/// </summary>
public enum LineConfigEnum
{
    [Description("Sys")] SysCfgPara,

    [Description("Motor")] MotorCfgPara,

    [Description("SlaveNode")] SlaveNodeCfgPara,

    [Description("Line")] LineBodyCfgPara,

    [Description("Station")] StationCfgPara,

    [Description("Axis")] AxisCfgPara,

    [Description("MoverArray")] MoverArrayCfgPara,

    [Description("PID")] PIDCfgPara,

    [Description("AxisOffset")] AxisOffsetCfgPara,

    [Description("EquipmentWiringDirection")] EquipmentWiringDirectionCfgPara,

    [Description("StationOffset")] StationOffsetCfgPara,

    [Description("UIView")] UIViewLineCfgPara
}

public static class LineConfigNameExtensions
{
    /// <summary>
    /// 返回配置的带后缀文件名
    /// </summary>
    /// <param name="configEnum"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static string GetConfigFileName(this LineConfigEnum configEnum)
    {
        return configEnum switch
        {
            LineConfigEnum.SysCfgPara => "SysCfg.xml",
            LineConfigEnum.MotorCfgPara => "MagLevMotorInit.xml",
            LineConfigEnum.SlaveNodeCfgPara => "SlNodeCfg.xml",
            LineConfigEnum.LineBodyCfgPara => "MagLevLineBodyCfg.xml",
            LineConfigEnum.StationCfgPara => "MagLevStationCfg.xml",
            LineConfigEnum.AxisCfgPara => "MagLevMoverCfg.xml",
            LineConfigEnum.MoverArrayCfgPara => "MagLevMoverArrayCfg.xml",
            LineConfigEnum.PIDCfgPara => "MagLevMoverPidCfg.xml",
            LineConfigEnum.AxisOffsetCfgPara => "MagLevMoverOffsetCfg.xml",
            LineConfigEnum.EquipmentWiringDirectionCfgPara => "WiringDirectionCfg.xml",
            LineConfigEnum.StationOffsetCfgPara => "MagLevStnOffsetCfg.xml",
            LineConfigEnum.UIViewLineCfgPara => "ViewLineCfg.xml",
            _ => throw new ArgumentOutOfRangeException(nameof(configEnum), configEnum, null)
        };
    }

    /// <summary>
    ///     获取配置xml根节点名称
    /// </summary>
    /// <param name="configEnum"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static string GetRootName(this LineConfigEnum configEnum)
    {
        return configEnum switch
        {
            LineConfigEnum.SysCfgPara => "ModularCfg",
            LineConfigEnum.MotorCfgPara => "MagLevCfg",
            LineConfigEnum.SlaveNodeCfgPara => "ModularCfg",
            LineConfigEnum.LineBodyCfgPara => "MagLevCfg",
            LineConfigEnum.StationCfgPara => "MagLevCfg",
            LineConfigEnum.AxisCfgPara => "MagLevCfg",
            LineConfigEnum.MoverArrayCfgPara => "MagLevCfg",
            LineConfigEnum.PIDCfgPara => "MagLevCfg",
            LineConfigEnum.AxisOffsetCfgPara => "MagLevCfg",
            LineConfigEnum.EquipmentWiringDirectionCfgPara => "MagLevCfg",
            LineConfigEnum.StationOffsetCfgPara => "MagLevCfg",
            LineConfigEnum.UIViewLineCfgPara => "ModularCfg",
            _ => throw new ArgumentOutOfRangeException(nameof(configEnum), configEnum, null)
        };
    }

    /// <summary>
    /// 获取配置的标题
    /// </summary>
    /// <param name="configEnum"></param>
    /// <param name="longName"></param>
    /// <returns></returns>
    public static string GetConfigTitle(this LineConfigEnum configEnum, bool longName = false)
    {
        var fieldInfo = configEnum.GetType().GetField(configEnum.ToString());
        var attributes = (DescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
        var desc = longName ? attributes[0].Description + "Params:" : attributes[0].Description;
        return attributes.Length > 0 ? desc : configEnum.ToString();
    }
}