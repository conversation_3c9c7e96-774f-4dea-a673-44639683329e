namespace ModularAT.Entity.Config;

public class ViewLineConfig
{
    /// <summary>
    /// 线体ID
    /// </summary>
    public int LineId { get; set; } = -1;

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; } = "";

    /// <summary>
    /// 预制体名称
    /// </summary>
    public string PrefabName { get; set; } = "";

    /// <summary>
    /// 定位点
    /// (x,y,z)
    /// </summary>
    public string AnchorPoint { get; set; } = "0,0,0";

    /// <summary>
    /// 移动正方向
    /// (1-4)上下左右
    /// </summary>
    public int MoveDirection { get; set; } = 4;

    /// <summary>
    /// 定子长度
    /// </summary>
    public float StatorLength { get; set; } = 576f;

    /// <summary>
    /// 定子数量
    /// </summary>
    public int StatorCount { get; set; } = 1;

    /// <summary>
    /// 是否可移动段
    /// </summary>
    public bool IsRemovable { get; set; } = false;

    /// <summary>
    /// 是否弧形段
    /// </summary>
    public bool IsArc { get; set; } = false;

    /// <summary>
    /// 接驳电机Id
    /// </summary>
    public int TranspId { get; set; } = -1;

    /// <summary>
    /// 接驳长度
    /// </summary>
    public float TranspLength { get; set; } = 0f;

    /// <summary>
    /// 旋转轴移动正方向
    /// (1-4)上下左右
    /// </summary>
    public int RoAxisDirection { get; set; } = -1;

    /// <summary>
    /// 控制器反馈的接驳电机最大行程
    /// </summary>
    public float TranspRunTravel { get; set; }
    
    /// <summary>
    /// 旋转轴Id
    /// </summary>
    public int RoAxisId { get; set; } = -1;

    /// <summary>
    /// 弧形段旋转半径
    /// </summary>
    public float RoAxisRunRadius { get; set; }
}