using System.Collections.Generic;
using System.IO.Ports;
using System.Text.Json.Serialization;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace ModularAT.Entity.Config;

public partial class SerialPortConfig : ObservableObject
{
    public SerialPortConfig()
    {
        BaudRateList = [9600, 14400, 19200, 38400, 57600, 115200, 230400, 460800, 921600];
        DataBitsList = [5, 6, 7, 8];
        StopBitsList = [StopBits.None, StopBits.One, StopBits.OnePointFive, StopBits.Two];
        ParityBitsList = [Parity.None, Parity.Odd, Parity.Even];
    }

    [RelayCommand]
    private void RefreshPorts()
    {
        PortList = [.. SerialPort.GetPortNames()];
    }

    [JsonIgnore]
    [ObservableProperty]
    public partial List<string> PortList { get; set; }

    [JsonIgnore] public List<int> BaudRateList { get; set; }

    [JsonIgnore] public List<int> DataBitsList { get; set; }

    [JsonIgnore] public List<StopBits> StopBitsList { get; set; }

    [JsonIgnore] public List<Parity> ParityBitsList { get; set; }

    public string SelectedPort { get; set; }
    public int SelectedBaudRate { get; set; }
    public int SelectedDataBits { get; set; }
    public StopBits SelectedStopBits { get; set; }
    public Parity SelectedParity { get; set; }
}