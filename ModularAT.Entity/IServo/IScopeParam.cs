namespace ModularAT.Entity;

public interface IScopeParam
{
    /// <summary>
    ///     Q轴电流反馈值(A)
    /// </summary>
    public int Iq_FB { get; set; }

    /// <summary>
    ///     实际速度(mm/s)
    /// </summary>
    public int Actual_Velocity { get; set; }

    /// <summary>
    ///     实际位置(PosUnit)
    /// </summary>
    public int Actual_Position { get; set; }

    /// <summary>
    ///     母线电压(V)
    /// </summary>
    public short Bus_Voltage { get; set; }
}