namespace ModularAT.Entity;

public interface IErrorRecordParam
{
    public short New_ErrIndex { get; set; }
    public short Pre_ErrIndex { get; set; }
    public ushort His_Err_Code0 { get; set; }
    public ushort His_Err_Code1 { get; set; }
    public ushort His_Err_Code2 { get; set; }
    public ushort His_Err_Code3 { get; set; }
    public ushort His_Err_Code4 { get; set; }
    public ushort His_Err_Code5 { get; set; }
    public ushort His_Err_Code6 { get; set; }
    public ushort His_Err_Code7 { get; set; }
    public ushort His_Err_Code8 { get; set; }
    public ushort His_Err_Code9 { get; set; }
    public ushort His_Err_Code10 { get; set; }
    public ushort His_Err_Code11 { get; set; }
    public ushort His_Err_Code12 { get; set; }
    public ushort His_Err_Code13 { get; set; }
    public ushort His_Err_Code14 { get; set; }
    public ushort His_Err_Code15 { get; set; }
    public ushort His_Err_Code16 { get; set; }
    public ushort His_Err_Code17 { get; set; }
    public ushort His_Err_Code18 { get; set; }

    public ushort His_Err_Code19 { get; set; }
}