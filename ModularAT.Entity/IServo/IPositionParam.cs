namespace ModularAT.Entity;

public interface IPositionParam
{
    /// <summary>
    ///     目标位置(PosUnit)
    /// </summary>
    public uint Target_Position { get; set; }

    /// <summary>
    ///     实际位置(PosUnit)
    /// </summary>
    public uint Actual_Position { get; set; }

    /// <summary>
    ///     位置环比例系数((mm/s)/PosUnit)
    /// </summary>
    public uint Position_Kp { get; set; }

    /// <summary>
    ///     位置环积分系数
    /// </summary>
    public uint Position_Ki { get; set; }

    /// <summary>
    ///     位置环微分系数
    /// </summary>
    public uint Position_Kd { get; set; }

    /// <summary>
    ///     位置指令低通滤波截止频率(Hz)
    /// </summary>
    public uint PILF_Cutoff_Freq { get; set; }

    /// <summary>
    ///     位置控制输出钳位UP(mm/s)
    /// </summary>
    public int PosCtrl_ClamUp { get; set; }

    /// <summary>
    ///     位置控制输出钳位LOW(mm/s)
    /// </summary>
    public int PosCtrl_ClamLow { get; set; }

    /// <summary>
    ///     位置指令均值滤波器截止频率(Hz)
    /// </summary>
    public uint PISA_Cutoff { get; set; }
}