namespace ModularAT.Entity;

public interface IProtectParam
{
    /// <summary>
    ///     短时过载检测阈值(A)
    /// </summary>
    public uint Short_OLD_Threshold { get; set; }

    /// <summary>
    ///     短时过载检测判定时间(ms)
    /// </summary>
    public uint Short_OLD_Time { get; set; }

    /// <summary>
    ///     过载判定电流阈值(Arms)
    /// </summary>
    public uint Sus_OLD_RateCur { get; set; }

    /// <summary>
    ///     过载峰值电流(Arms)
    /// </summary>
    public uint Sus_OLD_PeakCur { get; set; }

    /// <summary>
    ///     允许峰值电流持续时间(ms)
    /// </summary>
    public uint Dur_Of_PeakCur { get; set; }

    /// <summary>
    ///     持续过载升温补偿系数
    /// </summary>
    public uint Heat_Coeff { get; set; }

    /// <summary>
    ///     持续过载冷却补偿系数
    /// </summary>
    public uint Cool_Coeff { get; set; }

    /// <summary>
    ///     电机堵转检测电流阈值(Arms)
    /// </summary>
    public uint Locked_rotor_Current { get; set; }

    /// <summary>
    ///     电机堵转判定时间(ms)
    /// </summary>
    public uint Locked_rotor_Time { get; set; }

    /// <summary>
    ///     电机堵转速度判定阈值(mm/s)
    /// </summary>
    public uint Locked_rotor_Vel { get; set; }

    /// <summary>
    ///     MOS温度报警阈值(℃)
    /// </summary>
    public ushort MOS_Temp { get; set; }

    /// <summary>
    ///     编码器通讯错误次数报警阈值
    /// </summary>
    public ushort Encoder_Commu_Err { get; set; }

    /// <summary>
    ///     电机失速检测阈值(mm/s)
    /// </summary>
    public uint Stall_Dect { get; set; }

    /// <summary>
    ///     过压保护阈值(V)
    /// </summary>
    public uint Over_Voltage { get; set; }

    /// <summary>
    ///     欠压保护阈值(V)
    /// </summary>
    public uint Under_Voltage { get; set; }
    /// <summary>
    ///     电机热时间常数(s)
    /// </summary>
    public uint Thermal_Time_Const { get; set; }
    /// <summary>
    ///     瞬时过流检测阈值(A)
    /// </summary>
    public uint Ins_OCD_Threshold { get; set; }
    
    /// <summary>
    /// 驱动器热时间常数（s）
    /// </summary>
    public uint Servo_Thermal_Time { get; set; }
    /// <summary>
    /// 驱动器温升调整系数
    /// </summary>
    public uint Servo_Heat_Coeff { get; set; }
    /// <summary>
    /// 驱动器散热调整系数
    /// </summary>
    public uint Servo_Cool_Coeff { get; set; }
}