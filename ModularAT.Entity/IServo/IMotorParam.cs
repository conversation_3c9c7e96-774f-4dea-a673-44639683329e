namespace ModularAT.Entity;

public interface IMotorParam
{
    /// <summary>
    ///     电机线电阻(mΩ)
    /// </summary>
    public ushort LL_Resistance { get; set; }

    /// <summary>
    ///     电机线电感(mH)
    /// </summary>
    public ushort LL_Inductance { get; set; }

    /// <summary>
    ///     电机额定电流(Arms)
    /// </summary>
    public ushort Rate_Current { get; set; }

    /// <summary>
    ///     电机额定力矩(N)
    /// </summary>
    public ushort Rate_Torque { get; set; }

    /// <summary>
    ///     电机峰值电流(Arms)
    /// </summary>
    public ushort Peak_Current { get; set; }

    /// <summary>
    ///     电机力矩常数(N/Arms)
    /// </summary>
    public ushort Torque_Constant { get; set; }

    /// <summary>
    ///     电机反电动势系数(V(pk)/m/s)
    /// </summary>
    public ushort Back_Emf_Coeff { get; set; }

    /// <summary>
    ///     电机极对N-N距(mm)
    /// </summary>
    public ushort Electrode_Distance { get; set; }

    /// <summary>
    ///     电机极对数
    /// </summary>
    public ushort Number_Of_Poles { get; set; }

    /// <summary>
    ///     电角度偏移(PosUnit)
    /// </summary>
    public uint Elec_Offset { get; set; }

    /// <summary>
    ///     电机U相电流(A)
    /// </summary>
    public short U_Current { get; set; }

    /// <summary>
    ///     电机V相电流(A)
    /// </summary>
    public short V_Current { get; set; }

    /// <summary>
    ///     电机W相电流(A)
    /// </summary>
    public short W_Current { get; set; }

    /// <summary>
    ///     母线电压(V)
    /// </summary>
    public short Bus_Voltage { get; set; }

    // public uint Pos_Fit_Type { get; set; }
    // public uint Ceg_1 { get; set; }
    // public uint Ceg_2 { get; set; }
    // public uint Enc0_ThL { get; set; }
    // public uint Enc2_ThH { get; set; }
    // public uint Cth_1 { get; set; }
    // public uint Cth_2 { get; set; }
}