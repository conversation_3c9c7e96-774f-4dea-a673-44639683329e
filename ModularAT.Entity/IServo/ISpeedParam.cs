namespace ModularAT.Entity;

public interface ISpeedParam
{
    /// <summary>
    ///     目标速度(mm/s)
    /// </summary>
    public int Target_Velocity { get; set; }

    /// <summary>
    ///     实际速度(mm/s)
    /// </summary>
    public int Actual_Velocity { get; set; }

    /// <summary>
    ///     速度环比例系数(A/(mm/s))
    /// </summary>
    public uint Velocity_Kp { get; set; }

    /// <summary>
    ///     速度环积分系数(A/mm)
    /// </summary>
    public uint Velocity_Ki { get; set; }

    /// <summary>
    ///     速度环微分系数(A/(mm/s^2))
    /// </summary>
    public uint Velocity_Kd { get; set; }

    /// <summary>
    ///     速度环抗积分饱和系数
    /// </summary>
    public uint Velocity_Kc { get; set; }

    /// <summary>
    ///     速度前馈增益系数
    /// </summary>
    public uint Vel_FF_Gain { get; set; }

    /// <summary>
    ///     速度前馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Vel_FFLPF_CutFreq { get; set; }

    /// <summary>
    ///     速度反馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Vel_FBLPF_CutFreq { get; set; }

    /// <summary>
    ///     速度指令低通滤波截止频率(Hz)
    /// </summary>
    public uint VILP_Cutoff_Freq { get; set; }

    /// <summary>
    ///     速度控制输出钳位UP(A)
    /// </summary>
    public int VelCtrl_ClamUp { get; set; }

    /// <summary>
    ///     速度控制输出钳位LOW(A)
    /// </summary>
    public int VelCtrl_ClamLow { get; set; }
}