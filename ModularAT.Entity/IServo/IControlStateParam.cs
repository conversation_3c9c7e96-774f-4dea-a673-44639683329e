namespace ModularAT.Entity;

public interface IControlStateParam
{
    /// <summary>
    ///     控制字
    /// </summary>
    public ushort ControlWord { get; set; }

    /// <summary>
    ///     状态字
    /// </summary>
    public ushort StatusWord { get; set; }

    /// <summary>
    ///     运行状态
    /// </summary>
    public ushort ModeOfOperation { get; set; }

    /// <summary>
    ///     实际状态
    /// </summary>
    public ushort ModesOfOperationDisplay { get; set; }
}