namespace ModularAT.Entity;

public interface ISystemParam
{
    /// <summary>
    ///     驱动版本-芯片型号
    /// </summary>
    public ushort DRIVER_VERSION_0 { get; set; }

    /// <summary>
    ///     驱动版本-大版本迭代
    /// </summary>
    public ushort DRIVER_VERSION_1 { get; set; }

    /// <summary>
    ///     驱动版本-功能迭代
    /// </summary>
    public ushort DRIVER_VERSION_2 { get; set; }

    /// <summary>
    ///     驱动版本-Bug迭代
    /// </summary>
    public ushort DRIVER_VERSION_3 { get; set; }

    /// <summary>
    ///     驱动版本-调试发布(0-调试 1-发布)
    /// </summary>
    public ushort DRIVER_VERSION_4 { get; set; }

    /// <summary>
    ///     示波器控制
    /// </summary>
    public ushort ScopeCtl { get; set; }

    /// <summary>
    ///     示波器通道1
    /// </summary>
    public ushort ScopeMapList0 { get; set; }

    public ushort ScopeMapList1 { get; set; }
    public ushort ScopeMapList2 { get; set; }
    public ushort ScopeMapList3 { get; set; }
    public ushort ScopeMapList4 { get; set; }
    public ushort ScopeMapList5 { get; set; }
    public ushort ScopeMapList6 { get; set; }
    public ushort ScopeMapList7 { get; set; }
}