namespace ModularAT.Entity;

public interface ITorqueParam
{
    /// <summary>
    ///     Q轴电流目标值(A)
    /// </summary>
    public short Iq_CMD { get; set; }

    /// <summary>
    ///     D轴电流目标值(A)
    /// </summary>
    public short Id_CMD { get; set; }

    /// <summary>
    ///     Q轴电流反馈值(A)
    /// </summary>
    public short Iq_FB { get; set; }

    /// <summary>
    ///     D轴电流反馈值(A)
    /// </summary>
    public short Id_FB { get; set; }

    /// <summary>
    ///     电流环比例系数
    /// </summary>
    public uint Current_Kp { get; set; }

    /// <summary>
    ///     电流环积分系数
    /// </summary>
    public uint Current_Ki { get; set; }

    /// <summary>
    ///     电流环微分系数
    /// </summary>
    public uint Current_Kd { get; set; }

    /// <summary>
    ///     D轴反电势补偿系数
    /// </summary>
    public uint Current_Ke_D { get; set; }

    /// <summary>
    ///     Q轴反电势补偿系数
    /// </summary>
    public uint Current_Ke_Q { get; set; }

    /// <summary>
    ///     永磁体反电势补偿系数
    /// </summary>
    public uint Current_Kf { get; set; }

    /// <summary>
    ///     电流反馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Cur_FB_CutFreq { get; set; }

    /// <summary>
    ///     电流指令低通滤波截止频率(Hz)
    /// </summary>
    public uint CILP_CutFreq { get; set; }

    /// <summary>
    ///     电流前馈增益系数
    /// </summary>
    public uint Cur_FF_Gain { get; set; }

    /// <summary>
    ///     电流前馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Cur_FFLPF_CutFreq { get; set; }

    /// <summary>
    ///     电流指令陷波滤波中心频率(Hz)
    /// </summary>
    public uint CINF_NotchFreq { get; set; }

    /// <summary>
    ///     电流指令陷波滤波带宽(Hz)
    /// </summary>
    public uint CINF_CutFreq { get; set; }

    /// <summary>
    ///     电流指令陷波滤波深度(dB)
    /// </summary>
    public uint CINF_Depth { get; set; }
    
    /// <summary>
    /// U相电流采样调整因子
    /// </summary>
    public uint Cur_SA_Factor_U { get; set; }
    
    /// <summary>
    /// V相电流采样调整因子
    /// </summary>
    public uint Cur_SA_Factor_V { get; set; }
}