namespace ModularAT.Entity;

public interface IEncodeParam
{
    /// <summary>
    ///     编码器类型
    /// </summary>
    public ushort EncoderType { get; set; }

    /// <summary>
    ///     编码器分辨率
    /// </summary>
    public uint EncoderResolution { get; set; }

    /// <summary>
    ///     编码器版本-大版本迭代
    /// </summary>
    public ushort EncVersion_Master { get; set; }

    /// <summary>
    ///     编码器版本-功能迭代
    /// </summary>
    public ushort EncVersion_Func { get; set; }

    /// <summary>
    ///     编码器版本-Bug迭代
    /// </summary>
    public ushort EncVersion_Bug { get; set; }

    /// <summary>
    ///     编码器版本-调试版本
    /// </summary>
    public ushort EncVersion_Debug { get; set; }

    /// <summary>
    ///     编码器调试功能选择
    /// </summary>
    public ushort EncDebugFunc { get; set; }

    /// <summary>
    ///     编码器0位置调试接口
    /// </summary>
    public uint EncoderPos0 { get; set; }

    /// <summary>
    ///     编码器1位置调试接口
    /// </summary>
    public uint EncoderPos1 { get; set; }
}