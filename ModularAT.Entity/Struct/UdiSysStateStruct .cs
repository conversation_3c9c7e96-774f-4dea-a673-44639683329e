namespace ModularAT.Entity;

public struct UdiSysStateStruct
{
    /// <summary>
    ///     系统已准备好
    /// </summary>
    public bool Ready { get; set; }

    /// <summary>
    ///     系统使能状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    ///     系统错误状态
    /// </summary>
    public bool Error { get; set; }

    /// <summary>
    ///     系统运行状态
    /// </summary>
    public bool Running { get; set; }

    /// <summary>
    ///     系统总线状态
    /// </summary>
    public bool BusStatus { get; set; }

    /// <summary>
    ///     系统平台校验状态
    /// </summary>
    public bool PlatformVerification { get; set; }

    /// <summary>
    ///     轴配置完成，可进行轴序列初始化
    /// </summary>
    public bool AxisConfigurationComplete { get; set; }

    /// <summary>
    ///     运动参数配置完成，可进行系统旧状态恢复
    /// </summary>
    public bool MotionParametersConfigured { get; set; }

    /// <summary>
    ///     系统恢复旧状态完成
    /// </summary>
    public bool StateRestorationComplete { get; set; }
}