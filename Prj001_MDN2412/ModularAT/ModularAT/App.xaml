<Application
    x:Class="ModularAT.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  MahApps.Metro  -->
                <ResourceDictionary
                    Source="pack://application:,,,/MahApps.Metro;component/Styles/Clean/MetroWindow.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Clean/Controls.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml" />
                <!--  Theme setting  -->
                <ResourceDictionary
                    Source="pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml" />
                <ResourceDictionary
                    Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.FlatButton.xaml" />
                <ResourceDictionary
                    Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.FlatSlider.xaml" />

                <!--  Styles  -->
                <ResourceDictionary Source="/Resources/Styles/DialogStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ButtonStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/CheckBoxStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ContextMenuStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/DataGridStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/DropDownButtonStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/GridSplitterStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/GroupBoxStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/MenuItemStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/MetroDialogStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/NumericUpDownStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/RadioButtonStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ScrollBarStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ScrollViewerStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/TabControlStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/TextBlockStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/TextBoxStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ToogleSwitchStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ToggleButtonStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/ToolTipStyles.xaml" />
                <ResourceDictionary Source="/Resources/Styles/WindowStyles.xaml" />
                <!--  Dependent styles  -->
                <ResourceDictionary Source="/Resources/Styles/ComboBoxStyles.xaml" />
                <!--  Requires scrollbar styles  -->
                <!--<ResourceDictionary Source="/Resources/Styles/DragablzStyles.xaml" />-->
                <!--  Requires button and TextBox styles  -->
                <ResourceDictionary Source="/Resources/Styles/ExpanderStyles.xaml" />
                <!--  Requires button styles  -->
                <ResourceDictionary Source="/Resources/Styles/RectangleStyles.xaml" />
                <!--  Requires tooltip styles  -->
                <ResourceDictionary Source="/Resources/Styles/PasswordBoxStyles.xaml" />
                <!--  Requires tooltip styles  -->
                <ResourceDictionary Source="/Resources/Styles/ListBoxStyle.xaml" />
                <!--  Requires ScrollViewer/ScrollBar styles  -->
                
                

            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>

</Application>