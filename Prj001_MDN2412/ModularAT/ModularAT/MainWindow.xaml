<mah:MetroWindow
    x:Class="ModularAT.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
    xmlns:conv2="clr-namespace:ModularAT.ValueConverter"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:ModularAT.ViewModels"
    Title="{Binding Title}"
    d:DataContext="{d:DesignInstance Type=vm:MainViewModel}"
    MouseUp="MetroWindow_MouseUp"
    TitleCharacterCasing="Normal"
    mc:Ignorable="d">
    <mah:MetroWindow.IconTemplate>
        <DataTemplate>
            <Border
                Margin="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Background="{DynamicResource MahApps.Brushes.IdealForeground}">
                <Image
                    Margin="1"
                    Source="/Resources/Images/modular.gif"
                    Stretch="Uniform" />
            </Border>
        </DataTemplate>
    </mah:MetroWindow.IconTemplate>
    <mah:MetroWindow.RightWindowCommands>
        <mah:WindowCommands>
            <!-- 语言选择 -->
            <DockPanel LastChildFill="False" HorizontalAlignment="Right" VerticalAlignment="Center">
                <TextBlock
                    Text="{Binding LanResources.Language, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{StaticResource CenterTextBlock}"
                    DockPanel.Dock="Left" />
                <ComboBox DockPanel.Dock="Right"  Margin="10,0,10,0" 
                          VerticalContentAlignment="Top"
                          ItemsSource="{Binding  LocalizationInfos}" DisplayMemberPath="Name" SelectedValuePath="Code"
                          SelectedValue="{x:Bind localization:LocalizationManager.Current.Code}">

                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="SelectionChanged">
                            <i:InvokeCommandAction Command="{x:Bind ViewModel.ChangeLanguageCommand}"
                                                   CommandParameter="{Binding SelectedValue, RelativeSource={RelativeSource AncestorType=ComboBox}}" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </ComboBox>
            </DockPanel>
        </mah:WindowCommands>
    </mah:MetroWindow.RightWindowCommands>
    <Window.Resources>
        <ResourceDictionary>
            <conv:BoolToBrushConverter
                x:Key="BoolToBrushConverter"
                FalseValue="Gray"
                TrueValue="Green" />
            <conv:BoolToBrushConverter
                x:Key="AndonMaintainConverter"
                FalseValue="Gray"
                TrueValue="Orange" />
            <conv:BoolToBrushConverter
                x:Key="AndonErrorConverter"
                FalseValue="Gray"
                TrueValue="Red" />
            <conv:BoolToStringConverter
                x:Key="BoolToStringConverter"
                FalseValue="{Binding LanResources.Main_Conn_disconnected, Source={x:Static localization:LocalizationService.Current}}"
                TrueValue="{Binding LanResources.Main_Conn_successful, Source={x:Static localization:LocalizationService.Current}}" />
            <conv:BoolInverter x:Key="BoolReverseConverter" />
            <conv:BoolToVisibilityConverter
                x:Key="Bool2VisibilityConverter"
                FalseValue="Collapsed"
                TrueValue="Visible" />
            <conv:ValueConverterGroup x:Key="BoolReverseToVisibilityConverter">
                <conv:BoolInverter />
                <conv:BoolToVisibilityConverter />
            </conv:ValueConverterGroup>
            <conv2:DrawingColorToBrushConverter x:Key="DrawingColorToBrushConverter" />
            <Thickness x:Key="ControlMargin">5 5 5 5</Thickness>
        </ResourceDictionary>
    </Window.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>


        <!--  控制按钮  -->
        <DockPanel
            Grid.Row="0"
            Grid.ColumnSpan="2"
            Grid.IsSharedSizeScope="True"
            Background="AliceBlue">

            <StackPanel Width="Auto" MinWidth="65">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding ChangeAutoModeCommand}"
                    IsChecked="{Binding IsManual, Converter={StaticResource BoolReverseConverter}}"
                    IsEnabled="{Binding IsManual, Mode=OneWay}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="AllInclusive" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Auto, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>
            <StackPanel Width="Auto" MinWidth="65">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding ChangeManualModeCommand}"
                    IsChecked="{Binding IsManual}"
                    IsEnabled="{Binding IsManual, Mode=OneWay, Converter={StaticResource BoolReverseConverter}}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="HandPointingUp" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Manual, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>
            <Rectangle
                Width="1"
                Height="60"
                VerticalAlignment="Stretch"
                Fill="DimGray" />

            <StackPanel Width="Auto" MinWidth="65"
                        Visibility="{Binding IsManual, Converter={StaticResource BoolReverseToVisibilityConverter}}">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding InitalizeSystemCommand}"
                    IsChecked="{Binding IsInitialize}"
                    IsEnabled="{Binding IsRunning, Mode=OneWay, Converter={StaticResource BoolReverseConverter}}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="Backburger" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Init, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65"
                        Visibility="{Binding IsManual, Converter={StaticResource BoolReverseToVisibilityConverter}}">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    HorizontalAlignment="Center"
                    Command="{Binding StartControlCommand}"
                    IsChecked="{Binding IsRunning}"
                    IsEnabled="{Binding IsInitialize, Mode=OneWay, Converter={StaticResource BoolReverseConverter}}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="MotionPlayOutline" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Start, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65"
                        Visibility="{Binding IsManual, Converter={StaticResource BoolReverseToVisibilityConverter}}">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    HorizontalAlignment="Center"
                    Command="{Binding StopControlCommand}"
                    IsChecked="{Binding IsStop}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="PauseCircleOutline" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Stop, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding EmergencyStopCommand}"
                    IsChecked="{Binding IsEmergency}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="CloseOctagonOutline" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Emergency_stop, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding RestControlCommand}"
                    IsChecked="{Binding IsResetting}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="Replay" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Reset, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65"
                        Visibility="{Binding IsManual, Converter={StaticResource Bool2VisibilityConverter}}">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    HorizontalAlignment="Center"
                    Command="{Binding PowerOnOffCommand}"
                    IsChecked="{Binding IsControlPower}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:Material
                        Width="25"
                        Height="25"
                        Kind="Power" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Enable, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>


            <StackPanel Width="Auto" MinWidth="65" Visibility="Collapsed">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding AxisErrorRestCommand}"
                    IsChecked="{Binding IsAxisResetting}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:PackIconModern
                        Width="25"
                        Height="25"
                        Kind="AxisThree" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Axis_err_reset, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65"
                        Visibility="{Binding IsManual, Converter={StaticResource Bool2VisibilityConverter}}">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding RestartControlSystemCommand}"
                    IsChecked="{Binding IsRestartting}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:PackIconModern
                        Width="25"
                        Height="25"
                        Kind="Refresh" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Sys_restart, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65" Visibility="Collapsed">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding SaveControlDataCommand}"
                    IsChecked="{Binding IsSaving}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:PackIconModern
                        Width="25"
                        Height="25"
                        Kind="Save" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Save, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65" Visibility="Collapsed">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding InitializeAxisArrayCommand}"
                    IsChecked="{Binding IsInitializeAxisArray}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:PackIconModern
                        Width="25"
                        Height="25"
                        Kind="Home" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Station_init, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65" Visibility="Collapsed">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding EnableStationCommand}"
                    IsChecked="{Binding IsEnableStation}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:PackIconModern
                        Width="25"
                        Height="25"
                        Kind="Unlock" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Station_enable, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel Width="Auto" MinWidth="65" Visibility="Collapsed">
                <ToggleButton
                    Width="48"
                    Height="48"
                    Margin="4"
                    Command="{Binding DisableStationCommand}"
                    IsChecked="{Binding IsDisableStation}"
                    Style="{DynamicResource MahApps.Styles.ToggleButton.Circle}">
                    <iconPacks:PackIconModern
                        Width="25"
                        Height="25"
                        Kind="Lock" />
                </ToggleButton>
                <TextBlock HorizontalAlignment="Center"
                           Text="{Binding LanResources.Main_Station_mask, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>


            <!--  安灯StackPanel  -->

            <StackPanel
                Width="Auto" MinWidth="65"
                HorizontalAlignment="Right"
                DockPanel.Dock="Right">
                <Ellipse
                    Width="48"
                    Height="48"
                    Margin="4"
                    Fill="{Binding IsError, Converter={StaticResource AndonErrorConverter}}"
                    Stroke="White" />
                <TextBlock HorizontalAlignment="Center" Margin="{StaticResource ControlMargin}"
                           Text="{Binding LanResources.Main_Fault, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel
                Width="Auto" MinWidth="65"
                HorizontalAlignment="Right"
                DockPanel.Dock="Right">
                <Ellipse
                    Width="48"
                    Height="48"
                    Margin="4"
                    Fill="{Binding IsMaintain, Converter={StaticResource AndonMaintainConverter}}"
                    Stroke="White" />
                <TextBlock HorizontalAlignment="Center" Margin="{StaticResource ControlMargin}"
                           Text="{Binding LanResources.Main_Maint, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>

            <StackPanel
                Width="Auto" MinWidth="65"
                HorizontalAlignment="Right"
                DockPanel.Dock="Right">
                <Ellipse
                    Width="48"
                    Height="48"
                    Margin="4"
                    Fill="{Binding IsNormal, Converter={StaticResource BoolToBrushConverter}}"
                    Stroke="White" />
                <TextBlock HorizontalAlignment="Center" Margin="{StaticResource ControlMargin}"
                           Text="{Binding LanResources.Main_Running, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>


        </DockPanel>

        <!--  左侧菜单  -->


        <Border
            Grid.Row="1"
            Grid.Column="0"
            BorderBrush="Gray"
            BorderThickness="0,0,1,0">
            <ScrollViewer
                Margin="2"
                VerticalScrollBarVisibility="Auto">
                <TreeView
                    Name="menuTreeView"
                    DockPanel.Dock="Left">
                    <TreeViewItem helper:PermissionHelper.HasPerm="/Devices"
                                  Header="{Binding LanResources.Main_Equip_conn, Source={x:Static localization:LocalizationService.Current}}">
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ServoSerialPort"
                            Header="{Binding LanResources.Main_Driver, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ServoSerialPortViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ControlerClient"
                            Header="{Binding LanResources.Main_Ctrl, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ControlerClientViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Plaintext_msg, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="FTP"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Fw_upgrade, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                    </TreeViewItem>
                    <TreeViewItem helper:PermissionHelper.HasPerm="/Controller"
                                  Header="{Binding LanResources.Main_Ctrl, Source={x:Static localization:LocalizationService.Current}}">
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Offline_conf, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/Simulation"
                            Header="{Binding LanResources.Main_Sys_assembly, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:SimulationViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ControlerAxis"
                            Header="{Binding LanResources.Main_Axis_ctrl, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ControlerAxisViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ControlerTranStatus"
                            Header="{Binding LanResources.Main_Conn_stat, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ControlerTranStatusViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Station_ctrl, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ControlerSys"
                            Header="{Binding LanResources.Main_Sys_ctrl, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ControlerSysViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Feedback_info, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Err_fault, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ControlerOnlineConfig"
                            Header="{Binding LanResources.Main_Online_conf, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ControlerOnlineConfigViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Dev_comp, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="Servo"
                            Header="{Binding LanResources.Main_Curve_recip, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ControlerGenerateConfig"
                            Header="{Binding LanResources.Main_Conf_gen, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ControlerGenerateConfigViewModel}" />
                    </TreeViewItem>
                    <TreeViewItem helper:PermissionHelper.HasPerm="/Servo"
                                  Header="{Binding LanResources.Main_Driver, Source={x:Static localization:LocalizationService.Current}}">
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/Servo"
                            Header="{Binding LanResources.Main_Digital_io, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/ServoSetting"
                            Header="{Binding LanResources.Main_Servo_conf, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ServoSettingViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/Scope"
                            Header="{Binding LanResources.Main_Oscillo, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:ScopeViewModel}" />
                    </TreeViewItem>
                    <TreeViewItem helper:PermissionHelper.HasPerm="/Base"
                                  Header="{Binding LanResources.Main_Basic_sett, Source={x:Static localization:LocalizationService.Current}}">
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/BaseRole"
                            Header="{Binding LanResources.Main_Role_mgmt, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:BaseRoleViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/BaseUser"
                            Header="{Binding LanResources.Main_User_mgmt, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:BaseUserViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/BasePermission"
                            Header="{Binding LanResources.Main_Func_list, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:BasePermissionViewModel}" />
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/BasePermAssign"
                            Header="{Binding LanResources.Main_Perm_assign, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:BasePermAssignViewModel}" />
                    </TreeViewItem>
                    <TreeViewItem helper:PermissionHelper.HasPerm="/DataTrace"
                                  Header="{Binding LanResources.Main_Data_trace, Source={x:Static localization:LocalizationService.Current}}">
                        <TreeViewItem
                            helper:PermissionHelper.HasPerm="/DataTrace/OperateLog"
                            Header="{Binding LanResources.Main_Op_log, Source={x:Static localization:LocalizationService.Current}}"
                            Tag="{x:Type vm:OperateLogViewModel}" />
                    </TreeViewItem>
                    <!--<TreeView.ItemContainerStyle>
                            <Style TargetType="TreeViewItem">
                                <Setter Property="IsExpanded" Value="True" />
                                <Style.Triggers>
                                    <Trigger Property="HasItems" Value="False">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="TreeViewItem">
                                                    <Button Command="{Binding SwitchPageCommand}" Content="{TemplateBinding Header}" />
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TreeView.ItemContainerStyle>-->
                    <i:Interaction.Triggers>
                        <!--  默认界面显示  -->
                        <i:EventTrigger EventName="Loaded">
                            <i:InvokeCommandAction Command="{Binding SwitchPageCommand}"
                                                   CommandParameter="{x:Type vm:ControlerClientViewModel}" />
                        </i:EventTrigger>
                        <!--  导航界面切换  -->
                        <i:EventTrigger EventName="SelectedItemChanged"
                                        SourceObject="{Binding ElementName=menuTreeView}">
                            <i:InvokeCommandAction Command="{Binding SwitchPageCommand}"
                                                   CommandParameter="{Binding SelectedItem.Tag, ElementName=menuTreeView}" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                    <TreeView.ItemContainerStyle>
                        <Style TargetType="TreeViewItem">
                            <Setter Property="IsExpanded" Value="True" />
                        </Style>
                    </TreeView.ItemContainerStyle>
                </TreeView>
            </ScrollViewer>
        </Border>

        <!--  用于显示UserControl的区域  -->
        <Border
            Name="contentBorder"
            Grid.Row="1"
            Grid.Column="1">
            <ContentControl Content="{Binding Content}" />
        </Border>

        <!--  底部  -->
        <DockPanel
            Grid.Row="2"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Height="30"
            Background="AliceBlue">
            <!--  左侧控件的StackPanel  -->
            <StackPanel
                HorizontalAlignment="Left"
                DockPanel.Dock="Left"
                Orientation="Horizontal">
                <WrapPanel Visibility="{Binding IsServo, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock
                        Margin="2"
                        VerticalAlignment="Center"
                        Text="{Binding LanResources.Main_Sel_axis_sn, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox
                        Width="75"
                        Margin="20,0,2,0"
                        VerticalAlignment="Center"
                        ItemsSource="{Binding AxisNums}"
                        SelectedValue="{Binding Data.SelectedAxis}" />
                </WrapPanel>
                <TextBlock
                    Margin="10,0,2,0"
                    VerticalAlignment="Center"
                    Foreground="{Binding MsgColor, Converter={StaticResource DrawingColorToBrushConverter}}"
                    Text="{Binding MsgReceived}" />
            </StackPanel>

            <!--  右侧控件的StackPanel  -->

            <StackPanel
                Margin="0,0,5,0"
                HorizontalAlignment="Right"
                DockPanel.Dock="Right"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="{Binding LanResources.Main_Driver_conn, Source={x:Static localization:LocalizationService.Current}}" />
                <Ellipse
                    Width="25"
                    Height="25"
                    VerticalAlignment="Center"
                    Fill="{Binding Data.IsConnectedServo, Converter={StaticResource BoolToBrushConverter}}"
                    Stroke="White">
                    <Ellipse.ToolTip>
                        <ToolTip
                            Content="{Binding Data.IsConnectedServo, Converter={StaticResource BoolToStringConverter}}" />
                    </Ellipse.ToolTip>
                </Ellipse>
                <TextBlock
                    Margin="2"
                    VerticalAlignment="Center"
                    Text="{Binding LanResources.Main_Ctrl_conn, Source={x:Static localization:LocalizationService.Current}}" />
                <Ellipse
                    Width="25"
                    Height="25"
                    VerticalAlignment="Center"
                    Fill="{Binding Data.IsConnectedControler, Converter={StaticResource BoolToBrushConverter}}"
                    Stroke="White">
                    <Ellipse.ToolTip>
                        <ToolTip
                            Content="{Binding Data.IsConnectedControler, Converter={StaticResource BoolToStringConverter}}" />
                    </Ellipse.ToolTip>
                </Ellipse>
            </StackPanel>
        </DockPanel>


    </Grid>
</mah:MetroWindow>