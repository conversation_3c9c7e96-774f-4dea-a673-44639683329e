<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style x:Key="ProfileGridSplitter" TargetType="{x:Type GridSplitter}">
        <Setter Property="Width" Value="7" />
        <Setter Property="Margin" Value="-3,-3" />
        <Setter Property="BorderThickness" Value="3" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Panel.ZIndex" Value="1" />
        <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="ShowsPreview" Value="True" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
    </Style>

</ResourceDictionary>