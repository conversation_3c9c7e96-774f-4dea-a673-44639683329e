<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <Thickness x:Key="ControlMargin">5 5 5 5</Thickness>
    <Style BasedOn="{StaticResource DefaultTextBlock}" TargetType="{x:Type TextBlock}">
        <Setter Property="Margin" Value="{StaticResource ControlMargin}" />
    </Style>
    <Style BasedOn="{StaticResource DefaultTextBox}" TargetType="{x:Type TextBox}">
        <Setter Property="Margin" Value="{StaticResource ControlMargin}" />
        <Setter Property="MaxWidth" Value="180" />
    </Style>
    <Style BasedOn="{StaticResource DefaultNumericUpDown}" TargetType="{x:Type mah:NumericUpDown}">
        <Setter Property="Margin" Value="{StaticResource ControlMargin}" />
        <Setter Property="MaxWidth" Value="180" />
    </Style>
    <Style BasedOn="{StaticResource DefaultComboBox}" TargetType="{x:Type ComboBox}">
        <Setter Property="Margin" Value="{StaticResource ControlMargin}" />
        <Setter Property="MaxWidth" Value="180" />
    </Style>
    <!-- <Style BasedOn="{StaticResource MahApps.Styles.RadioButton.Win10}" TargetType="{x:Type RadioButton}" /> -->
</ResourceDictionary>