<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mahAppsControls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro">
    <Style
        x:Key="ExpanderDownHeader"
        BasedOn="{StaticResource ResourceKey=MahApps.Styles.ToggleButton.ExpanderHeader.Down}"
        TargetType="{x:Type TypeName=ToggleButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TypeName=ToggleButton}">
                    <Border
                        Padding="{TemplateBinding Property=Padding}"
                        Background="{TemplateBinding Property=Background}"
                        BorderBrush="{TemplateBinding Property=BorderBrush}"
                        BorderThickness="{TemplateBinding Property=BorderThickness}">
                        <Grid Background="Transparent" SnapsToDevicePixels="False">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <Rectangle
                                x:Name="Chevron"
                                Grid.Row="0"
                                Grid.Column="0"
                                Width="16"
                                Height="16"
                                VerticalAlignment="Center"
                                Fill="{DynamicResource ResourceKey=MahApps.Brushes.Gray3}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=ChevronUp}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <mahAppsControls:ContentControlEx
                                Grid.Column="1"
                                Margin="10,0,0,0"
                                Padding="{TemplateBinding Property=Padding}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="{TemplateBinding Property=HorizontalContentAlignment}"
                                VerticalContentAlignment="{TemplateBinding Property=VerticalContentAlignment}"
                                Content="{TemplateBinding Property=Content}"
                                ContentCharacterCasing="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=(mahAppsControls:ControlsHelper.ContentCharacterCasing)}"
                                ContentStringFormat="{TemplateBinding Property=ContentStringFormat}"
                                ContentTemplate="{TemplateBinding Property=ContentTemplate}"
                                ContentTemplateSelector="{TemplateBinding Property=ContentTemplateSelector}"
                                RecognizesAccessKey="True"
                                SnapsToDevicePixels="{TemplateBinding Property=SnapsToDevicePixels}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="Chevron" Property="OpacityMask">
                                <Setter.Value>
                                    <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=ChevronDown}" />
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Chevron" Property="Fill"
                                    Value="{DynamicResource ResourceKey=MahApps.Brushes.Gray5}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="DefaultExpander"
        BasedOn="{StaticResource ResourceKey=MahApps.Styles.Expander}"
        TargetType="{x:Type TypeName=Expander}">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="ExpandDirection" Value="Down" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="mahAppsControls:HeaderedControlHelper.HeaderBackground" Value="Transparent" />
        <Setter Property="mahAppsControls:HeaderedControlHelper.HeaderForeground"
                Value="{StaticResource ResourceKey=MahApps.Brushes.Gray3}" />
        <Setter Property="mahAppsControls:ControlsHelper.ContentCharacterCasing" Value="Normal" />
        <Setter Property="mahAppsControls:ExpanderHelper.HeaderDownStyle"
                Value="{DynamicResource ResourceKey=ExpanderDownHeader}" />
    </Style>

    <Style
        x:Key="ProfileExpanderOpen"
        BasedOn="{StaticResource ResourceKey=MahApps.Styles.ToggleButton.ExpanderHeader.Down}"
        TargetType="{x:Type TypeName=ToggleButton}">
        <Setter Property="Template" Value="{x:Null}" />
    </Style>

    <Style
        x:Key="ProfileExpanderClosed"
        BasedOn="{StaticResource ResourceKey=MahApps.Styles.ToggleButton.ExpanderHeader.Right}"
        TargetType="{x:Type TypeName=ToggleButton}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TypeName=ToggleButton}">
                    <Border
                        Padding="{TemplateBinding Property=Padding}"
                        Background="{TemplateBinding Property=Background}"
                        BorderBrush="{TemplateBinding Property=BorderBrush}"
                        BorderThickness="{TemplateBinding Property=BorderThickness}">
                        <Grid Background="Transparent" SnapsToDevicePixels="False">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Rectangle
                                x:Name="Chevron"
                                Grid.Row="0"
                                Width="16"
                                Height="16"
                                Margin="10,15,10,10"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Fill="{DynamicResource ResourceKey=MahApps.Brushes.Gray3}">
                                <Rectangle.OpacityMask>
                                    <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=ChevronLeft}" />
                                </Rectangle.OpacityMask>
                            </Rectangle>
                            <TextBlock
                                Grid.Row="1"
                                Margin="0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="{TemplateBinding Property=Foreground}"
                                Style="{StaticResource ResourceKey=DefaultTextBlock}"
                                Text="{TemplateBinding Property=Content}"
                                TextOptions.TextFormattingMode="Display">
                                <TextBlock.LayoutTransform>
                                    <RotateTransform Angle="-90" />
                                </TextBlock.LayoutTransform>
                            </TextBlock>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Chevron" Property="Fill"
                                    Value="{DynamicResource ResourceKey=MahApps.Brushes.Gray5}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="ProfileExpander"
        BasedOn="{StaticResource ResourceKey=MahApps.Styles.Expander}"
        TargetType="{x:Type TypeName=Expander}">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="ExpandDirection" Value="Right" />
        <Setter Property="Padding" Value="10" />
        <Setter Property="mahAppsControls:HeaderedControlHelper.HeaderBackground" Value="Transparent" />
        <Setter Property="mahAppsControls:HeaderedControlHelper.HeaderForeground"
                Value="{StaticResource ResourceKey=MahApps.Brushes.Gray3}" />
        <Setter Property="mahAppsControls:ControlsHelper.ContentCharacterCasing" Value="Normal" />
        <Setter Property="mahAppsControls:ExpanderHelper.HeaderRightStyle"
                Value="{DynamicResource ResourceKey=ProfileExpanderClosed}" />
        <Style.Triggers>
            <Trigger Property="IsExpanded" Value="True">
                <Setter Property="ExpandDirection" Value="Down" />
                <Setter Property="mahAppsControls:ExpanderHelper.HeaderDownStyle"
                        Value="{DynamicResource ResourceKey=ProfileExpanderOpen}" />
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>