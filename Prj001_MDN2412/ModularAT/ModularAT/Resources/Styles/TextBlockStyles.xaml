<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style x:Key="DefaultTextBlock" TargetType="{x:Type TextBlock}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Text}" />
    </Style>

    <Style
        x:Key="HeaderTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource MahApps.Fonts.Family.Control}" />
        <Setter Property="Foreground" Value="{StaticResource MahApps.Brushes.Accent}" />
        <Setter Property="FontSize" Value="20" />
        <Setter Property="Margin" Value="0,0,0,5" />
    </Style>

    <Style
        x:Key="InfoTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style
        x:Key="ProfileGroupTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style
        x:Key="AccentTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Accent}" />
    </Style>

    <Style
        x:Key="ErrorTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Validation5}" />
    </Style>

    <Style
        x:Key="MessageTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource MahApps.Fonts.Family.Header}" />
        <Setter Property="FontSize" Value="18" />
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="TextWrapping" Value="Wrap" />
        <Setter Property="TextAlignment" Value="Center" />
        <Setter Property="Margin" Value="10,0" />
    </Style>

    <Style
        x:Key="BoldTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="FontWeight" Value="Bold" />
    </Style>

    <Style
        x:Key="WrapTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="TextWrapping" Value="Wrap" />
    </Style>

    <Style
        x:Key="StatusMessageTextBlock"
        BasedOn="{StaticResource WrapTextBlock}"
        TargetType="{x:Type TextBlock}" />

    <!--  TextBlock style for buttons with images and text  -->
    <Style
        x:Key="ButtonWithImageTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="Margin" Value="10,5" />
        <Setter Property="TextAlignment" Value="Center" />
    </Style>

    <Style
        x:Key="CenterTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <Style
        x:Key="LinkTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Accent}" />
        <Setter Property="Cursor" Value="Hand" />
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Accent2}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="DisabledTextBlock"
        BasedOn="{StaticResource DefaultTextBlock}"
        TargetType="{x:Type TextBlock}">
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Opacity" Value="0.55" />
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>