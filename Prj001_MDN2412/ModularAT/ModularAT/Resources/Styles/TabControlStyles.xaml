<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style x:Key="DefaultTabItem" TargetType="{x:Type TabItem}">
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Gray3}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Height" Value="34" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="Padding" Value="10,0" />
        <Setter Property="MinWidth" Value="150" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabItem}">
                    <Grid
                        x:Name="PART_Grid"
                        Margin="0"
                        Background="{TemplateBinding Background}"
                        SnapsToDevicePixels="True">
                        <ContentPresenter
                            x:Name="PART_HeaderContent"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            ContentSource="Header"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                            TextBlock.FontFamily="{StaticResource MahApps.Fonts.Family.Header}"
                            TextBlock.FontSize="18" />
                        <!--<Rectangle x:Name="PART_Selector" VerticalAlignment="Bottom" Height="2" Visibility="Collapsed" Fill="{DynamicResource MahApps.Brushes.Accent}" />-->
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger SourceName="PART_Grid" Property="IsMouseOver" Value="True">
                            <Trigger.Setters>
                                <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Gray5}" />
                            </Trigger.Setters>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Trigger.Setters>
                                <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.Accent}" />
                                <!-- <Setter TargetName="PART_Selector" Property="Visibility" Value="Visible" /> -->
                            </Trigger.Setters>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="DefaultTabControl" TargetType="{x:Type TabControl}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0,1,0,0" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="ItemContainerStyle" Value="{StaticResource DefaultTabItem}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <DockPanel LastChildFill="True">
                        <Grid
                            x:Name="HeaderGrid"
                            Margin="0,-3,0,0"
                            DockPanel.Dock="Left">
                            <UniformGrid
                                x:Name="HeaderPanel"
                                HorizontalAlignment="Stretch"
                                Panel.ZIndex="1"
                                Columns="{Binding Items.Count, RelativeSource={RelativeSource FindAncestor, AncestorType=TabControl}}"
                                IsItemsHost="True"
                                KeyboardNavigation.TabIndex="1" />
                        </Grid>
                        <Border
                            x:Name="ContentPanel"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            KeyboardNavigation.DirectionalNavigation="Contained"
                            KeyboardNavigation.TabIndex="2"
                            KeyboardNavigation.TabNavigation="Local"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                            <ContentPresenter
                                x:Name="PART_SelectedContentHost"
                                ContentSource="SelectedContent"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                UseLayoutRounding="False" />
                        </Border>
                    </DockPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="TabStripPlacement" Value="Top">
                            <Setter TargetName="HeaderGrid" Property="DockPanel.Dock" Value="Top" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ProfileTabItem" TargetType="{x:Type TabItem}">
        <Setter Property="Visibility" Value="Collapsed" />
    </Style>
</ResourceDictionary>