<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style
        x:Key="DefaultScrollBar"
        BasedOn="{StaticResource MahApps.Styles.ScrollBar}"
        TargetType="{x:Type ScrollBar}">
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto" />
                <Setter Property="Height" Value="8" />
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Width" Value="8" />
                <Setter Property="Height" Value="Auto" />
            </Trigger>
            <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self}, Path=IsMouseOver}" Value="False">
                <Setter Property="Opacity" Value="0.25" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style BasedOn="{StaticResource ResourceKey=DefaultScrollBar}" TargetType="{x:Type TypeName=ScrollBar}" />
</ResourceDictionary>