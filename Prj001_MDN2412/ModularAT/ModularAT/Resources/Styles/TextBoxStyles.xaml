<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <Style
        x:Key="DefaultTextBox"
        BasedOn="{StaticResource MahApps.Styles.TextBox}"
        TargetType="{x:Type TextBox}">
        <Setter Property="FontSize" Value="14" />
        <!-- <Setter Property="BorderThickness" Value="0,0,0,1" /> -->
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="mah:ControlsHelper.FocusBorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="ContextMenu" Value="{StaticResource CutCopyPasteContextMenu}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource DefaultErrorTemplate}" />
    </Style>

    <!--  Apply the default design to all TextBox  -->
    <Style BasedOn="{StaticResource DefaultTextBox}" TargetType="{x:Type TextBox}" />

    <Style
        x:Key="ConsoleTextBox"
        BasedOn="{StaticResource DefaultTextBox}"
        TargetType="{x:Type TextBox}">
        <Setter Property="FontFamily" Value="Consolas" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
    </Style>

    <Style
        x:Key="SearchTextBox"
        BasedOn="{StaticResource MahApps.Styles.TextBox.Search}"
        TargetType="{x:Type TextBox}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="mah:ControlsHelper.FocusBorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <!--<Setter Property="ContextMenu" Value="{StaticResource CutCopyPasteContextMenu}" />-->
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource DefaultErrorTemplate}" />
        <Setter Property="mah:TextBoxHelper.Watermark" Value="SearchDots" />
        <Setter Property="mah:TextBoxHelper.ClearTextButton" Value="True" />
        <Style.Triggers>
            <Trigger Property="mah:TextBoxHelper.HasText" Value="True">
                <Setter Property="mah:TextBoxHelper.ButtonContentTemplate" Value="{x:Null}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style
        x:Key="ReadOnlyTextBox"
        BasedOn="{StaticResource DefaultTextBox}"
        TargetType="{x:Type TextBox}">
        <Setter Property="IsReadOnly" Value="True" />
    </Style>

    <Style
        x:Key="TextBlockAsTextBox"
        BasedOn="{StaticResource DefaultTextBox}"
        TargetType="{x:Type TextBox}">
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="IsReadOnly" Value="True" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsTabStop" Value="False" />
        <Setter Property="TextWrapping" Value="Wrap" />
    </Style>

    <Style
        x:Key="BrowseFolderTextBox"
        BasedOn="{StaticResource MahApps.Styles.TextBox.Button}"
        TargetType="{x:Type TextBox}">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderBrush" Value="{DynamicResource MahApps.Brushes.Gray8}" />
        <!--<Setter Property="ContextMenu" Value="{StaticResource CutCopyPasteContextMenu}" />-->
        <Setter Property="Validation.ErrorTemplate" Value="{StaticResource DefaultErrorTemplate}" />
        <Setter Property="mah:TextBoxHelper.ButtonContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <!--  Modern - FolderOpen  -->
                    <ContentControl
                        x:Name="PART_PackIcon"
                        Width="{Binding RelativeSource={RelativeSource AncestorType=TextBox}, Path=(mah:TextBoxHelper.ButtonWidth), Mode=OneWay}"
                        Height="{Binding RelativeSource={RelativeSource AncestorType=TextBox}, Path=(mah:TextBoxHelper.ButtonWidth), Mode=OneWay}"
                        Padding="3"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Content="F1 M 19,50L 28,34L 63,34L 54,50L 19,50 Z M 19,28.0001L 35,28C 36,25 37.4999,24.0001 37.4999,24.0001L 48.75,24C 49.3023,24 50,24.6977 50,25.25L 50,28L 53.9999,28.0001L 53.9999,32L 27,32L 19,46.4L 19,28.0001 Z "
                        Style="{DynamicResource MahApps.Styles.ContentControl.PathIcon}" />
                </DataTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>