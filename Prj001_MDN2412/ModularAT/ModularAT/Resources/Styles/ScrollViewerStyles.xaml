<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style
        x:Key="DefaultScrollViewer"
        BasedOn="{StaticResource MahApps.Styles.ScrollViewer}"
        TargetType="{x:Type ScrollViewer}">
        <Setter Property="PanningMode" Value="Both" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Hidden" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <ScrollContentPresenter Grid.RowSpan="1" Grid.ColumnSpan="1" />
                        <ScrollBar
                            Name="PART_VerticalScrollBar"
                            Grid.Row="0"
                            Grid.Column="1"
                            Margin="10,0,0,0"
                            HorizontalAlignment="Right"
                            Maximum="{TemplateBinding ScrollableHeight}"
                            ViewportSize="{TemplateBinding ViewportHeight}"
                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                            Value="{TemplateBinding VerticalOffset}" />
                        <ScrollBar
                            x:Name="PART_HorizontalScrollBar"
                            Grid.Row="1"
                            Grid.Column="0"
                            Margin="0,10,0,0"
                            Maximum="{TemplateBinding ScrollableWidth}"
                            Orientation="Horizontal"
                            ViewportSize="{TemplateBinding ViewportWidth}"
                            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                            Value="{TemplateBinding HorizontalOffset}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource DefaultScrollViewer}" TargetType="{x:Type ScrollViewer}" />

    <Style
        x:Key="FadeOutScrollViewer"
        BasedOn="{StaticResource MahApps.Styles.ScrollViewer}"
        TargetType="{x:Type ScrollViewer}">
        <Setter Property="VerticalScrollBarVisibility" Value="Hidden" />
        <Setter Property="PanningMode" Value="VerticalOnly" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <ScrollContentPresenter
                            Grid.Row="0"
                            Grid.RowSpan="2"
                            Grid.Column="0"
                            Grid.ColumnSpan="2" />
                        <ScrollBar
                            Name="PART_VerticalScrollBar"
                            Grid.Row="0"
                            Grid.Column="1"
                            HorizontalAlignment="Right"
                            Maximum="{TemplateBinding Property=ScrollableHeight}"
                            ViewportSize="{TemplateBinding Property=ViewportHeight}"
                            Visibility="{TemplateBinding Property=ComputedVerticalScrollBarVisibility}"
                            Value="{TemplateBinding Property=VerticalOffset}" />
                        <ScrollBar
                            x:Name="PART_HorizontalScrollBar"
                            Grid.Row="1"
                            Grid.Column="0"
                            Maximum="{TemplateBinding Property=ScrollableWidth}"
                            Orientation="Horizontal"
                            ViewportSize="{TemplateBinding Property=ViewportWidth}"
                            Visibility="{TemplateBinding Property=ComputedHorizontalScrollBarVisibility}"
                            Value="{TemplateBinding Property=HorizontalOffset}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
                <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
            </Trigger>
        </Style.Triggers>
    </Style>
</ResourceDictionary>