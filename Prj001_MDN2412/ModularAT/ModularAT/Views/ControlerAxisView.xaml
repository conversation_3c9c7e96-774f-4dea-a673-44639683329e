<UserControl
    x:Class="ModularAT.Views.ControlerAxisView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:controller="clr-namespace:ModularAT.Entity.Controller;assembly=ModularAT.Entity"
    xmlns:conv="clr-namespace:ModularAT.ValueConverter"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:local="clr-namespace:ModularAT.Views"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:common="clr-namespace:ModularAT.UiCommon"
    xmlns:mapper="clr-namespace:ModularAT.Mapper"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:sys="using:System"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance Type=viewmodels:ControlerAxisViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <Style BasedOn="{StaticResource MahApps.Styles.RadioButton.Win10}" TargetType="{x:Type RadioButton}" />
            <conv:IntToBooleanConverter x:Key="IntegerToBoolConverter" />
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>


    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <TextBlock
            Grid.Row="0"
            Grid.ColumnSpan="2"
            Style="{StaticResource ResourceKey=HeaderTextBlock}"
            Text="{Binding LanResources.ControlerAxis_Mover_axis_ctrl, Source={x:Static localization:LocalizationService.Current}}" />
        <!-- 参数设置汇总 -->
        <Grid Grid.Row="1" Grid.Column="0" HorizontalAlignment="Left">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <!-- 模式设置 -->
            <StackPanel
                Grid.Row="1"
                Grid.Column="0"
                MinWidth="180"
                Margin="10" HorizontalAlignment="Left">
                <TextBlock
                    Text="{Binding LanResources.ControlerAxis_Axis_mot_mode, Source={x:Static localization:LocalizationService.Current}}" />
                <UniformGrid Columns="2" Rows="2" HorizontalAlignment="Left">
                    <RadioButton
                        Command="{Binding SetRunTypeCommand}"
                        Content="{Binding LanResources.ControlerAxis_Jog_mot, Source={x:Static localization:LocalizationService.Current}}"
                        GroupName="RunType"
                        IsChecked="{Binding AxisCtrlCmdModel.AxisRunMode, Converter={StaticResource IntegerToBoolConverter}, ConverterParameter=0}" />
                    <RadioButton
                        Command="{Binding SetRunTypeCommand}"
                        Content="{Binding LanResources.ControlerAxis_Abs_mot, Source={x:Static localization:LocalizationService.Current}}"
                        GroupName="RunType"
                        IsChecked="{Binding AxisCtrlCmdModel.AxisRunMode, Converter={StaticResource IntegerToBoolConverter}, ConverterParameter=1}" />
                    <RadioButton
                        Command="{Binding SetRunTypeCommand}"
                        Content="{Binding LanResources.ControlerAxis_Rel_mot, Source={x:Static localization:LocalizationService.Current}}"
                        GroupName="RunType"
                        IsChecked="{Binding AxisCtrlCmdModel.AxisRunMode, Converter={StaticResource IntegerToBoolConverter}, ConverterParameter=2}" />
                    <RadioButton
                        Command="{Binding SetRunTypeCommand}"
                        Content="{Binding LanResources.ControlerAxis_Station_mot, Source={x:Static localization:LocalizationService.Current}}"
                        GroupName="RunType"
                        IsChecked="{Binding AxisCtrlCmdModel.AxisRunMode, Converter={StaticResource IntegerToBoolConverter}, ConverterParameter=3}" />
                </UniformGrid>
                <TextBlock
                    Text="{Binding LanResources.ControlerAxis_Axis_id, Source={x:Static localization:LocalizationService.Current}}" />
                <!-- <mah:NumericUpDown -->
                <!--     Maximum="255" -->
                <!--     Minimum="0" -->
                <!--     Value="{Binding AxisCtrlCmdModel.AxisID}" /> -->
                <ComboBox HorizontalAlignment="Left" MinWidth="180"
                          ItemsSource="{x:Bind common:GlobalParamsHelper.GetMovers()}"
                          SelectedIndex="{Binding AxisCtrlCmdModel.AxisID}" />
                <TextBlock
                    Text="{Binding LanResources.ControlerAxis_Axis_type, Source={x:Static localization:LocalizationService.Current}}"
                    Visibility="Collapsed" />
                <ComboBox SelectedIndex="{Binding AxisCtrlCmdModel.AxisType}"
                          Visibility="Collapsed">
                    <ComboBoxItem
                        Content="{Binding LanResources.ControlerAxis_Mover, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                        Content="{Binding LanResources.ControlerAxis_Rotary_motor, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem
                        Content="{Binding LanResources.ControlerAxis_Linear_motor, Source={x:Static localization:LocalizationService.Current}}" />
                </ComboBox>
                <TextBlock
                    Text="{Binding LanResources.ControlerAxis_Speed_mode, Source={x:Static localization:LocalizationService.Current}}" />
                <!--<ComboBox SelectedIndex="{Binding AxisCtrlCmdModel.VelMode}">
                        <ComboBoxItem Content="T" />
                        <ComboBoxItem Content="S5" />
                    </ComboBox>-->
                <UniformGrid Columns="3" HorizontalAlignment="Left">
                    <RadioButton
                        Content="T"
                        GroupName="CmdModel"
                        IsChecked="{Binding AxisCtrlCmdModel.VelMode, Converter={StaticResource IntegerToBoolConverter}, ConverterParameter=0}" />
                    <RadioButton
                        Content="S5"
                        GroupName="CmdModel"
                        IsChecked="{Binding AxisCtrlCmdModel.VelMode, Converter={StaticResource IntegerToBoolConverter}, ConverterParameter=1}" />
                </UniformGrid>
                <TextBlock
                    Text="{Binding LanResources.ControlerAxis_Axis_ctrl_mode, Source={x:Static localization:LocalizationService.Current}}" />
                <ComboBox HorizontalAlignment="Left" MinWidth="180"
                          DisplayMemberPath="Key"
                          ItemsSource="{Binding AxisCtrlDic}"
                          SelectedValue="{Binding AxisCtrlCmdModel.AxisCtrl, UpdateSourceTrigger=LostFocus}"
                          SelectedValuePath="Value" />

            </StackPanel>
            <!-- 参数设置 -->
            <ScrollViewer Grid.Row="1" Grid.Column="1">
                <StackPanel MinWidth="180"  HorizontalAlignment="Left">
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Target_line_id, Source={x:Static localization:LocalizationService.Current}}"
                        Visibility="{Binding SetLineVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <ComboBox
                        ItemsSource="{x:Bind common:GlobalParamsHelper.GetLines()}"
                        SelectedIndex="{Binding AxisCtrlCmdModel.AxisTargetObjectID}"
                        Visibility="{Binding SetLineVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Target_station_id, Source={x:Static localization:LocalizationService.Current}}"
                        Visibility="{Binding SetStationVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <ComboBox
                        ItemsSource="{x:Bind common:GlobalParamsHelper.GetStations(true)}"
                        SelectedIndex="{Binding AxisCtrlCmdModel.AxisTargetStationID}"
                        Visibility="{Binding SetStationVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Speed, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.AxisSetVel}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Accel, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.AxisSetAcc}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Decel, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.AxisSetDec}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Jerk, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.AxisSetJerk}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Pos_accu, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.ILrSetLocationPrecision}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Anti_coll_accu, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.ILrSetAntiCollPrecision}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Target_pos, Source={x:Static localization:LocalizationService.Current}}"
                        Visibility="{Binding SetPosVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <TextBox Text="{Binding AxisCtrlCmdModel.AxisTarPos}"
                             Visibility="{Binding SetPosVisible, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerAxis_Sel_op, Source={x:Static localization:LocalizationService.Current}}" />
                    <StackPanel Orientation="Horizontal">
                        <Button
                            helper:PermissionHelper.HasPerm="/ControlerAxis/Execute"
                            Command="{Binding ExecCommand}"
                            Content="{Binding LanResources.ControlerAxis_Exec, Source={x:Static localization:LocalizationService.Current}}" />
                        <Button
                            Command="{Binding ReadCommand}"
                            Content="{Binding LanResources.ControlerAxis_Read, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Collapsed" />
                        <Button
                            helper:PermissionHelper.HasPerm="/ControlerAxis/Stop"
                            Command="{Binding StopCommand}"
                            Content="{Binding LanResources.ControlerAxis_Stop, Source={x:Static localization:LocalizationService.Current}}" />
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Grid>
        <!-- 状态反馈表格 -->
        <ListView
            HorizontalAlignment="Right"
            Grid.Row="1"
            Grid.Column="1"
            ItemsSource="{x:Bind ViewModel.AxisFeedBacks, IsItemsSource=True, Mode=OneWay}">
            <ListView.View>
                <GridView>
                    <GridView.ColumnHeaderContainerStyle>
                        <Style TargetType="GridViewColumnHeader">
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_id, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind IAxisID, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_obj, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind IAxisCurObject, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_line, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind IAxisCurObjectID, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Driver_err, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind UiAxisDrvErrCode, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_err, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind UiAxisMotionErrCode, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_curr_pos_mm, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind sys:Convert.ToDouble(DiAxisCurPos*0.001), Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_curr_speed, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock Text="{x:Bind DiAxisCurVel, Mode=OneWay}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn
                        Header="{Binding LanResources.ControlerAxis_Axis_curr_stat, Source={x:Static localization:LocalizationService.Current}}"
                        Width="Auto">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate DataType="controller:AxisFeedBackModel">
                                <TextBlock
                                    Text="{x:Bind UdiAxisRunState, Mode=OneWay}"
                                    ToolTipService.HorizontalOffset="10"
                                    ToolTipService.Placement="Mouse"
                                    ToolTipService.ToolTip="{x:Bind mapper:AxisFeedBackMapping.GetUdiAxisRunStateOctDesc(UdiAxisRunState)}"
                                    ToolTipService.VerticalOffset="10" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
            <ListView.ItemContainerStyle>
                <Style BasedOn="{StaticResource MahApps.Styles.ListViewItem}" TargetType="{x:Type ListViewItem}">
                    <Setter Property="mah:ItemHelper.HoverBackgroundBrush" Value="Transparent" />
                    <Setter Property="mah:ItemHelper.HoverSelectedBackgroundBrush" Value="Transparent" />
                    <Setter Property="mah:ItemHelper.SelectedBackgroundBrush" Value="Transparent" />
                    <Setter Property="mah:ItemHelper.ActiveSelectionBackgroundBrush" Value="Transparent" />
                    <Setter Property="Background" Value="Transparent" />
                </Style>
            </ListView.ItemContainerStyle>
        </ListView>
        <local:ControlerDebug
            Grid.Row="2"
            Grid.ColumnSpan="2"
            DataContext="{Binding DebugViewModel}" />

    </Grid>
</UserControl>