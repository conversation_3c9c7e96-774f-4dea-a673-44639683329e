<UserControl
    x:Class="ModularAT.Views.ControlerGenerateConfigView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance Type=viewmodels:ControlerGenerateConfigViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" MinWidth="250" />
        </Grid.ColumnDefinitions>
        <StackPanel Grid.Row="0" Grid.Column="0" >
            <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}" Text="{Binding LanResources.ControlerGenerateConfig_Conf_gen, Source={x:Static localization:LocalizationService.Current}}" />
            <StackPanel
                MinWidth="180"
                Margin="10"
                HorizontalAlignment="Left">
                <TextBlock  Text="{Binding LanResources.ControlerGenerateConfig_Sys_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown IsEnabled="False" Value="{Binding ConfigNum.SystemCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Motor_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="255"
                    Minimum="0"
                    Value="{Binding ConfigNum.MotorCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Slave_node_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="255"
                    Minimum="0"
                    Value="{Binding ConfigNum.SlaveNodeCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Line_seg_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="255"
                    Minimum="0"
                    Value="{Binding ConfigNum.LineSegmentCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Station_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="21"
                    Minimum="0"
                    Value="{Binding ConfigNum.StationCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Mover_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="255"
                    Minimum="0"
                    Value="{Binding ConfigNum.MoverCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Rot_axis_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="255"
                    Minimum="0"
                    Value="{Binding ConfigNum.RotaryAxisCount}" />

                <TextBlock Text="{Binding LanResources.ControlerGenerateConfig_Io_conf_num, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:NumericUpDown
                    Maximum="255"
                    Minimum="0"
                    Value="{Binding ConfigNum.IoCount}" />

                <Button
                    helper:PermissionHelper.HasPerm="/ControlerSys/Execute"
                    Command="{Binding GenerateConfigCommand}"
                    Content="{Binding LanResources.ControlerGenerateConfig_Gen_conf_file, Source={x:Static localization:LocalizationService.Current}}" />

            </StackPanel>
        </StackPanel>

    </Grid>
</UserControl>