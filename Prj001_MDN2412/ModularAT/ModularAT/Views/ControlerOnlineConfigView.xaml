<UserControl
    x:Class="ModularAT.Views.ControlerOnlineConfigView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:localenum="clr-namespace:ModularAT.Entity.Enum;assembly=ModularAT.Entity"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance Type=viewmodels:ControlerOnlineConfigViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}" TargetType="{x:Type TypeName=Button}">
            <Setter Property="Margin" Value="0,10,0,0" />
        </Style>
        <CollectionViewSource x:Key="cvParameters" Source="{Binding OnlineParameters}">
            <CollectionViewSource.GroupDescriptions>
                <PropertyGroupDescription PropertyName="Title" />
            </CollectionViewSource.GroupDescriptions>
        </CollectionViewSource>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}" Text="{Binding LanResources.ControlerOnlineConfig_Online_conf, Source={x:Static localization:LocalizationService.Current}}" />

        <StackPanel
            Grid.Row="1"
            Grid.Column="0"
            MinWidth="180"
            Margin="5">
            <TextBlock
                Margin="2"
                VerticalAlignment="Center"
                Text="{Binding LanResources.ControlerOnlineConfig_Sel_conf, Source={x:Static localization:LocalizationService.Current}}" />
            <Button
                helper:PermissionHelper.HasPerm="/ServoSetting/SetPara"
                Command="{Binding Path=LoadParamsCommand}"
                CommandParameter="{x:Static localenum:LineConfigEnum.SysCfgPara}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Select}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>-->
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerOnlineConfig_Sys_conf, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button
                helper:PermissionHelper.HasPerm="/ServoSetting/SetParamsAll"
                Command="{Binding Path=LoadParamsCommand}"
                CommandParameter="{x:Static localenum:LineConfigEnum.StationCfgPara}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=SelectAll}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>-->
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerOnlineConfig_Station_conf, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button
                helper:PermissionHelper.HasPerm="/ServoSetting/SetParamsAll"
                Command="{Binding Path=SendStationConfigsCommand}"
                CommandParameter="{x:Static localenum:LineConfigEnum.StationCfgPara}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Select}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>-->
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerOnlineConfig_Write, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>


        </StackPanel>
        <DataGrid
            x:Name="paraDataGrid"
            Grid.Row="1"
            Grid.Column="1"
            Margin="10,0,10,0"
            CanUserAddRows="False"
            CanUserSortColumns="False"
            ItemsSource="{Binding Source={StaticResource cvParameters}}"
            SelectionMode="Extended"
            VirtualizingStackPanel.IsVirtualizing="True">
            <!--  Height="{Binding ActualHeight, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=Border}}"  -->
            <DataGrid.GroupStyle>
                <GroupStyle>
                    <GroupStyle.ContainerStyle>
                        <Style TargetType="{x:Type GroupItem}">
                            <Setter Property="Margin" Value="0,0,0,5" />
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="{x:Type GroupItem}">
                                        <Expander
                                            Background="AliceBlue"
                                            BorderBrush="AliceBlue"
                                            BorderThickness="1,1,1,5"
                                            IsExpanded="True">
                                            <Expander.Header>
                                                <DockPanel>
                                                    <TextBlock VerticalAlignment="Center" Text="{Binding Path=Name}" />
                                                </DockPanel>
                                            </Expander.Header>
                                            <Expander.Content>
                                                <ItemsPresenter />
                                            </Expander.Content>
                                        </Expander>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </GroupStyle.ContainerStyle>
                </GroupStyle>
            </DataGrid.GroupStyle>
            <DataGrid.Columns>
                <DataGridTextColumn
                    Binding="{Binding EnglishName}"
                    Header="{Binding LanResources.ControlerOnlineConfig_Param_name, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding DataType}"
                    Header="{Binding LanResources.ControlerOnlineConfig_Set_type, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Path=ReadValue}"
                    Header="{Binding LanResources.ControlerOnlineConfig_Read_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn Binding="{Binding Path=Value}" Header="{Binding LanResources.ControlerOnlineConfig_Set_val, Source={x:Static localization:LocalizationService.Current}}" />
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Describe}"
                    Header="{Binding LanResources.ControlerOnlineConfig_Desc, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />

            </DataGrid.Columns>
        </DataGrid>

    </Grid>
</UserControl>