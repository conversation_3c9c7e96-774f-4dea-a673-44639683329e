<mah:MetroWindow
    x:Class="ModularAT.Views.LoginWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    Title="LoginWindow"
    Width="700"
    Height="400"
    d:DataContext="{d:DesignInstance Type=viewmodels:LoginViewModel}"
    ShowMaxRestoreButton="False"
    ShowMinButton="False"
    ShowTitleBar="False"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">
    <Window.Resources>
        <Thickness x:Key="ControlMargin">0 5 0 0</Thickness>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="299*" />
            <ColumnDefinition Width="401*" />
        </Grid.ColumnDefinitions>
        <Grid Grid.ColumnSpan="2">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid x:Name="LoginGrid">
                <Grid.RowDefinitions>
                    <RowDefinition Height="70" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="30" />
                    <RowDefinition Height="30" />
                </Grid.RowDefinitions>
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <Viewbox>
                        <Image
                            Margin="10,0,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Source="/Resources/Images/ModularAT.png">
                            <Image.RenderTransform>
                                <ScaleTransform ScaleX="1" ScaleY="1" />
                            </Image.RenderTransform>
                        </Image>
                    </Viewbox>
                </Grid>
                <Grid
                    Grid.Row="1"
                    Width="250"
                    Margin="0,0,0,0"
                    KeyboardNavigation.TabNavigation="Cycle">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Label
                        Grid.Row="0"
                        VerticalAlignment="Center"
                        Content="" />
                    <mah:MetroHeader
                        Grid.Row="1"
                        Margin="{StaticResource ControlMargin}"
                        Header="{Binding LanResources.Login_User_name, Source={x:Static localization:LocalizationService.Current}}">
                        <mah:MetroHeader.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                    <iconPacks:PackIconMaterial VerticalAlignment="Center" Kind="Account" />
                                    <TextBlock
                                        Margin="2,0,0,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding}" />
                                </StackPanel>
                            </DataTemplate>
                        </mah:MetroHeader.HeaderTemplate>
                        <ComboBox
                            DisplayMemberPath="UserName"
                            IsEditable="True"
                            IsEnabled="True"
                            MaxWidth="500"
                            ItemsSource="{Binding LoginInfos}"
                            SelectedValuePath="UserName"
                            Text="{Binding UserName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnExceptions=True, ValidatesOnDataErrors=True, NotifyOnValidationError=True}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="SelectionChanged">
                                    <i:InvokeCommandAction Command="{Binding GetPasswordCommand}"
                                                           CommandParameter="{Binding SelectedValue, RelativeSource={RelativeSource AncestorType=ComboBox}}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </ComboBox>
                    </mah:MetroHeader>
                    <mah:MetroHeader
                        Grid.Row="2"
                        Margin="{StaticResource ControlMargin}"
                        Header="{Binding LanResources.Login_Passwd, Source={x:Static localization:LocalizationService.Current}}">
                        <mah:MetroHeader.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel VerticalAlignment="Center" Orientation="Horizontal">
                                    <iconPacks:PackIconMaterial VerticalAlignment="Center" Kind="Key" />
                                    <TextBlock
                                        Margin="2,0,0,0"
                                        VerticalAlignment="Center"
                                        Text="{Binding}" />
                                </StackPanel>
                            </DataTemplate>
                        </mah:MetroHeader.HeaderTemplate>
                        <PasswordBox
                            helper:PasswordBoxHelper.Password="{Binding Password, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnExceptions=True, ValidatesOnDataErrors=True, NotifyOnValidationError=True}" />
                    </mah:MetroHeader>

                    <CheckBox
                        Grid.Row="3"
                        Margin="13,0,0,0"
                        VerticalAlignment="Center"
                        Content="{Binding LanResources.Login_Rem_passwd, Source={x:Static localization:LocalizationService.Current}}"
                        IsChecked="{Binding IsRmembered}" />
                    <Button
                        Grid.Row="4"
                        VerticalAlignment="Center"
                        Command="{Binding LoginCommand}"
                        Content="{Binding LanResources.Login_Login, Source={x:Static localization:LocalizationService.Current}}"
                        Cursor="Hand"
                        IsDefault="True" />
                </Grid>
                <TextBlock
                    Grid.Row="2"
                    Foreground="Red"
                    Text="{Binding LoginError}"
                    ToolTip="{Binding LoginError}" />
            </Grid>

            <Border Grid.Column="1" Background="{DynamicResource MahApps.Brushes.Accent}">
                <Image Margin="30" Source="/Resources/Images/loginBackGround.png" />
            </Border>

            <TextBlock
                Grid.Column="1"
                Margin="10"
                HorizontalAlignment="Right"
                VerticalAlignment="Bottom"
                Foreground="{DynamicResource MahApps.Brushes.SystemControlBackgroundChromeWhite}"
                Text="{Binding Version}" />
        </Grid>
        <!--<Grid x:Name="VerifyGrid" Grid.ColumnSpan="2" />-->
    </Grid>
</mah:MetroWindow>