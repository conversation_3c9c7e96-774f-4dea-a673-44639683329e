<UserControl
    x:Class="ModularAT.Views.BaseUserView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <UserControl.Resources>
        <Thickness x:Key="ControlMargin">0 5 0 0</Thickness>
        <conv:BoolToStringConverter x:Key="IsAddConverter">
            <conv:BoolToStringConverter.TrueValue>添加</conv:BoolToStringConverter.TrueValue>
            <conv:BoolToStringConverter.FalseValue>编辑</conv:BoolToStringConverter.FalseValue>
        </conv:BoolToStringConverter>
    </UserControl.Resources>
    <!--<i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadDataCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>-->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Name="Column1" Width="*" />
            <ColumnDefinition Name="Column2" Width="3" />
            <ColumnDefinition Name="Column3" Width="Auto" />
        </Grid.ColumnDefinitions>
        <DockPanel Grid.Row="0" Grid.Column="0">
            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                <TextBox
                    Margin="4,3"
                    mah:TextBoxHelper.ButtonCommand="{Binding SearchCommand, Mode=OneWay}"
                    mah:TextBoxHelper.ButtonCommandParameter="{Binding Path=Text, RelativeSource={RelativeSource Mode=Self}}"
                    mah:TextBoxHelper.Watermark="{Binding LanResources.BaseUser_Enter_keywords, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{DynamicResource MahApps.Styles.TextBox.Search}" />
            </StackPanel>
            <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                <Button
                    Margin="3"
                    Command="{Binding AddCommand}"
                    Content="{Binding LanResources.BaseUser_New, Source={x:Static localization:LocalizationService.Current}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Click">
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column1}"
                                Value="6*" />
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column2}"
                                Value="3" />
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column3}"
                                Value="4*" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=EditPanel}"
                                Value="Visible" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=EditPanel}"
                                Value="Visible" />
                            <i:ChangePropertyAction
                                PropertyName="SelectedIndex"
                                TargetObject="{Binding ElementName=table}"
                                Value="-1" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=changePWD}"
                                Value="Collapsed" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </Button>
                <Button
                    Margin="3"
                    Command="{Binding LoadDataCommand}"
                    Content="{Binding LanResources.BaseUser_Refresh, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>
        </DockPanel>
        <DataGrid
            Name="table"
            Grid.Row="1"
            Grid.Column="0"
            Margin="3"
            AllowDrop="False"
            AutoGenerateColumns="False"
            CanUserAddRows="False"
            ItemsSource="{x:Bind ViewModel.Datas}"
            Style="{StaticResource MahApps.Styles.DataGrid.Azure}">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Binding="{Binding uLoginName}"
                    Header="{Binding LanResources.BaseUser_User_name, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding uRealName}"
                    Header="{Binding LanResources.BaseUser_Real_name, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding RoleNames[0]}"
                    Header="{Binding LanResources.BaseUser_Role, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding uStatus}"
                    Header="{Binding LanResources.BaseUser_Status, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding uRemark}"
                    Header="{Binding LanResources.BaseUser_Remark, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding uCreateTime}"
                    Header="{Binding LanResources.BaseUser_Create_time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding uUpdateTime}"
                    Header="{Binding LanResources.BaseUser_Mod_time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding uLastErrTime}"
                    Header="{Binding LanResources.BaseUser_Last_login, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTemplateColumn Header="{Binding LanResources.BaseUser_Op, Source={x:Static localization:LocalizationService.Current}}">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Hyperlink
                                        Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding}"
                                        Foreground="{DynamicResource MahApps.Brushes.Text}">
                                        <TextBlock Text="{Binding LanResources.BaseUser_Edit, Source={x:Static localization:LocalizationService.Current}}" />
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Click">
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column1}"
                                                    Value="6*" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column2}"
                                                    Value="3" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column3}"
                                                    Value="4*" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Visibility"
                                                    TargetObject="{Binding ElementName=EditPanel}"
                                                    Value="Visible" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Visibility"
                                                    TargetObject="{Binding ElementName=changePWD}"
                                                    Value="Visible" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Hyperlink>
                                </TextBlock>
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Hyperlink
                                        Command="{Binding DataContext.DeleteOneCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding uID}"
                                        Foreground="{DynamicResource MahApps.Brushes.Text}">
                                        <TextBlock Text="{Binding LanResources.BaseUser_Delete, Source={x:Static localization:LocalizationService.Current}}" />
                                    </Hyperlink>
                                </TextBlock>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="MouseDoubleClick">
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column1}"
                        Value="6*" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column2}"
                        Value="3" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column3}"
                        Value="4*" />
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=EditPanel}"
                        Value="Visible" />
                    <i:InvokeCommandAction Command="{Binding EditCommand}"
                                           CommandParameter="{Binding SelectedItem, ElementName=table}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </DataGrid>
        <GridSplitter
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="1"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="Gray"
            ResizeBehavior="PreviousAndNext"
            ResizeDirection="Columns" />
        <StackPanel
            Name="EditPanel"
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="2"
            Orientation="Vertical"
            Visibility="Collapsed">
            <GroupBox Margin="4,2" Header="{Binding IsAdd, Converter={StaticResource IsAddConverter}}">
                <AdornerDecorator>
                    <StackPanel>
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseUser_Login_name, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            Text="{Binding Current.uLoginName}" />
                        <StackPanel Orientation="Horizontal">
                            <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseUser_Passwd, Source={x:Static localization:LocalizationService.Current}}" />
                            <CheckBox
                                Name="changePWD"
                                Margin="10,0,0,0"
                                Content="{Binding LanResources.BaseUser_Change_passwd, Source={x:Static localization:LocalizationService.Current}}"
                                IsChecked="{Binding IsChangePWD}">
                                <i:Interaction.Triggers>
                                    <i:EventTrigger EventName="Checked">
                                        <i:ChangePropertyAction
                                            PropertyName="Password"
                                            TargetObject="{Binding ElementName=pwd}"
                                            Value="" />
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>
                            </CheckBox>
                        </StackPanel>
                        <PasswordBox
                            Name="pwd"
                            helper:PasswordBoxHelper.Password="{Binding PassWord, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged, ValidatesOnExceptions=True, ValidatesOnDataErrors=True, NotifyOnValidationError=True}"
                            IsEnabled="{Binding IsChangePWD}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseUser_Real_name, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            Text="{Binding Current.uRealName}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseUser_Role, Source={x:Static localization:LocalizationService.Current}}" />
                        <ComboBox
                            HorizontalContentAlignment="Stretch"
                            DisplayMemberPath="Name"
                            ItemsSource="{x:Bind ViewModel.Roles}"
                            SelectedValue="{x:Bind ViewModel.CurrentRId, Mode=TwoWay}"
                            SelectedValuePath="Id" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseUser_Remark, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            mah:TextBoxHelper.ClearTextButton="True"
                            AcceptsReturn="True"
                            Text="{Binding Current.uRemark}"
                            TextWrapping="Wrap" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BaseUser_Status, Source={x:Static localization:LocalizationService.Current}}" />
                        <mah:NumericUpDown
                            mah:TextBoxHelper.Watermark="{Binding LanResources.BaseUser_Pending_enable, Source={x:Static localization:LocalizationService.Current}}"
                            Maximum="10"
                            Minimum="0"
                            Value="{Binding Current.uStatus}" />
                        <Button
                            Width="50"
                            Command="{Binding SaveCommand}"
                            Content="{Binding LanResources.BaseUser_Save, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                        <Button
                            Width="50"
                            Command="{Binding CancelCommand}"
                            Content="{Binding LanResources.BaseUser_Cancel, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                    </StackPanel>
                </AdornerDecorator>
            </GroupBox>
        </StackPanel>
    </Grid>
</UserControl>