<UserControl
    x:Class="ModularAT.Views.BasePermissionView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <UserControl.Resources>
        <Thickness x:Key="ControlMargin">0 5 0 0</Thickness>
        <conv:BoolToStringConverter x:Key="IsAddConverter">
            <conv:BoolToStringConverter.TrueValue>添加</conv:BoolToStringConverter.TrueValue>
            <conv:BoolToStringConverter.FalseValue>编辑</conv:BoolToStringConverter.FalseValue>
        </conv:BoolToStringConverter>
    </UserControl.Resources>
    <!--<i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadDataCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>-->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Name="Column1" Width="*" />
            <ColumnDefinition Name="Column2" Width="3" />
            <ColumnDefinition Name="Column3" Width="Auto" />
        </Grid.ColumnDefinitions>
        <DockPanel Grid.Row="0" Grid.Column="0">
            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                <TextBox
                    Margin="4,3"
                    mah:TextBoxHelper.ButtonCommand="{Binding SearchCommand, Mode=OneWay}"
                    mah:TextBoxHelper.ButtonCommandParameter="{Binding Path=Text, RelativeSource={RelativeSource Mode=Self}}"
                    mah:TextBoxHelper.Watermark="{Binding LanResources.BasePermission_Enter_keywords, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{DynamicResource MahApps.Styles.TextBox.Search}" />
            </StackPanel>
            <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                <Button
                    Margin="3"
                    Command="{Binding AddCommand}"
                    Content="{Binding LanResources.BasePermission_New, Source={x:Static localization:LocalizationService.Current}}">
                    <i:Interaction.Triggers>
                        <i:EventTrigger EventName="Click">
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column1}"
                                Value="6*" />
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column2}"
                                Value="3" />
                            <i:ChangePropertyAction
                                PropertyName="Width"
                                TargetObject="{Binding ElementName=Column3}"
                                Value="4*" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=EditPanel}"
                                Value="Visible" />
                            <i:ChangePropertyAction
                                PropertyName="Visibility"
                                TargetObject="{Binding ElementName=EditPanel}"
                                Value="Visible" />
                            <i:ChangePropertyAction
                                PropertyName="SelectedIndex"
                                TargetObject="{Binding ElementName=table}"
                                Value="-1" />
                        </i:EventTrigger>
                    </i:Interaction.Triggers>
                </Button>
                <Button
                    Margin="3"
                    Command="{Binding LoadDataCommand}"
                    Content="{Binding LanResources.BasePermission_Refresh, Source={x:Static localization:LocalizationService.Current}}" />
            </StackPanel>
        </DockPanel>
        <DataGrid
            Name="table"
            Grid.Row="1"
            Grid.Column="0"
            Margin="3"
            AllowDrop="False"
            AutoGenerateColumns="False"
            CanUserAddRows="False"
            ItemsSource="{x:Bind ViewModel.Datas}"
            Style="{StaticResource MahApps.Styles.DataGrid.Azure}">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Binding="{Binding Name}"
                    Header="{Binding LanResources.BasePermission_Menu, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Code}"
                    Header="{Binding LanResources.BasePermission_Bind_code, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding IsButton}"
                    Header="{Binding LanResources.BasePermission_Is_button, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding IsHide}"
                    Header="{Binding LanResources.BasePermission_Is_hidden, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Func}"
                    Header="{Binding LanResources.BasePermission_Btn_event, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Description}"
                    Header="{Binding LanResources.BasePermission_Desc, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding OrderSort}"
                    Header="{Binding LanResources.BasePermission_Level, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Enabled}"
                    Header="{Binding LanResources.BasePermission_Enable, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding CreateBy}"
                    Header="{Binding LanResources.BasePermission_Creator, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding CreateTime}"
                    Header="{Binding LanResources.BasePermission_Create_time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding ModifyBy}"
                    Header="{Binding LanResources.BasePermission_Modifier, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding ModifyTime}"
                    Header="{Binding LanResources.BasePermission_Mod_time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTemplateColumn Header="{Binding LanResources.BasePermission_Op, Source={x:Static localization:LocalizationService.Current}}">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Hyperlink
                                        Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding}"
                                        Foreground="{DynamicResource MahApps.Brushes.Text}">
                                        <TextBlock Text="{Binding LanResources.BasePermission_Edit, Source={x:Static localization:LocalizationService.Current}}" />
                                        <i:Interaction.Triggers>
                                            <i:EventTrigger EventName="Click">
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column1}"
                                                    Value="6*" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column2}"
                                                    Value="3" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Width"
                                                    TargetObject="{Binding ElementName=Column3}"
                                                    Value="4*" />
                                                <i:ChangePropertyAction
                                                    PropertyName="Visibility"
                                                    TargetObject="{Binding ElementName=EditPanel}"
                                                    Value="Visible" />
                                            </i:EventTrigger>
                                        </i:Interaction.Triggers>
                                    </Hyperlink>
                                </TextBlock>
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Hyperlink
                                        Command="{Binding DataContext.DeleteOneCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                        CommandParameter="{Binding Id}"
                                        Foreground="{DynamicResource MahApps.Brushes.Text}">
                                        <TextBlock Text="{Binding LanResources.BasePermission_Delete, Source={x:Static localization:LocalizationService.Current}}" />
                                    </Hyperlink>
                                </TextBlock>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="MouseDoubleClick">
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column1}"
                        Value="6*" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column2}"
                        Value="3" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column3}"
                        Value="4*" />
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=EditPanel}"
                        Value="Visible" />
                    <i:InvokeCommandAction Command="{Binding EditCommand}"
                                           CommandParameter="{Binding SelectedItem, ElementName=table}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </DataGrid>
        <GridSplitter
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="1"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="Gray"
            ResizeBehavior="PreviousAndNext"
            ResizeDirection="Columns" />
        <StackPanel
            Name="EditPanel"
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="2"
            Orientation="Vertical"
            Visibility="Collapsed">
            <GroupBox Margin="4,2" Header="{Binding IsAdd, Converter={StaticResource IsAddConverter}}">
                <AdornerDecorator>
                    <StackPanel>
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Menu_name, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            Text="{Binding Current.Name}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Level, Source={x:Static localization:LocalizationService.Current}}" />
                        <mah:NumericUpDown Minimum="0" Value="{Binding Current.OrderSort}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="ValueChanged">
                                    <i:InvokeCommandAction
                                        Command="{Binding DataContext.LoadPermPairsCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </mah:NumericUpDown>
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Parent_menu, Source={x:Static localization:LocalizationService.Current}}" />
                        <ComboBox
                            ItemsSource="{Binding PermPairs}"
                            SelectedValue="{x:Bind ViewModel.Current.Pid, Mode=TwoWay}"
                            SelectedValuePath="Key">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Value}" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Bind_code, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            Text="{Binding Current.Code}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Is_button, Source={x:Static localization:LocalizationService.Current}}" />
                        <CheckBox
                            mah:CheckBoxHelper.CheckCornerRadius="2"
                            IsChecked="{Binding Current.IsButton}"
                            IsEnabled="True" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Is_hidden, Source={x:Static localization:LocalizationService.Current}}" />
                        <CheckBox
                            mah:CheckBoxHelper.CheckCornerRadius="2"
                            IsChecked="{Binding Current.IsHide}"
                            IsEnabled="True" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Btn_event, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            Text="{Binding Current.Func}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Desc, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            AcceptsReturn="True"
                            Text="{Binding Current.Description}"
                            TextWrapping="Wrap" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.BasePermission_Enable, Source={x:Static localization:LocalizationService.Current}}" />
                        <CheckBox
                            mah:CheckBoxHelper.CheckCornerRadius="2"
                            IsChecked="{Binding Current.Enabled}"
                            IsEnabled="True" />

                        <Button
                            Width="50"
                            Command="{Binding SaveCommand}"
                            Content="{Binding LanResources.BasePermission_Save, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                        <Button
                            Width="50"
                            Command="{Binding CancelCommand}"
                            Content="{Binding LanResources.BasePermission_Cancel, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                    </StackPanel>
                </AdornerDecorator>
            </GroupBox>
        </StackPanel>
    </Grid>
</UserControl>