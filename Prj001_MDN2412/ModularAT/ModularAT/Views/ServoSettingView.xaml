<UserControl
    x:Class="ModularAT.Views.ServoSettingView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vc="clr-namespace:ModularAT.ValueConverter"
    xmlns:vm="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance Type=vm:ServoSettingViewModel}"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <!--<vc:TransposeParaConverter x:Key="TransposeParaConverter" />-->
            <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}" TargetType="{x:Type TypeName=Button}">
                <Setter Property="Margin" Value="0,10,0,0" />
                <Setter Property="Width" Value="180" />
                <Setter Property="HorizontalAlignment" Value="Left" />
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding RequestAuthCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}" Text="{Binding LanResources.ServoSetting_Driver_params, Source={x:Static localization:LocalizationService.Current}}" />

        <StackPanel
            HorizontalAlignment="Left"
            Grid.Row="1"
            Grid.Column="0"
            MinWidth="180"
            Margin="5">

            <TextBlock
                Margin="2"
                Text="{Binding LanResources.ServoSetting_Sel_op, Source={x:Static localization:LocalizationService.Current}}" />
            <Button
                helper:PermissionHelper.HasPerm="/ServoSetting/SetPara"
                Command="{Binding Path=SetParaCommand}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Select}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSetting_Sel_write, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button helper:PermissionHelper.HasPerm="/ServoSetting/SetParamsAll"
                    Command="{Binding Path=SetParamsAllCommand}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=SelectAll}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSetting_Write_all, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button helper:PermissionHelper.HasPerm="/ServoSetting/ParaClear" Command="{Binding Path=ParaClearCommand}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=KeyboardReturn}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSetting_Restore_def_params, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button helper:PermissionHelper.HasPerm="/ServoSetting/ErrorReset"
                    Command="{Binding Path=ErrorResetCommand}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Refresh}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSetting_Err_reset, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <Button helper:PermissionHelper.HasPerm="/ServoSetting/ErrorRecordClear"
                    Command="{Binding Path=ErrorRecordClearCommand}">
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=NotificationClearAll}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ServoSetting_Fault_rec_clear, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>

            <!--  驱动模式设置  -->
            <TextBlock
                Margin="2,10"
                VerticalAlignment="Center"
                Text="{Binding LanResources.ServoSetting_Drive_mode_set, Source={x:Static localization:LocalizationService.Current}}" />

            <Label Content="{Binding LanResources.ServoSetting_Ctrl_right, Source={x:Static localization:LocalizationService.Current}}" />
            <ComboBox
                DisplayMemberPath="Key"
                ItemsSource="{Binding DriverControlBy}"
                SelectedValue="{Binding DriverMode.ControlBy}"
                SelectedValuePath="Value">
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="SelectionChanged">
                        <i:CallMethodAction MethodName="SetDriverControlBy" TargetObject="{Binding}" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ComboBox>

            <Label Content="{Binding LanResources.ServoSetting_Local_ctrl_mode, Source={x:Static localization:LocalizationService.Current}}">
                <Label.Style>
                    <Style TargetType="Label">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="0">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="1">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Label.Style>
            </Label>
            <ComboBox
                DisplayMemberPath="Key"
                ItemsSource="{Binding DriverLocalMode}"
                SelectedValue="{Binding DriverMode.Mode}"
                SelectedValuePath="Value">
                <ComboBox.Style>
                    <Style BasedOn="{StaticResource DefaultComboBox}" TargetType="ComboBox">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="0">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="1">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ComboBox.Style>
            </ComboBox>

            <Label Content="{Binding LanResources.ServoSetting_Sub_mode, Source={x:Static localization:LocalizationService.Current}}">
                <Label.Style>
                    <Style TargetType="Label">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="0">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="1">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Label.Style>
            </Label>
            <ComboBox
                Name="submode"
                DisplayMemberPath="Key"
                SelectedIndex="{Binding DriverMode.SubMode}"
                SelectedValuePath="Value">
                <ComboBox.Style>
                    <Style BasedOn="{StaticResource DefaultComboBox}" TargetType="ComboBox">
                        <Setter Property="ItemsSource" Value="{Binding DriverSubModeNomal}" />
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="0">
                                <Setter Property="Visibility" Value="Collapsed" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding DriverMode.ControlBy}" Value="1">
                                <Setter Property="Visibility" Value="Visible" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding DriverMode.Mode}" Value="0">
                                <Setter Property="ItemsSource" Value="{Binding DriverSubModeNomal}" />
                                <Setter Property="SelectedIndex" Value="0" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding DriverMode.Mode}" Value="1">
                                <Setter Property="ItemsSource" Value="{Binding DriverSubModeTest}" />
                                <Setter Property="SelectedIndex" Value="0" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ComboBox.Style>
                <i:Interaction.Triggers>
                    <i:EventTrigger EventName="SelectionChanged">
                        <i:InvokeCommandAction Command="{Binding SetDriverModeCommand}" />
                    </i:EventTrigger>
                </i:Interaction.Triggers>
            </ComboBox>

        </StackPanel>
        <DataGrid
            x:Name="paraDataGrid"
            Grid.Row="1"
            Grid.Column="1"
            Margin="10,0,10,0"
            CanUserAddRows="False"
            CanUserSortColumns="False"
            ItemsSource="{Binding Data.ParamItems}"
            SelectionChanged="paraDataGrid_SelectionChanged"
            SelectionMode="Extended"
            VirtualizingStackPanel.IsVirtualizing="True">
            <!--  Height="{Binding ActualHeight, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=Border}}"  -->
            <DataGrid.GroupStyle>
                <GroupStyle>
                    <GroupStyle.ContainerStyle>
                        <Style TargetType="{x:Type GroupItem}">
                            <Setter Property="Margin" Value="0,0,0,5" />
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="{x:Type GroupItem}">
                                        <Expander
                                            Background="AliceBlue"
                                            BorderBrush="AliceBlue"
                                            BorderThickness="1,1,1,5"
                                            IsExpanded="True">
                                            <Expander.Header>
                                                <DockPanel>
                                                    <TextBlock VerticalAlignment="Center">
                                                        <TextBlock.Text>
                                                            <MultiBinding StringFormat="{}{0} - {1}">
                                                                <Binding Mode="OneWay" Path="Name" />
                                                                <Binding
                                                                    Converter="{vc:ItemCountConverter}"
                                                                    Mode="OneWay"
                                                                    Path="ItemCount" />
                                                            </MultiBinding>
                                                        </TextBlock.Text>
                                                    </TextBlock>
                                                </DockPanel>
                                            </Expander.Header>
                                            <Expander.Content>
                                                <ItemsPresenter />
                                            </Expander.Content>
                                        </Expander>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </GroupStyle.ContainerStyle>
                </GroupStyle>
            </DataGrid.GroupStyle>
            <DataGrid.Columns>
                <!--<DataGridTemplateColumn Header="{Binding LanResources.ServoSetting_Select, Source={x:Static localization:LocalizationService.Current}}">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox VerticalAlignment="Center" IsChecked="{Binding IsSelected, Mode=TwoWay}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>-->
                <!--<DataGridCheckBoxColumn Binding="{Binding IsSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Header="{Binding LanResources.ServoSetting_Select, Source={x:Static localization:LocalizationService.Current}}" />-->
                <DataGridTextColumn
                    Binding="{Binding No}"
                    Header="No."
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Name}"
                    Header="{Binding LanResources.ServoSetting_Param_name, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding DataType}"
                    Header="{Binding LanResources.ServoSetting_Set_type, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding MinValue}"
                    Header="{Binding LanResources.ServoSetting_Min_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding MaxValue}"
                    Header="{Binding LanResources.ServoSetting_Max_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Path=ReadValue}"
                    Header="{Binding LanResources.ServoSetting_Read_val, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn Binding="{Binding Path=Int64Value}" Header="{Binding LanResources.ServoSetting_Set_val, Source={x:Static localization:LocalizationService.Current}}" />
                <!--<DataGridTemplateColumn Header="{Binding LanResources.ServoSetting_Set_val, Source={x:Static localization:LocalizationService.Current}}">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBox
                                        VerticalAlignment="Center"
                                        Text="{Binding Int64Value, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                        Visibility="{Binding Access, Converter={StaticResource PermissionConverter}}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>-->

                <!--<mah:DataGridNumericUpDownColumn Header="{Binding LanResources.ServoSetting_Set_val, Source={x:Static localization:LocalizationService.Current}}">
                        <mah:DataGridNumericUpDownColumn.Binding>
                            <MultiBinding Converter="{StaticResource InputConverter}">
                                <Binding Path="EditString" />
                                <Binding Path="MinValue" />
                                <Binding Path="MaxValue" />
                                <Binding Path="Coefficient" />
                            </MultiBinding>
                        </mah:DataGridNumericUpDownColumn.Binding>
                    </mah:DataGridNumericUpDownColumn>-->
                <DataGridTextColumn
                    Binding="{Binding Access}"
                    Header="{Binding LanResources.ServoSetting_Perm, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Coefficient}"
                    Header="{Binding LanResources.ServoSetting_Coeff, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <!--<DataGridCheckBoxColumn Binding="{Binding IsMonitorVar, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Header="{Binding LanResources.ServoSetting_Monitor, Source={x:Static localization:LocalizationService.Current}}" />-->
                <!--<DataGridTemplateColumn Header="{Binding LanResources.ServoSetting_Monitor, Source={x:Static localization:LocalizationService.Current}}">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <CheckBox VerticalAlignment="Center" IsChecked="{Binding IsMonitorVar, Mode=TwoWay}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>-->
                <DataGridTextColumn
                    Width="*"
                    Binding="{Binding Description}"
                    Header="{Binding LanResources.ServoSetting_Desc, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />

            </DataGrid.Columns>
            <!--<DataGrid.Style>
                    <Style BasedOn="{StaticResource DefaultDataGrid}" TargetType="{x:Type DataGrid}">
                        <Setter Property="ColumnWidth" Value="*" />
                    </Style>
                </DataGrid.Style>-->

            <!--<DataGrid.RowStyle>
                    <Style BasedOn="{StaticResource MahApps.Styles.DataGridRow}" TargetType="{x:Type DataGridRow}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Price, Mode=OneWay, Converter={StaticResource AlbumPriceIsTooMuchConverter}}" Value="True">
                                <Setter Property="Background" Value="#FF8B8B" />
                                <Setter Property="Foreground" Value="DarkRed" />
                            </DataTrigger>
                            <MultiDataTrigger>
                                <MultiDataTrigger.Conditions>
                                    <Condition Binding="{Binding Price, Mode=OneWay, Converter={StaticResource AlbumPriceIsTooMuchConverter}}" Value="True" />
                                    <Condition Binding="{Binding Path=IsMouseOver, RelativeSource={RelativeSource Self}}" Value="true" />
                                </MultiDataTrigger.Conditions>
                                <Setter Property="Background" Value="#FFBDBD" />
                            </MultiDataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>-->
            <!--<DataGrid.RowValidationRules>
                  <vc:AlbumPriceIsReallyTooMuchValidation ValidatesOnTargetUpdated="True" ValidationStep="CommittedValue" />
                  <vc:AlbumPriceIsReallyTooMuchValidation ValidatesOnTargetUpdated="True" ValidationStep="UpdatedValue" />
              </DataGrid.RowValidationRules>-->
        </DataGrid>

    </Grid>
</UserControl>