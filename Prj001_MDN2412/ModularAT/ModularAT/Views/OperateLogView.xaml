<UserControl
    x:Class="ModularAT.Views.OperateLogView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls">
    <UserControl.Resources>
        <Thickness x:Key="ControlMargin">5 5 5 0</Thickness>
    </UserControl.Resources>
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding LoadDataCommand}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Name="Column1" Width="*" />
            <ColumnDefinition Name="Column2" Width="3" />
            <ColumnDefinition Name="Column3" Width="Auto" />
        </Grid.ColumnDefinitions>
        <DockPanel Grid.Row="0" Grid.Column="0">
            <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                <TextBox
                    Margin="4,3"
                    mah:TextBoxHelper.ButtonCommand="{Binding SearchCommand, Mode=OneWay}"
                    mah:TextBoxHelper.ButtonCommandParameter="{Binding Path=Text, RelativeSource={RelativeSource Mode=Self}}"
                    mah:TextBoxHelper.Watermark="{Binding LanResources.OperateLog_Enter_keywords, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{DynamicResource MahApps.Styles.TextBox.Search}" />
            </StackPanel>
            <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                <Button
                    Margin="{StaticResource ControlMargin}"
                    Command="{Binding LoadDataCommand}"
                    Content="{Binding LanResources.OperateLog_Refresh, Source={x:Static localization:LocalizationService.Current}}" />
                <Label
                    Margin="{StaticResource ControlMargin}"
                    VerticalContentAlignment="Center"
                    Content="{Binding LanResources.OperateLog_Start_time, Source={x:Static localization:LocalizationService.Current}}" />
                <mah:DateTimePicker
                    Margin="{StaticResource ControlMargin}"
                    mah:TextBoxHelper.ClearTextButton="True"
                    DisplayDateEnd="{x:Bind ViewModel.GetLimit(false), Mode=OneWay}"
                    DisplayDateStart="{x:Bind ViewModel.GetLimit(true), Mode=OneWay}"
                    SelectedDateTime="{Binding StartTime}" />
                <mah:DateTimePicker
                    Margin="{StaticResource ControlMargin}"
                    mah:TextBoxHelper.ClearTextButton="True"
                    DisplayDateEnd="{x:Bind ViewModel.GetLimit(false), Mode=OneWay}"
                    DisplayDateStart="{x:Bind ViewModel.GetLimit(true), Mode=OneWay}"
                    SelectedDateTime="{Binding EndTime}" />
            </StackPanel>
        </DockPanel>
        <DataGrid
            Name="table"
            Grid.Row="1"
            Grid.Column="0"
            Margin="3"
            AutoGenerateColumns="False"
            CanUserAddRows="False"
            HorizontalScrollBarVisibility="Visible"
            ItemsSource="{Binding Data}"
            ScrollViewer.CanContentScroll="True"
            Style="{StaticResource MahApps.Styles.DataGrid.Azure}">
            <DataGrid.Columns>
                <DataGridTextColumn
                    Binding="{Binding LogTime}"
                    Header="{Binding LanResources.OperateLog_Time, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Area}"
                    Header="{Binding LanResources.OperateLog_Module, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Controller}"
                    Header="{Binding LanResources.OperateLog_Op, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding Action}"
                    Header="{Binding LanResources.OperateLog_Behav, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    MaxWidth="200"
                    Binding="{Binding Description}"
                    Header="{Binding LanResources.OperateLog_Desc, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTextColumn
                    Binding="{Binding LoginName}"
                    Header="{Binding LanResources.OperateLog_Operator, Source={x:Static localization:LocalizationService.Current}}"
                    IsReadOnly="True" />
                <DataGridTemplateColumn Header="{Binding LanResources.OperateLog_Op, Source={x:Static localization:LocalizationService.Current}}">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                <Hyperlink
                                    Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                    CommandParameter="{Binding}"
                                    Foreground="{DynamicResource MahApps.Brushes.Text}">
                                    <TextBlock Text="{Binding LanResources.OperateLog_View, Source={x:Static localization:LocalizationService.Current}}" />
                                    <i:Interaction.Triggers>
                                        <i:EventTrigger EventName="Click">
                                            <i:ChangePropertyAction
                                                PropertyName="Width"
                                                TargetObject="{Binding ElementName=Column1}"
                                                Value="6*" />
                                            <i:ChangePropertyAction
                                                PropertyName="Width"
                                                TargetObject="{Binding ElementName=Column2}"
                                                Value="3" />
                                            <i:ChangePropertyAction
                                                PropertyName="Width"
                                                TargetObject="{Binding ElementName=Column3}"
                                                Value="4*" />
                                            <i:ChangePropertyAction
                                                PropertyName="Visibility"
                                                TargetObject="{Binding ElementName=EditPanel}"
                                                Value="Visible" />
                                        </i:EventTrigger>
                                    </i:Interaction.Triggers>
                                </Hyperlink>
                            </TextBlock>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            <i:Interaction.Triggers>
                <i:EventTrigger EventName="MouseDoubleClick">
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column1}"
                        Value="6*" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column2}"
                        Value="3" />
                    <i:ChangePropertyAction
                        PropertyName="Width"
                        TargetObject="{Binding ElementName=Column3}"
                        Value="4*" />
                    <i:ChangePropertyAction
                        PropertyName="Visibility"
                        TargetObject="{Binding ElementName=EditPanel}"
                        Value="Visible" />
                    <i:InvokeCommandAction Command="{Binding EditCommand}"
                                           CommandParameter="{Binding SelectedItem, ElementName=table}" />
                </i:EventTrigger>
            </i:Interaction.Triggers>
        </DataGrid>
        <GridSplitter
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="1"
            Background="Gray" />
        <StackPanel
            Name="EditPanel"
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="2"
            Orientation="Vertical"
            Visibility="Collapsed">
            <GroupBox Margin="4,2" Header="{Binding LanResources.OperateLog_Details, Source={x:Static localization:LocalizationService.Current}}">
                <AdornerDecorator>
                    <StackPanel>
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.OperateLog_Module, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            IsReadOnly="True"
                            Text="{Binding Current.Area}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.OperateLog_Op, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            IsReadOnly="True"
                            Text="{Binding Current.Controller}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.OperateLog_Behav, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            IsReadOnly="True"
                            Text="{Binding Current.Action}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.OperateLog_Detailed_desc, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            AcceptsReturn="True"
                            IsReadOnly="True"
                            Text="{Binding Current.Description}"
                            TextWrapping="Wrap" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.OperateLog_Time, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            IsReadOnly="True"
                            Text="{Binding Current.LogTime}" />
                        <mah:MetroHeader Margin="{StaticResource ControlMargin}" Header="{Binding LanResources.OperateLog_Operator, Source={x:Static localization:LocalizationService.Current}}" />
                        <TextBox
                            Margin="{StaticResource ControlMargin}"
                            HorizontalContentAlignment="Stretch"
                            IsReadOnly="True"
                            Text="{Binding Current.LoginName}" />
                        <Button
                            Width="50"
                            Command="{Binding CancelCommand}"
                            Content="{Binding LanResources.OperateLog_Cancel, Source={x:Static localization:LocalizationService.Current}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="Click">
                                    <i:ChangePropertyAction
                                        PropertyName="Width"
                                        TargetObject="{Binding ElementName=Column3}"
                                        Value="Auto" />
                                    <i:ChangePropertyAction
                                        PropertyName="Visibility"
                                        TargetObject="{Binding ElementName=EditPanel}"
                                        Value="Collapsed" />
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                        </Button>
                    </StackPanel>
                </AdornerDecorator>
            </GroupBox>
        </StackPanel>
    </Grid>
</UserControl>