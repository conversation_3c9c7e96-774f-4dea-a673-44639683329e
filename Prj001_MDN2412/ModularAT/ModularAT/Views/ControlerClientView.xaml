<UserControl
    x:Class="ModularAT.Views.ControlerClientView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance viewModels:ControlerClientViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <StackPanel HorizontalAlignment="Left" MinWidth="180">
        <TextBlock Style="{StaticResource ResourceKey=HeaderTextBlock}"
                   Text="{Binding LanResources.ControlerClient_Ctrl_conn, Source={x:Static localization:LocalizationService.Current}}" />
        <StackPanel MinWidth="180" Margin="10">
            <TextBlock Text="IP" />
            <TextBox Text="{Binding Config.Ip}" />
            <TextBlock
                Text="{Binding LanResources.ControlerClient_Port, Source={x:Static localization:LocalizationService.Current}}" />
            <TextBox Text="{Binding Config.Port}" />
            <Button Command="{Binding Path=ConnectPortCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=Data.IsConnectedControler}" Value="True">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=Connection}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerClient_Connect, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
            <Button Command="{Binding Path=ClosePortCommand}">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Path=Data.IsConnectedControler}" Value="False">
                                <Setter Property="IsEnabled" Value="False" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=LanDisconnect}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerClient_Disconnect, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
            <Button

                HorizontalAlignment="Left"
                Command="{Binding Path=SaveCommand}"
                Visibility="Hidden">
                <Button.Style>
                    <Style BasedOn="{StaticResource ResourceKey=ImageWithTextButton}"
                           TargetType="{x:Type TypeName=Button}" />
                </Button.Style>
                <Button.Content>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Rectangle
                            Grid.Row="0"
                            Grid.Column="0"
                            Style="{StaticResource ResourceKey=ButtonWithImageRectangle}">
                            <Rectangle.OpacityMask>
                                <VisualBrush Stretch="Uniform" Visual="{iconPacks:Material Kind=ContentSave}" />
                            </Rectangle.OpacityMask>
                        </Rectangle>
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Style="{StaticResource ResourceKey=ButtonWithImageTextBlock}"
                            Text="{Binding LanResources.ControlerClient_Save, Source={x:Static localization:LocalizationService.Current}}" />
                    </Grid>
                </Button.Content>
            </Button>
        </StackPanel>
    </StackPanel>
</UserControl>