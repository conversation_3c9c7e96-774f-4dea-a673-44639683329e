<UserControl
    x:Class="ModularAT.Views.ControlerDebug"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    d:DataContext="{d:DesignInstance Type=viewmodels:ControlerDebugViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <Thickness x:Key="ControlMargin">5 5 5 5</Thickness>
        <Style TargetType="{x:Type TextBox}">
            <Setter Property="ContextMenu" Value="{StaticResource MahApps.TextBox.ContextMenu}" />
        </Style>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" MinWidth="100" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Grid.Column="0">
            <WrapPanel>
                <Label Margin="{StaticResource ControlMargin}" Content="{Binding LanResources.ControlerDebug_Send, Source={x:Static localization:LocalizationService.Current}}" />
                <Button
                    Width="50"
                    Height="30"
                    Command="{Binding SendMsgCommand}"
                    Content="{Binding LanResources.ControlerDebug_Send, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{StaticResource ResourceKey=DefaultButton}" />
            </WrapPanel>

            <TextBox
                Height="120"
                Margin="{StaticResource ControlMargin}"
                Padding="5"
                mah:TextBoxHelper.SelectAllOnFocus="True"
                Text="{Binding SendMsgText}"
                TextWrapping="Wrap"
                VerticalScrollBarVisibility="Auto" />

        </StackPanel>
        <StackPanel Grid.Row="0" Grid.Column="1">
            <WrapPanel>
                <Label Margin="{StaticResource ControlMargin}" Content="{Binding LanResources.ControlerDebug_Log, Source={x:Static localization:LocalizationService.Current}}" />
                <Button
                    Width="50"
                    Height="30"
                    Command="{Binding ClearReceivedMsgCommand}"
                    Content="{Binding LanResources.ControlerDebug_Clear, Source={x:Static localization:LocalizationService.Current}}"
                    Style="{StaticResource ResourceKey=DefaultButton}" />
            </WrapPanel>
            <TextBox
                Height="120"
                Margin="{StaticResource ControlMargin}"
                Padding="5"
                IsReadOnly="True"
                PreviewMouseWheel="TextBox_PreviewMouseWheelHande"
                Text="{x:Bind ViewModel.ReceivedMsgText}"
                TextChanged="TextBox_TextChanged"
                TextWrapping="Wrap"
                VerticalScrollBarVisibility="Auto" />
        </StackPanel>

    </Grid>
</UserControl>