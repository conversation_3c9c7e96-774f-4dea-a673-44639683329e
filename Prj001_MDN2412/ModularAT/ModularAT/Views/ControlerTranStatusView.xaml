<UserControl
    x:Class="ModularAT.Views.ControlerTranStatusView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:localization="clr-namespace:ModularAT.Localization;assembly=ModularAT.Localization"
    xmlns:controller="clr-namespace:ModularAT.Driver.Controller;assembly=ModularAT.Driver.Controller"
    xmlns:controllermodel="clr-namespace:ModularAT.Entity.Controller;assembly=ModularAT.Entity"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:ModularAT.ControlHelpers"
    xmlns:local="clr-namespace:ModularAT.Views"
    xmlns:common="clr-namespace:ModularAT.UiCommon"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodels="clr-namespace:ModularAT.ViewModels"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    d:DataContext="{d:DesignInstance Type=viewmodels:ControlerTranStatusViewModel}"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/Resources/Styles/CommonViewStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <CollectionViewSource x:Key="TranCtlTypesViewSource" Source="{x:Static controller:ControllerConst.TranCtlTypes}" />
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Style="{StaticResource ResourceKey=HeaderTextBlock}"
                   Text="{Binding LanResources.ControlerTranStatus_Conn_ctrl, Source={x:Static localization:LocalizationService.Current}}" />
        <Grid Grid.Row="1" Grid.Column="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <ScrollViewer Grid.Column="0">
                <StackPanel MinWidth="180" Grid.Column="0" Margin="10">
                    <TextBlock
                        Margin="0,0,5,5"
                        Style="{StaticResource ResourceKey=ProfileGroupTextBlock}"
                        Text="{Binding LanResources.ControlerTranStatus_Conn_conf, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Curr_obj_id, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox
                        IsReadOnly="True"
                        SelectedIndex="{Binding TransState.ICurObjectID}"
                        ItemsSource="{x:Bind common:GlobalParamsHelper.GetLines()}">
                    </ComboBox>


                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Left_obj_id, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox ItemsSource="{x:Bind common:GlobalParamsHelper.GetLines(true)}"
                              SelectedIndex="{Binding TransState.ILeftConnectedObjectID}">
                    </ComboBox>


                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Conn_stat, Source={x:Static localization:LocalizationService.Current}}" />
                    <!--<ComboBox
                    Grid.Row="2"
                    Grid.Column="1"

                    SelectedIndex="{Binding TransState.ILeftConnectState}">
                    <ComboBoxItem Content="{Binding LanResources.ControlerTranStatus_Disconnect, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem Content="{Binding LanResources.ControlerTranStatus_Est_conn, Source={x:Static localization:LocalizationService.Current}}" />
                </ComboBox>-->
                    <mah:ToggleSwitch
                        IsOn="{Binding TransState.ILeftConnectState, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        OffContent="{Binding LanResources.ControlerTranStatus_Disconnect, Source={x:Static localization:LocalizationService.Current}}"
                        OnContent="{Binding LanResources.ControlerTranStatus_Est_conn, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Right_obj_id, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox
                        SelectedIndex="{Binding TransState.IRightConnectedObjectID}"
                        ItemsSource="{x:Bind common:GlobalParamsHelper.GetLines(true)}">
                    </ComboBox>

                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Conn_stat, Source={x:Static localization:LocalizationService.Current}}" />
                    <!--<ComboBox
                    Grid.Row="5"
                    Grid.Column="1"

                    SelectedIndex="{Binding TransState.IRightConnectState}">
                    <ComboBoxItem Content="{Binding LanResources.ControlerTranStatus_Disconnect, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBoxItem Content="{Binding LanResources.ControlerTranStatus_Est_conn, Source={x:Static localization:LocalizationService.Current}}" />
                </ComboBox>-->
                    <mah:ToggleSwitch
                        IsOn="{Binding TransState.IRightConnectState, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        OffContent="{Binding LanResources.ControlerTranStatus_Disconnect, Source={x:Static localization:LocalizationService.Current}}"
                        OnContent="{Binding LanResources.ControlerTranStatus_Est_conn, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Sel_op, Source={x:Static localization:LocalizationService.Current}}" />
                    <StackPanel
                        Orientation="Horizontal">
                        <Button
                            helper:PermissionHelper.HasPerm="/ControlerTranStatus/Execute"
                            Command="{Binding ExecuteCommand}"
                            Content="{Binding LanResources.ControlerTranStatus_Exec, Source={x:Static localization:LocalizationService.Current}}" />
                        <Button
                            Command="{Binding ReadCommand}"
                            Content="{Binding LanResources.ControlerTranStatus_Read, Source={x:Static localization:LocalizationService.Current}}"
                            Visibility="Hidden" />
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
            <Rectangle Grid.Column="1" VerticalAlignment="Stretch" Fill="Gray" Width="1" />
            <ScrollViewer Grid.Column="2">
                <StackPanel Width="200"
                            Margin="10">
                    <TextBlock
                        Style="{StaticResource ResourceKey=ProfileGroupTextBlock}"
                        Text="{Binding LanResources.ControlerTranStatus_Conn_ctrl, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Conn_id, Source={x:Static localization:LocalizationService.Current}}" />
                    <mah:NumericUpDown
                        Maximum="1"
                        Minimum="0"
                        Value="{Binding RoAxisCtrl.ITranID}" />

                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Target_station_id, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox
                        ItemsSource="{x:Bind common:GlobalParamsHelper.GetStations()}"
                        SelectedValuePath="Index"
                        SelectedIndex="{Binding RoAxisCtrl.UiStationID}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Speed, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding RoAxisCtrl.LrSetVel}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Accel, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding RoAxisCtrl.LrSetAcc}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Decel, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding RoAxisCtrl.LrSetDec}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Target_pos, Source={x:Static localization:LocalizationService.Current}}" />
                    <TextBox Text="{Binding RoAxisCtrl.LrTarPos}" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Ctrl_cmd, Source={x:Static localization:LocalizationService.Current}}" />
                    <ComboBox
                        DisplayMemberPath="Item1"
                        ItemsSource="{x:Bind controller:ControllerConst.TranCtlTypes}"
                        SelectedValue="{Binding RoAxisCtrl.UServoCtrlCmd, UpdateSourceTrigger=LostFocus}"
                        SelectedIndex="0"
                        SelectedValuePath="Item2" />
                    <TextBlock
                        Text="{Binding LanResources.ControlerTranStatus_Sel_op, Source={x:Static localization:LocalizationService.Current}}" />
                    <StackPanel Orientation="Horizontal">
                        <Button
                            helper:PermissionHelper.HasPerm="/ControlerAxis/RoAxisExecute"
                            Command="{Binding RoAxisExecuteCommand}"
                            Content="{Binding LanResources.ControlerTranStatus_Exec, Source={x:Static localization:LocalizationService.Current}}" />
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Grid>
        <UniformGrid Grid.Row="1" Grid.Column="1" Rows="2" HorizontalAlignment="Right">
            <!-- 接驳配置状态 -->
            <ListView
                Margin="2"
                Background="Transparent"
                ItemsSource="{x:Bind ViewModel.FeedBacks, Mode=OneWay}"
                SelectedIndex="0"
                Style="{StaticResource MahApps.Styles.ListView.Virtualized}">
                <ListView.ItemContainerStyle>
                    <Style BasedOn="{StaticResource MahApps.Styles.ListViewItem}" TargetType="{x:Type ListViewItem}">
                        <Setter Property="mah:ItemHelper.HoverBackgroundBrush" Value="Transparent" />
                        <Setter Property="mah:ItemHelper.HoverSelectedBackgroundBrush" Value="Transparent" />
                        <Setter Property="mah:ItemHelper.SelectedBackgroundBrush" Value="Transparent" />
                        <Setter Property="mah:ItemHelper.ActiveSelectionBackgroundBrush" Value="Transparent" />
                        <Setter Property="Background" Value="Transparent" />
                    </Style>
                </ListView.ItemContainerStyle>
                <ListView.View>
                    <GridView>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Line_id, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:TransStateFeedBackModel">
                                    <TextBlock Text="{x:Bind ILineBodySegID, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Line_left_conn_obj_id, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:TransStateFeedBackModel">
                                    <TextBlock Text="{x:Bind ILeftConnectedObjectID, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Line_right_conn_obj_id, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:TransStateFeedBackModel">
                                    <TextBlock Text="{x:Bind IRightConnectedObjectID, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>
            <!-- 接驳运行状态 -->
            <ListView
                Margin="2"
                Background="Transparent"
                ItemsSource="{x:Bind ViewModel.RoAxisFeedBacks, Mode=OneWay}"
                SelectedIndex="0"
                Style="{StaticResource MahApps.Styles.ListView.Virtualized}">
                <ListView.ItemContainerStyle>
                    <Style BasedOn="{StaticResource MahApps.Styles.ListViewItem}" TargetType="{x:Type ListViewItem}">
                        <Setter Property="mah:ItemHelper.HoverBackgroundBrush" Value="Transparent" />
                        <Setter Property="mah:ItemHelper.HoverSelectedBackgroundBrush" Value="Transparent" />
                        <Setter Property="mah:ItemHelper.SelectedBackgroundBrush" Value="Transparent" />
                        <Setter Property="mah:ItemHelper.ActiveSelectionBackgroundBrush" Value="Transparent" />
                        <Setter Property="Background" Value="Transparent" />
                    </Style>
                </ListView.ItemContainerStyle>
                <ListView.View>
                    <GridView>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Conn_id, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">
                                    <TextBlock Text="{x:Bind Id, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Enable_stat, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">
                                    <TextBlock Text="{x:Bind BPoweron, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Run_stat, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">
                                    <TextBlock Text="{x:Bind BRunning, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Homing_done, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">
                                    <TextBlock Text="{x:Bind BHomeDone, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Err_code, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">
                                    <TextBlock Text="{x:Bind DwAxisErrorID, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Act_speed, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">
                                    <TextBlock
                                        Text="{x:Bind LrActVelocity, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn
                            Header="{Binding LanResources.ControlerTranStatus_Act_pos, Source={x:Static localization:LocalizationService.Current}}">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate DataType="controllermodel:RoAxisFeedBackModel">

                                    <TextBlock Text="{x:Bind LrActPosition, Mode=OneWay}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>
        </UniformGrid>
        <local:ControlerDebug
            Grid.Row="2"
            Grid.ColumnSpan="2"
            DataContext="{Binding DebugViewModel}" />
    </Grid>
</UserControl>