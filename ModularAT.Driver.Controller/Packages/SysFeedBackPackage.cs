using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class SysFeedBackPackage : ControllerPackageBase
{
    private readonly int _allCount;

    public SysFeedBackPackage(int allCount)
    {
        _allCount = allCount;
    }

    public List<SysFeedBackItem> SysFeedBackItems { get; set; } = new();


    public override uint GetCmdType()
    {
        return (ushort)CmdTypeFeedBackEnum.CMD_FEEDBACK_SYSTEM_STATE;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var dataSize = Marshal.SizeOf<SysFeedBackItem>();
        var count = _allCount / dataSize;
        byteBlock.SeekToStart();
        for (var i = 0; i < count; i++)
        {
            var item = new SysFeedBackItem();
            item.ISysErrorAxisID = byteBlock.ReadInt16();
            item.ISysErrorDrvID = byteBlock.ReadInt16();
            item.UdiSysDrvErrorCode = byteBlock.ReadUInt32();
            item.SSysDrvErrName = Encoding.UTF8.GetString(byteBlock.ReadToArray(24));
            item.UdiSysErrorCode = byteBlock.ReadUInt32();
            item.UdiSysState = byteBlock.ReadUInt32();
            //var item = byteBlock.ReadPackage<SysFeedBackItem>();
            SysFeedBackItems.Add(item);
        }

        #region 备用

        //ISysErrorAxisID = byteBlock.ReadInt16();
        //ISysErrorDrvID = byteBlock.ReadInt16();
        //UdiSysDrvErrorCode = byteBlock.ReadUInt32();
        //UdiSysErrorCode = byteBlock.ReadUInt32();
        //UdiSysState = byteBlock.ReadUInt32();

        #endregion
    }

    #region 备用

    ///// <summary>
    ///// 系统错误轴ID
    ///// </summary>
    //public short ISysErrorAxisID { get; set; }

    ///// <summary>
    ///// 系统错误驱动器ID
    ///// </summary>
    //public short ISysErrorDrvID { get; set; }

    ///// <summary>
    ///// 系统驱动器错误代码
    ///// </summary>
    //public uint UdiSysDrvErrorCode { get; set; }

    ///// <summary>
    ///// 系统错误代码
    ///// </summary>
    //public uint UdiSysErrorCode { get; set; }

    ///// <summary>
    ///// 系统状态
    ///// bit0:    系统已准备好
    ///// bit1:    系统使能状态
    ///// bit2:    系统错误状态
    ///// bit3:    系统运行状态
    ///// bit4:    系统总线状态
    ///// bit5:    系统平台校验状态
    ///// bit6:    轴配置完成，可进行轴序列初始化
    ///// bit7:    运动参数配置完成，可进行系统旧状态恢复
    ///// bit8:    系统恢复旧状态完成
    ///// bit8-31: 预留
    ///// </summary>
    //public uint UdiSysState { get; set; }

    #endregion
}