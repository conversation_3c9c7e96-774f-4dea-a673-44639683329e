using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using ModularAT.Common.Helper;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class StationsCfgInfoFeedbackPackage : ControllerPackageBase
{
    private readonly int _allCount;

    public StationsCfgInfoFeedbackPackage(int allCount)
    {
        _allCount = allCount;
    }

    public IEnumerable<OnlineStationCfgPara> Datas { get; } = [];

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeFeedBackEnum.CMD_FEEDBACK_SYSCFG_INFO;
    }

    public override int GetDataSize()
    {
        return Marshal.SizeOf(typeof(OnlineStationCfgPara));
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var dataSize = Marshal.SizeOf<OnlineStationCfgPara>();
        var count = _allCount / dataSize;
        byteBlock.Seek(0);
        for (var i = 0; i < count; i++)
        {
            var item = ConvertHelper.BytesToStruct<OnlineStationCfgPara>(byteBlock.Buffer, 0);
            Datas.Append(item);
        }
    }
}