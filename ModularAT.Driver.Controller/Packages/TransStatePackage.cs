using System;
using ModularAT.Entity.Controller;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class TransStatePackage : ControllerPackageBase, ITransState
{
    public short ICurObjectID { get; set; }
    public short ILeftConnectedObjectID { get; set; }
    public short ILeftConnectState { get; set; }
    public short IRightConnectedObjectID { get; set; }
    public short IRightConnectState { get; set; }

    public override ByteBlock GetByteBlock()
    {
        ByteBlock ??= new ByteBlock(1024);
        if (ByteBlock.Length == 0)
        {
            ByteBlock.Write(ICurObjectID);
            ByteBlock.Write(ILeftConnectedObjectID);
            ByteBlock.Write(ILeftConnectState);
            ByteBlock.Write(IRightConnectedObjectID);
            ByteBlock.Write(IRightConnectState);
        }

        return ByteBlock;
    }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeEnum.CMD_REQUEST_SET_CONNECT_STATE; //8197
    }

    public override int GetDataSize()
    {
        GetByteBlock();
        DataSize = (int)ByteBlock.Length;
        return DataSize;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        GetByteBlock();
        byteBlock.Write(ByteBlock);
        ByteBlock.Dispose();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }
}