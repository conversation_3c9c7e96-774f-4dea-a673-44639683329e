using System;
using System.Runtime.InteropServices;
using ModularAT.Common.Helper;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class SysCfgInfoFeedbackPackage : ControllerPackageBase
{
    // public byte iCtrlMethod { get; set; } // 系统控制方法->0：上位机控制 1：IO控制
    // public byte iRunMode { get; set; } // 系统运行模式->0：轴控制模式  1：工位控制模式  2：自动运行模式
    // public byte iAutoRunMode { get; set; } // 自动运行模式->0：同步模式 1 异步模式
    // public byte iRunDir { get; set; } // 系统运行方向->1：逆时针（正向） -1：顺时针（反向）
    // public byte iLineBodyType { get; set; } // 系统线体类型->0：接驳线  1： 回流线  2：环形线  3：混合线  4：物流线
    // public byte iLineBodySegNums { get; set; } // 系统线体组成段数
    // public byte iAxisNums { get; set; } // 系统轴（动子）数量
    // public byte iStationNums { get; set; } // 系统工位数量
    // public byte iSlaveNodeNums { get; set; } // 系统从站节点数量
    // public byte iDrvMotorNums { get; set; } // 系统驱动电机数量
    // public byte iAxisArrayCfgMode { get; set; } // 系统轴序列配置模式
    // public byte iResumeMoveCfgMode { get; set; } // 系统恢复运动配置模式


    public OnlineSysCfgPara Data { get; set; }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeFeedBackEnum.CMD_FEEDBACK_SYSCFG_INFO;
    }

    public override int GetDataSize()
    {
        return Marshal.SizeOf(typeof(OnlineSysCfgPara));
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        Data = ConvertHelper.BytesToStruct<OnlineSysCfgPara>(byteBlock.Buffer, 0);
    }
}