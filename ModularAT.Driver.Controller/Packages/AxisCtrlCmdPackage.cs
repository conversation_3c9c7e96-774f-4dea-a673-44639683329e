using System;
using ModularAT.Entity.Controller;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class AxisCtrlCmdPackage : ControllerPackageBase, IAxisCtrlCmd
{
    public short AxisID { get; set; }

    [Obsolete("控制对象已经从协议中移除，请勿使用此属性")] public short AxisType { get; set; }

    public short AxisTargetObjectID { get; set; }

    public short AxisTargetStationID { get; set; }

    public short AxisRunMode { get; set; }

    public short VelMode { get; set; }

    public ushort AxisCtrl { get; set; }

    public double AxisSetVel { get; set; }

    public double AxisSetAcc { get; set; }

    public double AxisSetDec { get; set; }

    public double AxisSetJerk { get; set; }

    public double AxisTarPos { get; set; }
    
    public double ILrSetLocationPrecision { get; set; }
    
    public double ILrSetAntiCollPrecision { get; set; }

    public override ByteBlock GetByteBlock()
    {
        ByteBlock ??= new ByteBlock(1024);
        if (ByteBlock.Length == 0)
        {
            ByteBlock.Write(AxisID);
            //_byteBlock.Write(AxisType);
            ByteBlock.Write(AxisTargetObjectID);
            ByteBlock.Write(AxisTargetStationID);
            ByteBlock.Write(AxisRunMode);
            ByteBlock.Write(VelMode);
            ByteBlock.Write(AxisCtrl);
            ByteBlock.Write(AxisSetVel);
            ByteBlock.Write(AxisSetAcc);
            ByteBlock.Write(AxisSetDec);
            ByteBlock.Write(AxisSetJerk);
            ByteBlock.Write(AxisTarPos);
            ByteBlock.Write(ILrSetLocationPrecision);
            ByteBlock.Write(ILrSetAntiCollPrecision);
        }

        return ByteBlock;
    }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeEnum.CMD_REQUEST_MOVERCTRL;
    }

    public override int GetDataSize()
    {
        GetByteBlock();
        DataSize = (int)ByteBlock.Length;
        return DataSize;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        GetByteBlock();
        byteBlock.Write(ByteBlock.ToArray());
        ByteBlock.Dispose();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }
}