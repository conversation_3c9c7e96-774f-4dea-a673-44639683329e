using System;
using System.Collections.Generic;
using ModularAT.Common.Helper;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 在线配置命令包类，用于处理特定类型的数据并生成字节块。
/// </summary>
/// <typeparam name="T">数据类型，必须是结构体。</typeparam>
public class OnlineConfigCmdPackage<T> : ControllerPackageBase where T : struct
{
    /// <summary>
    /// 获取或设置数据列表。
    /// </summary>
    public List<T> Datas { get; set; }

    /// <summary>
    /// 获取字节块。
    /// </summary>
    /// <returns>包含数据的字节块。</returns>
    public override ByteBlock GetByteBlock()
    {
        // 如果_byteBlock为空，则创建一个新的ByteBlock实例
        ByteBlock ??= new ByteBlock(1024);
        // 如果_byteBlock的长度为0，则将Datas中的数据转换为字节并写入_byteBlock
        if (ByteBlock.Length == 0)
            for (var i = 0; i < Datas.Count; i++)
                ByteBlock.Write(ConvertHelper.StructToByte(Datas[i]));

        return ByteBlock;
    }

    /// <summary>
    /// 获取命令类型。
    /// </summary>
    /// <returns>命令类型的枚举值。</returns>
    public override uint GetCmdType()
    {
        // 根据泛型类型返回对应的命令类型枚举值
        switch (Type.GetTypeCode(typeof(T)))
        {
            case TypeCode.Object when typeof(T) == typeof(OnlineStationCfgPara):
                return (ushort)CmdTypeEnum.CMD_REQUEST_CFG_STATIONINFO;
            case TypeCode.Object when typeof(T) == typeof(OnlineSysCfgPara):
                return (uint)CmdTypeEnum.CMD_REQUEST_CFG_SYSINFO;
            default:
                return 0;
        }
    }

    /// <summary>
    /// 获取数据大小。
    /// </summary>
    /// <returns>数据的大小。</returns>
    public override int GetDataSize()
    {
        // 调用GetByteBlock方法，确保_byteBlock已初始化并包含数据
        GetByteBlock();
        // 将_byteBlock的长度转换为int类型并赋值给_dataSize
        DataSize = (int)ByteBlock.Length;
        return DataSize;
    }

    /// <summary>
    /// 将数据打包到字节块中。
    /// </summary>
    /// <param name="byteBlock">目标字节块。</param>
    public override void Package(in ByteBlock byteBlock)
    {
        // 调用GetByteBlock方法，确保_byteBlock已初始化并包含数据
        GetByteBlock();
        // 将_byteBlock的内容写入到传入的byteBlock中
        byteBlock.Write(ByteBlock.ToArray());
        // 释放_byteBlock占用的资源
        ByteBlock.Dispose();
    }

    /// <summary>
    /// 从字节块中解包数据。
    /// </summary>
    /// <param name="byteBlock">包含数据的字节块。</param>
    public override void Unpackage(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }
}
