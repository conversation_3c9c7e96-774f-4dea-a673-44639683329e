using System;
using System.Runtime.InteropServices;
using ModularAT.Common.Helper;
using ModularAT.Entity.Controller;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class RoAxisCtrlCmdPackage : ControllerPackageBase
{
    public RoAxisCtrlCmd Data { get; set; }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeEnum.CMD_REQUEST_TRANCTRL;
    }

    public override int GetDataSize()
    {
        // GetByteBlock();
        // DataSize = (int)ByteBlock.Length;
        // return DataSize;
        return Marshal.SizeOf(typeof(RoAxisCtrlCmd));
    }

    public override ByteBlock GetByteBlock()
    {
        ByteBlock ??= new ByteBlock(1024);
        // if (ByteBlock.Length == 0)
        // {
        //     ByteBlock.Write(Data.uServoCtrlCmd);
        //     ByteBlock.Write(Data.uiStationID);
        //     ByteBlock.Write(Data.iTranID);
        //     ByteBlock.Write(Data.lrSetVel);
        //     ByteBlock.Write(Data.lrSetAcc);
        //     ByteBlock.Write(Data.lrSetDec);
        //     ByteBlock.Write(Data.lrTarPos);
        // }
        var bytes = ConvertHelper.StructToByte(Data);
        if (ByteBlock.Length == 0)
        {
            ByteBlock.Write(bytes);
        }

        return ByteBlock;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        GetByteBlock();
        byteBlock.Write(ByteBlock);
        ByteBlock.Dispose();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }
}