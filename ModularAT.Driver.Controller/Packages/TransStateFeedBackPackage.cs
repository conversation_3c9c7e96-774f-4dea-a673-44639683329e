using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller.Packages;

public class TransStateFeedBackPackage : ControllerPackageBase
{
    private readonly int _allCount;

    public TransStateFeedBackPackage(int allCount)
    {
        _allCount = allCount;
    }

    public List<TransStateFeedBackItem> TransStateFeedBackItems { get; set; } = new();

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeFeedBackEnum.CMD_FEEDBACK_LINEBODY_STATE;
    }


    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var dataSize = Marshal.SizeOf<TransStateFeedBackItem>();
        var count = _allCount / dataSize;
        byteBlock.Seek(0);
        for (var i = 0; i < count; i++)
        {
            var item = new TransStateFeedBackItem();
            item.ILineBodySegID = byteBlock.ReadInt16();
            item.ILeftConnectedObjectID = byteBlock.ReadInt16();
            item.IRightConnectedObjectID = byteBlock.ReadInt16();
            //TransStateFeedBackItem item = byteBlock.ReadObject<TransStateFeedBackItem>();
            TransStateFeedBackItems.Add(item);
        }

        #region 备用

        //ILineBodySegID = byteBlock.ReadShort();
        //ILeftConnectedObjectID = byteBlock.ReadShort();
        //IRightConnectedObjectID = byteBlock.ReadShort();

        #endregion
    }

    #region 备用

    //public short ILineBodySegID { get; set; }
    //public short ILeftConnectedObjectID { get; set; }
    //public short IRightConnectedObjectID { get; set; }

    #endregion
}