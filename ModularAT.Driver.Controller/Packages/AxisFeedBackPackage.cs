using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class AxisFeedBackPackage : ControllerPackageBase
{
    private readonly int _allCount;

    public AxisFeedBackPackage(int allCount)
    {
        _allCount = allCount;
    }

    public List<AxisFeedBackItem> AxisFeedBackItems { get; set; }

    public override void Package(in ByteBlock byteBlock)
    {
        foreach (var item in AxisFeedBackItems)
        {
            byteBlock.Write(item.IAxisCurObject);
            byteBlock.Write(item.IAxisCurObjectID);
            byteBlock.Write(item.UiAxisDrvErrCode);
            byteBlock.Write(item.UiAxisMotionErrCode);
            byteBlock.Write(item.DiAxisCurPos);
            byteBlock.Write(item.DiAxisCurVel);
            byteBlock.Write(item.UdiAxisRunState);
        }
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        AxisFeedBackItems = new List<AxisFeedBackItem>();
        var dataSize = Marshal.SizeOf<AxisFeedBackItem>();
        var count = _allCount / dataSize;
        byteBlock.SeekToStart();
        for (var i = 0; i < count; i++)
        {
            var item = new AxisFeedBackItem
            {
                //item.IAxisID = byteBlock.ReadInt32();
                IAxisCurObject = byteBlock.ReadInt16(),
                IAxisCurObjectID = byteBlock.ReadInt16(),
                UiAxisDrvErrCode = byteBlock.ReadUInt32(),
                UiAxisMotionErrCode = byteBlock.ReadUInt32(),
                DiAxisCurPos = byteBlock.ReadInt32(),
                DiAxisCurVel = byteBlock.ReadInt32(),
                UdiAxisRunState = byteBlock.ReadUInt32()
            };
            //var item = byteBlock.ReadPackage<AxisFeedBackItem>();
            AxisFeedBackItems.Add(item);
        }
    }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeFeedBackEnum.CMD_FEEDBACK_MOVER_STATE;
    }

    #region 备用

    //public short IAxisID { get; set; }
    //public short IAxisCurObject { get; set; }

    //public short IAxisCurObjectID { get; set; }

    //public uint UiAxisDrvErrCode { get; set; }

    //public uint UiAxisMotionErrCode { get; set; }

    //public int DiAxisCurPos { get; set; }

    //public int DiAxisCurVel { get; set; }

    //public uint UdiAxisRunState { get; set; }

    #endregion
}