using System;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 控制器包基类，定义了控制器包的基本结构和方法。
/// </summary>
public abstract class ControllerPackageBase : PackageBase
{
    /// <summary>
    /// 用于存储数据的字节块。
    /// </summary>
    protected ByteBlock ByteBlock = new(1024);

    /// <summary>
    /// 数据的大小。
    /// </summary>
    protected int DataSize;

    /// <summary>
    /// 获取数据的大小。
    /// </summary>
    /// <returns>数据的大小。</returns>
    public virtual int GetDataSize()
    {
        // 调用GetByteBlock方法，确保_byteBlock已初始化并包含数据
        GetByteBlock();
        // 将_byteBlock的长度转换为int类型并赋值给_dataSize
        DataSize = (int)ByteBlock.Length;
        return DataSize;
    }

    /// <summary>
    /// 获取字节块。
    /// </summary>
    /// <returns>包含数据的字节块。</returns>
    public virtual ByteBlock GetByteBlock()
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 获取命令类型。
    /// </summary>
    /// <returns>命令类型的枚举值。</returns>
    public abstract uint GetCmdType();
}
