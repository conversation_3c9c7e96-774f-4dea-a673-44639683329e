using System;
using System.Collections.Generic;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class RoAxisFeedBackPackage : ControllerPackageBase
{
    private readonly int _allCount;

    public RoAxisFeedBackPackage(int allCount)
    {
        _allCount = allCount;
    }

    public List<RoAxisFeedBackItem> RoAxisFeedBackItems { get; set; }

    public override void Package(in ByteBlock byteBlock)
    {
        if (RoAxisFeedBackItems == null)
        {
            return;
        }
        foreach (var item in RoAxisFeedBackItems)
        {
            byteBlock.Write(item.BPoweron);
            byteBlock.Write(item.BRunning);
            byteBlock.Write(item.BHomeDone);
            byteBlock.Write(item.BReserved);
            byteBlock.Write(item.DwAxisErrorID);
            byteBlock.Write(item.LrActVelocity);
            byteBlock.Write(item.LrActPosition);
        }
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        RoAxisFeedBackItems = new List<RoAxisFeedBackItem>();
        var dataSize = 24; //Marshal.SizeOf<RoAxisFeedBackItem>();
        var count = _allCount / dataSize;
        byteBlock.SeekToStart();
        for (var i = 0; i < count; i++)
        {
            var item = new RoAxisFeedBackItem();
            item.BPoweron = byteBlock.ReadBoolean();
            item.BRunning = byteBlock.ReadBoolean();
            item.BHomeDone = byteBlock.ReadBoolean();
            item.BReserved = byteBlock.ReadBoolean();
            item.DwAxisErrorID = byteBlock.ReadUInt32();
            item.LrActVelocity = byteBlock.ReadDouble();
            item.LrActPosition = byteBlock.ReadDouble();
            RoAxisFeedBackItems.Add(item);
        }
    }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeFeedBackEnum.CMD_FEEDBACK_ROAXIS_PULSE;
    }
}