using System;
using ModularAT.Entity.Controller;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

public class SysCtrlCmdPackage : ControllerPackageBase, ISysCtrlCmd
{
    [Obsolete("控制对象已经从协议中移除，请勿使用此属性")] public short ICtrlObj { get; set; }

    public short ISysRunMode { get; set; }

    public short ISysAutoRunMode { get; set; }

    public short ISysRunVelRatio { get; set; }

    public short IDrvID { get; set; }

    public ushort UiSysCtrl { get; set; }

    public override uint GetCmdType()
    {
        return (ushort)CmdTypeEnum.CMD_REQUEST_SYSCTRL;
    }

    public override int GetDataSize()
    {
        GetByteBlock();
        DataSize = (int)ByteBlock.Length;
        return DataSize;
    }

    public override ByteBlock GetByteBlock()
    {
        ByteBlock ??= new ByteBlock(1024);
        if (ByteBlock.Length == 0)
        {
            //_byteBlock.Write(ICtrlObj);//不需要了
            ByteBlock.Write(ISysRunMode);
            ByteBlock.Write(ISysAutoRunMode);
            ByteBlock.Write(ISysRunVelRatio);
            ByteBlock.Write(IDrvID);
            ByteBlock.Write(UiSysCtrl);
        }

        return ByteBlock;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        GetByteBlock();
        byteBlock.Write(ByteBlock);
        //byteBlock.WriteByteBlock(_byteBlock);
        ByteBlock.Dispose();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }
}