using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using log4net.Core;
using ModularAT.Entity.Config;
using ModularAT.Entity.Controller;
using ModularAT.Entity.Enum;
using ModularAT.Entity.OnlineConfig;
using ModularAT.Service.Setting;

namespace ModularAT.Driver.Controller;

public class ControllerContext
{
    private static ControllerContext _instance;
    public static ControllerContext Instance => _instance ??= new ControllerContext();

    private ControllerContext()
    {
        #region 加载配置

        //先加载配置文件，再加载控制器反馈的配置
        CurrentViewConfig = OnlineConfigService.LoadConfig<Online_ViewLineConfig>(LineConfigEnum.UIViewLineCfgPara);
        CurrentSysConfig = OnlineConfigService.LoadConfig<Online_SysCfgDto>(LineConfigEnum.SysCfgPara);
        CurrentStationConfigs =
            OnlineConfigService.LoadConfig<List<Online_StationCfgDto>>(LineConfigEnum.StationCfgPara);
        CurrentViewLineConfigs = ControllerSettingService.GetViewLineConfig();

        #endregion
    }

    #region 控制器反馈信息

    /// <summary>
    /// 当前系统反馈
    /// </summary>
    public SysFeedBackModel SysFeedBack { get; internal set; }

    /// <summary>
    /// 当前轴反馈
    /// </summary>
    public List<AxisFeedBackModel> AxisFeedBacks { get; internal set; }

    /// <summary>
    /// 当前旋转轴反馈
    /// </summary>
    public List<RoAxisFeedBackModel> RoAxisFeedBacks { get; internal set; }

    /// <summary>
    /// 当前接驳状态
    /// </summary>
    public List<TransStateFeedBackModel> TransStateFeedBacks { get; internal set; }

    public event Action<NoticeModel> NoticeChanged;

    public void OnNoticeChanged(NoticeModel noticeModel)
    {
        NoticeChanged?.Invoke(noticeModel);
    }

    #endregion

    #region 全局配置

    /// <summary>
    /// 当前上位机配置的UI视图
    /// </summary>
    public Online_ViewLineConfig CurrentViewConfig { get; internal set; }

    /// <summary>
    /// 当前上位机配置的动子列表
    /// </summary>
    public List<Online_MoverCfgDto> CurrentMoverConfigs { get; internal set; }

    /// <summary>
    /// 当前上位机配置的工位列表
    /// </summary>
    public List<Online_StationCfgDto> CurrentStationConfigs { get; set; }

    /// <summary>
    /// 当前上位机系统配置
    /// </summary>
    public Online_SysCfgDto CurrentSysConfig { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ViewLineConfig> CurrentViewLineConfigs { get; internal set; }

    #endregion
}