using ModularAT.Localization.Resources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using ModularAT.Common.Extensions;
using ModularAT.Common.Log;
using ModularAT.Driver.Controller.Adapter;
using ModularAT.Driver.Controller.Packages;
using ModularAT.Entity;
using ModularAT.Entity.Config;
using ModularAT.Entity.Controller;
using ModularAT.Entity.OnlineConfig;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace ModularAT.Driver.Controller;

public class ControlerTcpClient
{
    #region Variable

    // 创建一个新的TcpClient实例
    private readonly TcpClient tcpClient = new();

    // 定义一个IReceiver类型的变量，初始值为null
    private IReceiver receiver = null;

    // 定义一个只读的IWaitingClient<TcpClient>类型的变量
    private readonly IWaitingClient<TcpClient> waitClient;

    // 定义一个布尔类型的变量，用于表示是否已连接
    private bool isConnected;

    // 定义一个公共属性IsConnected，用于获取和设置isConnected变量的值
    public bool IsConnected
    {
        get => Volatile.Read(ref isConnected);
        private set
        {
            isConnected = value;
            // 使用WeakReferenceMessenger发送一个ValueChangedMessage<bool>消息，消息的内容为isConnected的值，消息的键为"ControllerTcpClient"
            WeakReferenceMessenger.Default.Send(new ValueChangedMessage<bool>(isConnected), "ControllerTcpClient");
        }
    }

    #endregion

    #region Function

    // 构造函数，初始化TcpClient的事件处理程序和等待客户端
    public ControlerTcpClient()
    {
        // 设置TcpClient的Connecting事件处理程序，当即将连接到服务器时执行
        tcpClient.Connecting = (client, e) => { return EasyTask.CompletedTask; }; //即将连接到服务器，此时已经创建socket，但是还未建立tcp
        // 设置TcpClient的Connected事件处理程序，当成功连接到服务器时执行
        tcpClient.Connected = (client, e) =>
        {
            // 设置IsConnected属性为true，表示已连接
            IsConnected = true;
            // #if RELEASE
            // 启动心跳检测
            Heartbeat();
            // #endif
            // 返回一个已完成的任务
            return EasyTask.CompletedTask;
        }; //成功连接到服务器
        // 设置TcpClient的Disconnected事件处理程序，当从服务器断开连接时执行
        tcpClient.Disconnected = (client, e) =>
        {
            // 设置IsConnected属性为false，表示已断开连接
            IsConnected = false;
            // 返回一个已完成的任务
            return EasyTask.CompletedTask;
        }; //从服务器断开连接，当连接不成功时不会触发。
        // 设置TcpClient的Received事件处理程序，当接收到数据时执行
        tcpClient.Received = Receive;
        // 调用CreateWaitingClient方法获取到IWaitingClient的对象，并将其赋值给waitClient变量
        // 设置用于筛选的fun委托，当返回为true时，才会响应返回
        waitClient = tcpClient.CreateWaitingClient(new WaitingOptions
        {
            FilterFunc = response => // 设置用于筛选的fun委托，当返回为true时，才会响应返回
            {
                return true;

                // if (response.Data.Length == 1)
                // {
                //     return true;
                // }
                // return false;
            }
        });
        // 订阅UiLogAppender的LogReceived事件，当接收到日志时执行OnLogReceived方法
        UiLogAppender.LogReceived += OnLogReceived;
    }

    /// <summary>
    /// 设置客户端连接参数
    /// </summary>
    /// <param name="config"></param>
    public void Setup(TcpParamConfig config)
    {
        // 设置TcpClient的连接参数
        tcpClient?.Setup(new TouchSocketConfig()
                // 设置远程IP地址和端口号
                .SetRemoteIPHost($"{config.Ip}:{config.Port}")
                // 设置TCP数据处理适配器
                .SetTcpDataHandlingAdapter(() => new ControllerFixedHeaderDataHandlingAdapter())
                // 配置容器，添加一个ControllerLog类型的日志记录器
                .ConfigureContainer(a => a.AddLogger(new ControllerLog()))
            // 配置插件，使用重连插件，尝试连接5次，每次间隔1秒
            // .ConfigurePlugins(a =>
            // {
            //     a.UseReconnection(5, true); //如需永远尝试连接，tryCount设置为-1即可。
            // })
        );
        // 创建一个接收器
        //receiver = tcpClient.CreateReceiver();
        // 设置字节序为大端
        //TouchSocketBitConverter.DefaultEndianType = EndianType.Big;//设置字节序为大端
    }

    /// <summary>
    /// 主动连接
    /// </summary>
    /// <returns></returns>
    public Result Connnect()
    {
        // 尝试连接到服务器
        return tcpClient.TryConnect();
    }

    /// <summary>
    /// 主动断开
    /// </summary>
    public void Close()
    {
        // 安全关闭TcpClient连接
        tcpClient.SafeClose();
    }

    /// <summary>
    ///     发送封包对象
    /// </summary>
    /// <param name="servoPackage"></param>
    public void Send(ControllerPackageBase servoPackage)
    {
        // 检查是否已连接
        if (!CheckConnected()) return;
        // 创建一个ControllerTcpRequest对象
        var requestInfo = new ControllerTcpRequest(servoPackage);
        // 发送请求信息
        tcpClient.Send(requestInfo);
        // 记录发送的数据
        tcpClient.Logger.Info(Lang.ControlerTcpClient_Send_data + requestInfo.ByteArray.ByBytesToHexString(" "));
    }

    /// <summary>
    ///     发送自定义类型命令
    /// </summary>
    /// <param name="cmdType">命令类型</param>
    /// <param name="data">数据</param>
    /// <param name="isLog">是否记录日志</param>
    public void Send(ushort cmdType, byte[] data = null, bool isLog = false)
    {
        // 检查是否已连接
        if (!CheckConnected()) return;
        // 创建一个ControllerTcpRequest对象
        var requestInfo = new ControllerTcpRequest();
        // 设置命令类型
        requestInfo.CmdType = cmdType;
        // 设置数据长度
        requestInfo.BodyLength = data != null ? data.Length : 0;
        // 设置数据
        requestInfo.Data = data;
        // 发送请求信息
        tcpClient.Send(requestInfo);
        // 记录发送的数据
        if (isLog)
            tcpClient.Logger.Info(Lang.ControlerTcpClient_Send_data + requestInfo.ByteArray.ByBytesToHexString(" "));
    }

    /// <summary>
    ///     发送十六进制字符串
    /// </summary>
    /// <param name="hexString"></param>
    public void Send(string hexString, bool bLog = true)
    {
        // 检查是否已连接
        if (!CheckConnected()) return;
        // 检查十六进制字符串是否为空
        if (!hexString.IsNullOrEmpty())
        {
            // 将十六进制字符串转换为字节数组
            var d = hexString.ByHexStringToBytes(" ");
            // 发送字节数组
            tcpClient.Send(d);
            // 记录发送的数据
            if (bLog) tcpClient.Logger.Debug(Lang.ControlerTcpClient_Send_data + hexString);
        }
    }

    /// <summary>
    ///     发送并等待返回
    /// </summary>
    /// <param name="inputData"></param>
    /// <returns>字节数组</returns>
    public async Task<byte[]> SendWaitReturnAsync(byte[] inputData, int timeout = 5000)
    {
        // 检查是否已连接
        if (!CheckConnected()) return default;
        // 发送数据并等待返回
        var result = await waitClient.SendThenReturnAsync(inputData, timeout);
        // 记录发送的数据和返回的数据
        tcpClient.Logger.Info($"发送数据：{inputData.ByBytesToHexString(" ")}; 收到回应：{result?.ByBytesToHexString(" ")}");
        // 返回结果
        return result;
    }


    /// <summary>
    ///     发送并等待返回RequestInfo
    /// </summary>
    /// <param name="inputData">完整命令的字节数组</param>
    /// <param name="timeout">等待回复时间，默认2秒</param>
    /// <returns></returns>
    public async Task<IRequestInfo> SendWaitReturnRequestInfoAsync(ControllerPackageBase servoPackage,
        int timeout = 1000)
    {
        // 检查是否已连接
        if (!CheckConnected()) return default;
        // 创建一个ControllerTcpRequest对象
        var requestInfo = new ControllerTcpRequest(servoPackage);
        // 获取请求信息的字节数组
        var inputData = requestInfo.BuildAsBytes();
        try
        {
            // 发送请求并等待响应
            var responsedData = await waitClient.SendThenResponseAsync(inputData, timeout);
            // 获取响应的RequestInfo
            var responsed = responsedData.RequestInfo; // 同步收到的RequestInfo
            // 记录发送的数据和收到的回复
            tcpClient.Logger.Info($"发送数据：{inputData.ByBytesToHexString(" ")}; 收到回复：{responsed?.ToJsonString()}");
            // 返回请求信息
            return requestInfo;
        }
        catch (Exception e)
        {
            // 如果是超时异常
            if (e is TimeoutException)
                // 记录警告信息
                tcpClient?.Logger.Warning($"发送数据：{inputData.ByBytesToHexString(" ")}成功，等待回复超时！");
            else
                // 记录错误信息
                tcpClient?.Logger.Error(e, $"发送数据：{inputData.ByBytesToHexString(" ")}成功，等待回复失败！");
            // 返回空
            return null;
        }
    }

    /// <summary>
    /// 构建字节数组
    /// </summary>
    /// <param name="cmdType">命令类型</param>
    /// <param name="data">数据</param>
    /// <returns>构建的字节数组</returns>
    public byte[] BuildBytes(uint cmdType, byte[] data = null)
    {
        // 创建一个ControllerTcpRequest对象
        var requestInfo = new ControllerTcpRequest();
        // 设置命令类型
        requestInfo.CmdType = cmdType;
        // 设置数据长度
        requestInfo.BodyLength = data != null ? data.Length : 0;
        // 设置数据
        requestInfo.Data = data;
        // 返回构建的字节数组
        return requestInfo.BuildAsBytes();
    }

    /// <summary>
    /// 接受到数据后处理
    /// </summary>
    /// <param name="client"></param>
    /// <param name="e"></param>
    /// <returns></returns>
    private Task Receive(TcpClient client, ReceivedDataEventArgs e)
    {
        // return Task.Run(() =>
        // {
        if (e.RequestInfo is ControllerTcpResponse requestInfo)
        {
            //tcpClient.Logger.Debug($"接收数据：CmdType: {requestInfo.CmdType.ToString("X")}, BodyLength: {requestInfo.BodyLength}, Data: {requestInfo.Data.ByBytesToHexString(" ")}");
            var data = requestInfo.Data;
            if (data.Length == 0) return Task.CompletedTask;
            switch (requestInfo.CmdType)
            {
                //动子反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_MOVER_STATE:
                {
                    var ret = new AxisFeedBackPackage(requestInfo.BodyLength);
                    using (var bBlock = new ByteBlock(data))
                    {
                        ret.Unpackage(bBlock);
                    }

                    var convertedList = ret.AxisFeedBackItems
                        .Select(item => item.MapTo<AxisFeedBackModel>());
                    var instanceAxisFeedBacks = convertedList.ToList();
                    ControllerContext.Instance.AxisFeedBacks = instanceAxisFeedBacks;

                    // 处理轴反馈通知
                    for (var index = 0; index < ret.AxisFeedBackItems.Count; index++)
                    {
                        var item = ret.AxisFeedBackItems[index];
                        var noticeModel = item.ToNoticeModel(index);
                        ControllerContext.Instance.OnNoticeChanged(noticeModel);
                    }

                    WeakReferenceMessenger.Default.Send(
                        new ValueChangedMessage<IEnumerable<AxisFeedBackModel>>(instanceAxisFeedBacks));
                }
                    break;
                //线体反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_LINEBODY_STATE:
                {
                    var ret = new TransStateFeedBackPackage(requestInfo.BodyLength);
                    using (var bBlock = new ByteBlock(data))
                    {
                        ret.Unpackage(bBlock);
                    }

                    var convertedList = ret.TransStateFeedBackItems
                        .Select(item => item.MapTo<TransStateFeedBackModel>());
                    var transStateFeedBackModels = convertedList.ToList();
                    ControllerContext.Instance.TransStateFeedBacks = transStateFeedBackModels;
                    WeakReferenceMessenger.Default.Send(
                        new ValueChangedMessage<IEnumerable<TransStateFeedBackModel>>(transStateFeedBackModels));
                }
                    break;
                //系统反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_SYSTEM_STATE:
                {
                    var ret = new SysFeedBackPackage(requestInfo.BodyLength);
                    using (var bBlock = new ByteBlock(data))
                    {
                        ret.Unpackage(bBlock);
                    }

                    for (var index = 0; index < ret.SysFeedBackItems.Count; index++)
                    {
                        var model = ret.SysFeedBackItems[index].MapTo<SysFeedBackModel>();
                        var noticeModel = ret.SysFeedBackItems[index].ToNoticeModel();
                        ControllerContext.Instance.OnNoticeChanged(noticeModel);
                        ControllerContext.Instance.SysFeedBack = model;
                        WeakReferenceMessenger.Default.Send(new ValueChangedMessage<SysFeedBackModel>(model));
                    }
                }
                    break;
                //旋转轴反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_ROAXIS_PULSE:
                {
                    var ret = new RoAxisFeedBackPackage(requestInfo.BodyLength);
                    using (var bBlock = new ByteBlock(data))
                    {
                        ret.Unpackage(bBlock);
                    }

                    var convertedList = ret.RoAxisFeedBackItems
                        .Select(item => item.MapTo<RoAxisFeedBackModel>());
                    var instanceRoAxisFeedBacks = convertedList.ToList();
                    ControllerContext.Instance.RoAxisFeedBacks = instanceRoAxisFeedBacks;

                    // 处理旋转轴反馈通知
                    foreach (var (item, index) in ret.RoAxisFeedBackItems.Select((value, i) => (value, i)))
                    {
                        var noticeModel = item.ToNoticeModel(index);
                        ControllerContext.Instance.OnNoticeChanged(noticeModel);
                    }

                    WeakReferenceMessenger.Default.Send(
                        new ValueChangedMessage<IEnumerable<RoAxisFeedBackModel>>(instanceRoAxisFeedBacks));
                }
                    break;
                //系统配置信息
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_SYSCFG_INFO:
                {
                    var ret = new SysCfgInfoFeedbackPackage();
                    using (var bBlock = new ByteBlock(data))
                    {
                        ret.Unpackage(bBlock);
                    }

                    var sysCfgDto = ret.Data.MapTo<Online_SysCfgDto>();
                    ControllerContext.Instance.CurrentSysConfig = sysCfgDto;
                    WeakReferenceMessenger.Default.Send(new ValueChangedMessage<Online_SysCfgDto>(sysCfgDto));
                }
                    break;
                //工位反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_STATION_STATE:
                {
                    var ret = new StationsCfgInfoFeedbackPackage(requestInfo.BodyLength);
                    using (var bBlock = new ByteBlock(data))
                    {
                        ret.Unpackage(bBlock);
                    }

                    var convertedList = ret.Datas
                        .Select(item => item.MapTo<Online_StationCfgDto>());
                    var onlineStationCfgDtos = convertedList.ToList();
                    WeakReferenceMessenger.Default.Send(
                        new ValueChangedMessage<IEnumerable<Online_StationCfgDto>>(onlineStationCfgDtos));
                }
                    break;
                //设备反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_FACLLITY_STATU:
                {
                    var facllityStatus = BitConverter.ToUInt32(data, 0).ToFacilityStatus();
                    WeakReferenceMessenger.Default.Send(
                        new ValueChangedMessage<FacilityStatusFeedback>(facllityStatus));
                }
                    break;
                //初始化状态反馈
                case (uint)CmdTypeFeedBackEnum.CMD_FEEDBACK_INITIALIZE_STEP:
                {
                    var step = (InitializationStepEnum)BitConverter.ToUInt32(data, 0);
                    WeakReferenceMessenger.Default.Send(
                        new ValueChangedMessage<InitializationStepEnum>(step));
                }
                    break;
            }
        }
        else
        {
            tcpClient.Logger.Warning(Lang.ControlerTcpClient_Adapter_parsing_failed);
        }

        // });
        return Task.CompletedTask;
    }

    private void OnLogReceived(string logMessage)
    {
        WeakReferenceMessenger.Default.Send(new ValueChangedMessage<string>(logMessage));
    }

    /// <summary>
    /// 检查是否连接
    /// </summary>
    /// <returns></returns>
    private bool CheckConnected()
    {
        if (!IsConnected) MsgToUiHelper.SendMsgError(Lang.ControlerTcpClient_Controller_not_connected);
        return IsConnected;
    }

    /// <summary>
    /// 检查是否一问一答
    /// </summary>
    /// <returns></returns>
    private bool CheckProactivelyRev() => ControllerContext.Instance.CurrentSysConfig.ISysFeedbackMode == 1;

    /// <summary>
    /// 定时发送心跳包
    /// </summary>
    private void Heartbeat()
    {
        using var cts = new CancellationTokenSource();

        var tasks = new Dictionary<Action, int>
        {
            [RequestAuthorization] = 1000,
            [RequestGetRoAxisFeedBack] = 10,
            [RequestGetLineBody] = 100,
            [RequestGetSysFeedBack] = 100,
            [RequestGetAxisFeedBack] = 10
        };

        foreach (var taskConfig in tasks)
        {
            Task.Run(async () =>
            {
                while (IsConnected)
                    try
                    {
                        if (CheckProactivelyRev()) 
                            taskConfig.Key.Invoke();
                        await Task.Delay(taskConfig.Value);
                    }
                    catch (Exception ex)
                    {
                        tcpClient.Logger.Warning(ex, Lang.ControlerTcpClient_Controller_heartbeat_failed);
                        IsConnected = ex is not NotConnectedException;
                    }
            }, cts.Token);
        }
    }

    #endregion

    #region 预设数据包

    /// <summary>
    ///     授权包
    /// </summary>
    private void RequestAuthorization()
    {
        Send("AA 00 00 00 00 00 00 00 BB 00 00 00 00 00 00 00", false);
    }

    public void RequestGetInitStatus()
    {
        Send((ushort)CmdTypeEnum.CMD_REQUEST_GET_AXIS_MOVE_DATA);
    }

    /// <summary>
    /// 请求获取轴反馈信息
    /// </summary>
    public void RequestGetAxisFeedBack()
    {
        Send((ushort)CmdTypeEnum.CMD_REQUEST_GET_MOVER_STATE); //16387
    }

    /// <summary>
    /// 请求获取系统状态
    /// </summary>
    public void RequestGetSysFeedBack()
    {
        Send((ushort)CmdTypeEnum.CMD_REQUEST_GET_SYS_STATE); //16385
    }

    /// <summary>
    /// 请求获取线体反馈信息
    /// </summary>
    public void RequestGetLineBody()
    {
        Send((ushort)CmdTypeEnum.CMD_REQUEST_GET_LINEBODY_STATE); //16388
    }

    /// <summary>
    /// 请求获取旋转轴反馈信息
    /// </summary>
    public void RequestGetRoAxisFeedBack()
    {
        Send((ushort)CmdTypeEnum.CMD_REQUEST_GET_ROAXIS_PULSE);
    }


    /// <summary>
    ///     设置设备状态
    /// </summary>
    /// <param name="data"></param>
    public void SetFacllityState(FacilityStatusCtl data)
    {
        CheckConnected();
        Send((ushort)CmdTypeEnum.CMD_REQUEST_FACILITY_CTR, BitConverter.GetBytes(data.ToCmd()));
    }

    #endregion
}