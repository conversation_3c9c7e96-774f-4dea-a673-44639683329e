using System;
using log4net;
using log4net.Config;
using TouchSocket.Core;
using ILog = TouchSocket.Core.ILog;

[assembly: XmlConfigurator(ConfigFile = "log4net.config", Watch = true)]

namespace ModularAT.Driver.Controller;

internal class ControllerLog : ILog
{
    private readonly log4net.ILog m_logger;

    public ControllerLog()
    {
        //this.m_logger = log4net.LogManager.GetLogger(typeof(ControllerLog));
        m_logger = LogManager.GetLogger("Controller");
    }

    public LogLevel LogLevel { get; set; }

    public void Log(LogLevel logLevel, object source, string message, Exception exception)
    {
        //此处就是实际的日志输出
        switch (logLevel)
        {
            case LogLevel.Trace:
            case LogLevel.Debug:
                m_logger.Debug(message, exception);
                break;

            case LogLevel.Info:
                m_logger.Info(message, exception);
                break;

            case LogLevel.Warning:
                m_logger.Warn(message, exception);
                break;

            case LogLevel.Error:
            case LogLevel.Critical:
                m_logger.Error(message, exception);
                break;

            case LogLevel.None:
            default:
                break;
        }
    }
}