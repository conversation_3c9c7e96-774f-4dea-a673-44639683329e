using ModularAT.Localization.Resources;
using System;
using log4net.Core;
using ModularAT.Entity.Controller;
using ModularAT.Service.Setting;

namespace ModularAT.Driver.Controller;

internal static class ControllerHelper
{
    /// <summary>
    /// 将uint类型的状态值转换为FacilityStatusFeedback结构体。
    /// </summary>
    /// <param name="statusValue">uint类型的状态值。</param>
    /// <returns>转换后的FacilityStatusFeedback结构体。</returns>
    public static FacilityStatusFeedback ToFacilityStatus(this uint statusValue) => new()
    {
        // 使用位运算检查各个状态标志位
        IsInitializing = HasFlag(statusValue, FacllityStatuFeedBackEnum.Initializing),
        IsRunning = HasFlag(statusValue, FacllityStatuFeedBackEnum.Running),
        IsStop = HasFlag(statusValue, FacllityStatuFeedBackEnum.Stopping),
        IsResetting = HasFlag(statusValue, FacllityStatuFeedBackEnum.Resetting),
        IsEmergencyStopping = HasFlag(statusValue, FacllityStatuFeedBackEnum.EmergencyStopping),
        IsError = HasFlag(statusValue, FacllityStatuFeedBackEnum.Error),
        IsMaintenance = HasFlag(statusValue, FacllityStatuFeedBackEnum.Maintenance),
        IsNormal = HasFlag(statusValue, FacllityStatuFeedBackEnum.Normal),
        IsManual = HasFlag(statusValue, FacllityStatuFeedBackEnum.Manual)
    };

    /// <summary>
    /// 检查状态值是否包含指定的标志位。
    /// </summary>
    /// <param name="value">要检查的状态值。</param>
    /// <param name="flag">要检查的标志位。</param>
    /// <returns>如果状态值包含指定的标志位，则返回true；否则返回false。</returns>
    private static bool HasFlag(uint value, FacllityStatuFeedBackEnum flag) => (value & (uint)flag) != 0;

    /// <summary>
    /// 检查枚举值是否包含指定的标志位。
    /// </summary>
    /// <param name="value">要检查的枚举值。</param>
    /// <param name="flag">要检查的标志位。</param>
    /// <returns>如果枚举值包含指定的标志位，则返回true；否则返回false。</returns>
    private static bool HasFlag<T>(T value, T flag) where T : Enum =>
        (Convert.ToUInt32(value) & Convert.ToUInt32(flag)) != 0;

    /// <summary>
    /// 将SysFeedBackItem转换为NoticeModel。
    /// </summary>
    /// <param name="sysFeedBack">系统反馈项。</param>
    /// <returns>转换后的通知模型。</returns>
    public static NoticeModel ToNoticeModel(this SysFeedBackItem sysFeedBack)
    {
        var notice = new NoticeModel
        {
            From = "Sys",
            Id = sysFeedBack.ISysErrorAxisID
        };

        var sysState = (SysStateFlags)sysFeedBack.UdiSysState;

        if (IsErrorState(sysFeedBack, sysState))
        {
            notice.Level = Level.Error;
            notice.Message = GetErrorMessage(sysFeedBack);
            SetErrorSource(notice, sysFeedBack);
        }
        else
        {
            // 使用模式匹配简化状态判断逻辑
            (notice.Level, notice.Message) = sysState switch
            {
                var state when HasFlag(state, SysStateFlags.Running) => (Level.Info, Lang.ControllerHelper_System_is_running),
                var state when HasFlag(state, SysStateFlags.Ready) => (Level.Info, Lang.ControllerHelper_System_is_ready),
                var state when HasFlag(state, SysStateFlags.Enabled) => (Level.Info, Lang.ControllerHelper_System_is_enabled),
                var state when HasFlag(state, SysStateFlags.BusStatus) => (Level.Info, Lang.ControllerHelper_System_bus_is_connected),
                _ => (Level.Info, $"系统状态: {sysFeedBack.UdiSysState}")
            };
        }

        return notice;

        // 检查是否处于错误状态
        static bool IsErrorState(SysFeedBackItem feedback, SysStateFlags state)
            => HasFlag(state, SysStateFlags.Error) || feedback.UdiSysErrorCode > 0 || feedback.UdiSysDrvErrorCode > 0;

        // 设置错误来源
        static void SetErrorSource(NoticeModel model, SysFeedBackItem feedback)
        {
            bool isDriveError = feedback.UdiSysDrvErrorCode > 0;
            model.From = isDriveError ? "Servo" : "Sys";
            model.Id = isDriveError ? (int)feedback.UdiSysDrvErrorCode : (int)feedback.UdiSysErrorCode;
        }

        // 获取错误消息
        static string GetErrorMessage(SysFeedBackItem sysFd) => sysFd.UdiSysDrvErrorCode > 0
            ? $"驱动器错误: ErrCode={sysFd.UdiSysDrvErrorCode}, 驱动错误名称={sysFd.SSysDrvErrName}, Axis ID={sysFd.ISysErrorAxisID}"
            : sysFd.UdiSysErrorCode > 0
                ? $"系统错误: ErrCode={sysFd.UdiSysErrorCode}, {ControllerSettingService.GetErrorCodeDescription(sysFd.UdiSysErrorCode)}"
                : Lang.ControllerHelper_System_is_in_error_state;
    }


    /// <summary>
    /// 将AxisFeedBackItem转换为NoticeModel。
    /// </summary>
    /// <param name="axisFeedBack">轴反馈项。</param>
    /// <param name="axisId">轴ID。</param>
    /// <returns>转换后的通知模型。</returns>
    public static NoticeModel ToNoticeModel(this AxisFeedBackItem axisFeedBack, int axisId = 0)
    {
        var notice = new NoticeModel
        {
            From = "Axis",
            Id = axisId
        };
    
        var axisState = (AxisStateFlags)axisFeedBack.UdiAxisRunState;
    
        // 使用模式匹配简化状态判断
        var status = DetermineAxisStatus(axisState, axisFeedBack);
    
        notice.Level = status.Level;
        notice.Message = status.Message;
        return notice;
    }
    
    private static (Level Level, string Message) DetermineAxisStatus(AxisStateFlags state, AxisFeedBackItem feedback)
    {
        // 定义状态检查函数
        bool HasState(AxisStateFlags flag) => state.HasFlag(flag);
    
        // 错误状态检查
        if (HasState(AxisStateFlags.Error) || feedback.UiAxisDrvErrCode > 0 || feedback.UiAxisMotionErrCode > 0)
        {
            return (Level.Error, GetErrorMessage(state, feedback));
        }
    
        // 警告状态检查
        if (HasState(AxisStateFlags.Alarm) || HasState(AxisStateFlags.PositiveLimit) || 
            HasState(AxisStateFlags.NegativeLimit))
        {
            return (Level.Warn, GetWarningMessage(state, feedback));
        }
    
        // 通知状态检查
        if (HasState(AxisStateFlags.LeftCollision) || HasState(AxisStateFlags.RightCollision) ||
            HasState(AxisStateFlags.ReachedTarget) || HasState(AxisStateFlags.OnWorkstation))
        {
            return (Level.Notice, GetNoticeMessage(state, feedback));
        }
    
        // 常规状态
        return (Level.Info, GetStatusMessage(state, feedback));
    }
    
    private static string GetErrorMessage(AxisStateFlags state, AxisFeedBackItem feedback)
    {
        return feedback.UiAxisDrvErrCode > 0
            ? FormatAxisMessage(Lang.ControllerHelper_Axis_driver_error, feedback, $"ErrCode={feedback.UiAxisDrvErrCode}")
            : feedback.UiAxisMotionErrCode > 0
                ? FormatAxisMessage(Lang.ControllerHelper_Axis_movement_error, feedback, $"ErrCode={feedback.UiAxisMotionErrCode}")
                : FormatAxisMessage(Lang.ControllerHelper_Axis_error_status, feedback);
    }
    
    private static string GetWarningMessage(AxisStateFlags state, AxisFeedBackItem feedback)
    {
        return state switch
        {
            var s when s.HasFlag(AxisStateFlags.Alarm) => FormatAxisMessage(Lang.ControllerHelper_Axis_alarm, feedback),
            var s when s.HasFlag(AxisStateFlags.PositiveLimit) => FormatAxisMessage(Lang.ControllerHelper_Positive_limit_of_axis, feedback),
            var s when s.HasFlag(AxisStateFlags.NegativeLimit) => FormatAxisMessage(Lang.ControllerHelper_Negative_limit_of_axis, feedback),
            _ => FormatAxisMessage(Lang.ControllerHelper_Axis_warning, feedback, $"StateCode={state}")
        };
    }
    
    private static string GetNoticeMessage(AxisStateFlags state, AxisFeedBackItem feedback)
    {
        return state switch
        {
            var s when s.HasFlag(AxisStateFlags.LeftCollision) => FormatAxisMessage(Lang.ControllerHelper_Axis_in_left_position, feedback),
            var s when s.HasFlag(AxisStateFlags.RightCollision) => FormatAxisMessage(Lang.ControllerHelper_Axis_in_right_position, feedback),
            var s when s.HasFlag(AxisStateFlags.ReachedTarget) => 
                FormatAxisMessage(Lang.ControllerHelper_Axis_has_reached_the_target_position, feedback, $"Position={feedback.DiAxisCurPos/1000f}"),
            var s when s.HasFlag(AxisStateFlags.OnWorkstation) => 
                FormatAxisMessage(Lang.ControllerHelper_Axis_is_at_the_workstation, feedback, $"Position={feedback.DiAxisCurPos/1000f}"),
            _ => FormatAxisMessage(Lang.ControllerHelper_Axis_notification, feedback, $"StateCode={state}")
        };
    }
    
    private static string GetStatusMessage(AxisStateFlags state, AxisFeedBackItem feedback)
    {
        if (state.HasFlag(AxisStateFlags.Running))
        {
            return FormatAxisMessage(Lang.ControllerHelper_Axis_is_running, feedback, 
                $"Position={feedback.DiAxisCurPos/1000f}, Velocity={feedback.DiAxisCurVel}");
        }
        
        if (state.HasFlag(AxisStateFlags.Enabled))
        {
            return FormatAxisMessage(Lang.ControllerHelper_Axis_is_enabled, feedback, $"Position={feedback.DiAxisCurPos/1000f}");
        }
        
        return FormatAxisMessage(Lang.ControllerHelper_Axis_status, feedback, 
            $"Position={feedback.DiAxisCurPos/1000f}, StateCode={state}");
    }
    
    private static string FormatAxisMessage(string prefix, AxisFeedBackItem feedback, string details = null)
    {
        return string.IsNullOrEmpty(details)
            ? $"{prefix}: Line={feedback.IAxisCurObjectID}"
            : $"{prefix}: Line={feedback.IAxisCurObjectID}, {details}";
    }

    /// <summary>
    /// 将RoAxisFeedBackItem转换为NoticeModel。
    /// </summary>
    /// <param name="roAxisFeedBack">旋转轴反馈项。</param>
    /// <param name="roAxisId">旋转轴ID。</param>
    /// <returns>转换后的通知模型。</returns>
    public static NoticeModel ToNoticeModel(this RoAxisFeedBackItem roAxisFeedBack, int roAxisId = 0)
    {
        var notice = new NoticeModel
        {
            From = "RoAxis",
            Id = roAxisId
        };

        // 检查是否有错误
        if (roAxisFeedBack.DwAxisErrorID > 0)
        {
            notice.Level = Level.Error;
            notice.Message = $"旋转轴错误: ErrCode={roAxisFeedBack.DwAxisErrorID}";
            return notice;
        }

        // 无错误时设置信息级别
        notice.Level = Level.Info;
        notice.Message = GetStatusMessage();
        return notice;

        // 获取状态消息
        string GetStatusMessage()
        {
            return roAxisFeedBack.BRunning
                ? FormatRoAxisMessage(Lang.ControllerHelper_Rotary_axis_is_running, $"Velocity={roAxisFeedBack.LrActVelocity:F3}")
                : roAxisFeedBack.BHomeDone
                    ? FormatRoAxisMessage(Lang.ControllerHelper_Rotary_axis_homing_completed)
                    : roAxisFeedBack.BPoweron
                        ? FormatRoAxisMessage(Lang.ControllerHelper_Rotary_axis_is_enabled)
                        : FormatRoAxisMessage(Lang.ControllerHelper_Rotary_axis);
        }

        // 格式化旋转轴消息
        string FormatRoAxisMessage(string prefix, string details = "") =>
            string.IsNullOrEmpty(details)
                ? $"{prefix}: Position={roAxisFeedBack.LrActPosition:F3}"
                : $"{prefix}: Position={roAxisFeedBack.LrActPosition:F3}, {details}";
    }
}