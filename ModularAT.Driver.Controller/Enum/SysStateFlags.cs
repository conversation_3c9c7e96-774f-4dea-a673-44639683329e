using System;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 系统状态标志位
/// </summary>
[Flags]
public enum SysStateFlags : uint
{
    Ready = 1 << 0,            // 系统已准备好
    Enabled = 1 << 1,          // 系统使能状态
    Error = 1 << 2,            // 系统错误状态
    Running = 1 << 3,          // 系统运行状态
    BusStatus = 1 << 4,        // 系统总线状态
    PlatformVerified = 1 << 5, // 系统平台校验状态
    AxisConfigured = 1 << 6,   // 轴配置完成，可进行轴序列初始化
    MotionConfigured = 1 << 7, // 运动参数配置完成，可进行系统旧状态恢复
    StateRestored = 1 << 8     // 系统恢复旧状态完成
}