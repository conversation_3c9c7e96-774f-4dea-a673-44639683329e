namespace ModularAT.Driver.Controller;

/// <summary>
/// 接驳控制命令的枚举。
/// </summary>
public enum TranCtrlCmdEnum : ushort
{
    /// <summary>
    /// 启用向上运动。
    /// </summary>
    EnableUp = 0x0001, // 16#1

    /// <summary>
    /// 启用向下运动。
    /// </summary>
    EnableDown = 0x0002, // 16#2

    /// <summary>
    /// 停止运动。
    /// </summary>
    Stop = 0x0003, // 16#3

    /// <summary>
    /// 重置
    /// </summary>
    Reset = 0x0004, // 16#4

    /// <summary>
    /// 设置当前位置为零。
    /// </summary>
    SetZero = 0x0005, // 16#5

    /// <summary>
    /// 点动向前运动。
    /// </summary>
    JogForward = 0x0006, // 16#6

    /// <summary>
    /// 点动向后运动。
    /// </summary>
    JogBackward = 0x0007, // 16#7

    /// <summary>
    /// 绝对运动。
    /// </summary>
    AbsoluteMovement = 0x0008, // 16#8

    /// <summary>
    /// 相对运动。
    /// </summary>
    RelativeMovement = 0x0009, // 16#9

    /// <summary>
    /// 工位运动。
    /// </summary>
    StationMovement = 0x000A // 16#A
}