using System;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 轴状态标志位
/// </summary>
[Flags]
public enum AxisStateFlags : uint
{
    /// <summary>
    /// bit0: 单轴使能状态
    /// </summary>
    Enabled = 1u << 0,

    /// <summary>
    /// bit1: 单轴运行状态
    /// </summary>
    Running = 1u << 1,

    /// <summary>
    /// bit2: 单轴报警状态
    /// </summary>
    Alarm = 1u << 2,

    /// <summary>
    /// bit3: 单轴错误状态
    /// </summary>
    Error = 1u << 3,

    /// <summary>
    /// bit4: 单轴左碰撞
    /// </summary>
    LeftCollision = 1u << 4,

    /// <summary>
    /// bit5: 单轴右碰撞
    /// </summary>
    RightCollision = 1u << 5,

    /// <summary>
    /// bit6: 单轴正限位
    /// </summary>
    PositiveLimit = 1u << 6,

    /// <summary>
    /// bit7: 单轴负限位
    /// </summary>
    NegativeLimit = 1u << 7,

    /// <summary>
    /// bit8: 单轴在工位上
    /// </summary>
    OnWorkstation = 1u << 8,

    /// <summary>
    /// bit9: 单轴已到达目标位置
    /// </summary>
    ReachedTarget = 1u << 9
}