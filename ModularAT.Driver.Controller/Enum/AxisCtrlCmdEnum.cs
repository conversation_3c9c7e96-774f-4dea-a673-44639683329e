namespace ModularAT.Driver.Controller;

/// <summary>
/// 轴控制命令枚举，定义了各种轴控制命令。
/// </summary>
public enum AxisCtrlCmdEnum : byte
{
    // 控制命令掩码
    AXIS_CTRLCMD_CTRLMASK = 0xFF,

    // 禁用控制命令
    AXIS_CTRLCMD_DISABLE = 0x00,

    // 启用控制命令
    AXIS_CTRLCMD_ENABLE,

    // 停止控制命令
    AXIS_CTRLCMD_STOP = 0x01,

    // 重置控制命令
    AXIS_CTRLCMD_RESET,

    // 向前点动控制命令
    AXIS_CTRLCMD_JOGFOR = 0x05,

    // 向后点动控制命令
    AXIS_CTRLCMD_JOGBACK = 0x09,

    // 绝对移动控制命令
    AXIS_CTRLCMD_MOVEABS = 0x11,

    // 相对移动控制命令
    AXIS_CTRLCMD_MOVEREL = 0x21,

    // 移动到工位控制命令
    AXIS_CTRLCMD_MOVESTATION = 0x41,

    // 设置零点控制命令
    AXIS_CTRLCMD_SETZERO = 0x80
}
