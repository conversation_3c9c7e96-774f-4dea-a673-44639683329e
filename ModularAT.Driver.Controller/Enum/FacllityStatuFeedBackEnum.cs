using System;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 设施状态反馈枚举，定义了设施的各种状态。
/// </summary>
[Flags]
public enum FacllityStatuFeedBackEnum : ushort
{
    /// <summary>
    /// 按钮灯反馈：初始化中
    /// </summary>
    Initializing = 1 << 0,

    /// <summary>
    /// 按钮灯反馈：运行中
    /// </summary>
    Running = 1 << 1,

    /// <summary>
    /// 按钮灯反馈：停止中
    /// </summary>
    Stopping = 1 << 2,

    /// <summary>
    /// 按钮灯反馈：复位
    /// </summary>
    Resetting = 1 << 3,

    /// <summary>
    /// 按钮灯反馈：紧急停止
    /// </summary>
    EmergencyStopping = 1 << 4,

    /// <summary>
    /// 三色灯红色：紧急停机或故障报警
    /// </summary>
    Error = 1 << 5,

    /// <summary>
    /// 三色灯黄色：待机状态或准备启动
    /// </summary>
    Maintenance = 1 << 6,

    /// <summary>
    /// 三色灯绿色：系统正常运行中
    /// </summary>
    Normal = 1 << 7,

    /// <summary>
    /// 手动/自动反馈
    /// </summary>
    Manual = 1 << 8
}
