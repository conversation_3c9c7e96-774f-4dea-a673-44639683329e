namespace ModularAT.Driver.Controller;

/// <summary>
///     控制器反馈命令类型枚举
/// </summary>
public enum CmdTypeFeedBackEnum : uint
{
    /// <summary>
    ///     错误
    /// </summary>
    CMD_FEEDBACK_ERROR = 0xEFFF,

    /// <summary>
    ///     空闲
    /// </summary>
    CMD_FEEDBACK_IDLE = 0x0000,

    /// <summary>
    ///     发送命令成功
    /// </summary>
    CMD_FEEDBACK_TRUE = 0x0001,

    /// <summary>
    ///     通讯连接成功
    /// </summary>
    CMD_FEEDBACK_CONNECTED = 0x1000,

    /// <summary>
    ///     系统配置信息
    /// </summary>
    CMD_FEEDBACK_SYSCFG_INFO = 0x1001,

    /// <summary>
    ///     系统状态
    /// </summary>
    CMD_FEEDBACK_SYSTEM_STATE = 0x1002,

    /// <summary>
    ///     工位状态
    /// </summary>
    CMD_FEEDBACK_STATION_STATE = 0x1003,

    /// <summary>
    ///     动子状态
    /// </summary>
    CMD_FEEDBACK_MOVER_STATE = 0x1004,

    /// <summary>
    ///     线体状态
    /// </summary>
    CMD_FEEDBACK_LINEBODY_STATE = 0x1005,

    /// <summary>
    ///     反馈系统所有状态
    /// </summary>
    CMD_FEEDBACK_ALL_SYSTEM_STATE = 0x1006,

    /// <summary>
    ///     控制器版本
    /// </summary>
    CMD_FEEDBACK_CONTROLLER_VERSION = 0x1007,

    /// <summary>
    ///     反馈恢复运动参数
    /// </summary>
    CMD_FEEDBACK_RESUME_MOVE_PARAM = 0x1008,

    /// <summary>
    ///     反馈IO状态
    /// </summary>
    CMD_FEEDBACK_IO_STATE = 0x1009,

    /// <summary>
    ///     反馈旋转轴的实际脉冲
    /// </summary>
    CMD_FEEDBACK_ROAXIS_PULSE = 0x100A,

    /// <summary>
    ///     反馈设备实际状态
    /// </summary>
    CMD_FEEDBACK_FACLLITY_STATU = 0x100B,
    
    /// <summary>
    ///     设备初始化进行状态
    /// </summary>
    CMD_FEEDBACK_INITIALIZE_STEP = 0x100C
}