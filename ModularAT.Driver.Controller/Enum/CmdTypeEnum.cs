namespace ModularAT.Driver.Controller;

/// <summary>
///     客户端请求指令类型
/// </summary>
public enum CmdTypeEnum : ushort
{
    /// <summary>
    ///     通讯连接请求
    /// </summary>
    CMD_REQUEST_CONNECT = 0x1000,

    /// <summary>
    ///     通讯断开请求
    /// </summary>
    CMD_REQUEST_DISCONNECT = 0x1001,

    /// <summary>
    ///     单动子控制
    /// </summary>
    CMD_REQUEST_MOVERCTRL = 0x2000,

    /// <summary>
    ///     所有动子控制
    /// </summary>
    CMD_REQUEST_ALL_MOVER_CTRL = 0x2001,

    /// <summary>
    ///     接驳控制
    /// </summary>
    CMD_REQUEST_TRANCTRL = 0x2002,

    /// <summary>
    ///     系统控制
    /// </summary>
    CMD_REQUEST_SYSCTRL = 0x2003,

    /// <summary>
    ///     轴组运动
    /// </summary>
    CMD_REQUEST_GROUP_MOVE = 0x2004,

    /// <summary>
    ///     设置线体连接状态
    /// </summary>
    CMD_REQUEST_SET_CONNECT_STATE = 0x2005,

    /// <summary>
    ///     系统所有命令同时发送
    /// </summary>
    CMD_REQUEST_ALL_SYSTEM_CTRL = 0x2006,

    /// <summary>
    ///     工位控制
    /// </summary>
    CMD_REQUEST_STATION_CTRL = 0x2007,

    /// <summary>
    ///     开始debug模式
    /// </summary>
    CMD_REQUEST_START_DEBUG_MODE = 0x2008,

    /// <summary>
    ///     停止debug模式
    /// </summary>
    CMD_REQUEST_STOP_DEBUG_MODE = 0x2009,

    /// <summary>
    ///     重启系统
    /// </summary>
    CMD_REQUEST_SYS_REBOOT = 0x200A,

    /// <summary>
    ///     继续上一指令重运行
    /// </summary>
    CMD_REQUEST_SYS_PRE_COMMAND_RERUN = 0x200B,

    /// <summary>
    ///     系统IO控制
    /// </summary>
    CMD_REQUEST_SYS_IO_CTRL = 0x200C,

    // 配置命令
    /// <summary>
    ///     系统信息配置
    /// </summary>
    CMD_REQUEST_CFG_SYSINFO = 0x3000,

    /// <summary>
    ///     电机信息配置
    /// </summary>
    CMD_REQUEST_CFG_MOTORINFO = 0x3001,

    /// <summary>
    ///     从站节点信息配置
    /// </summary>
    CMD_REQUEST_CFG_SLAVENODEINFO = 0x3002,

    /// <summary>
    ///     线体段信息配置
    /// </summary>
    CMD_REQUEST_CFG_LINEINFO = 0x3003,

    /// <summary>
    ///     工位信息配置
    /// </summary>
    CMD_REQUEST_CFG_STATIONINFO = 0x3004,

    /// <summary>
    ///     动子信息配置
    /// </summary>
    CMD_REQUEST_CFG_MOVERINFO = 0x3005,

    /// <summary>
    ///     动子序列初始化->在线
    /// </summary>
    CMD_REQUEST_CFG_MOVERARRAYINFO_ONLINE = 0x3006,

    /// <summary>
    ///     动子序列初始化->配置文件
    /// </summary>
    CMD_REQUEST_CFG_MOVERARRAYINFO_CFGFILE = 0x3007,

    /// <summary>
    ///     动子序列初始化->恢复文件
    /// </summary>
    CMD_REQUEST_CFG_MOVERARRAYINFO_RESUMEFILE = 0x3008,

    /// <summary>
    ///     运动PID配置
    /// </summary>
    CMD_REQUEST_CFG_MOTIONPID = 0x3009,

    /// <summary>
    ///     UI通过TCP在线读命令运动
    /// </summary>
    CMD_REQUEST_CFG_RESUME_MOVE_UI_TCP = 0x300A,

    /// <summary>
    ///     UI通过UI执行运动
    /// </summary>
    CMD_REQUEST_CFG_RESUME_MOVE_UI = 0x300B,

    /// <summary>
    ///     UI通过不进行恢复运动
    /// </summary>
    CMD_REQUEST_CFG_RESUME_MOVE_PLC = 0x300C,

    /// <summary>
    ///     改为保存数据
    /// </summary>
    CMD_REQUEST_CFG_SAVE = 0x300D,

    /// <summary>
    ///     改为动子补偿
    /// </summary>
    CMD_REQUEST_CFG_AXIS_STATION_OFFSEET = 0x300E,

    /// <summary>
    ///     轴恢复上一条运动指令成功
    /// </summary>
    CMD_REQUEST_SYS_AXIS_RESUME_OK = 0x300F,

    /// <summary>
    ///     工位补偿
    /// </summary>
    CMD_REQUEST_CFG_STATION_OFFSEET = 0x3010,

    // 获取命令
    /// <summary>
    ///     获取系统配置信息
    /// </summary>
    CMD_REQUEST_GET_SYSCFG_INFO = 0x4000,

    /// <summary>
    ///     获取系统状态信息
    /// </summary>
    CMD_REQUEST_GET_SYS_STATE = 0x4001,

    /// <summary>
    ///     获取工位状态信息
    /// </summary>
    CMD_REQUEST_GET_STATION_STATE = 0x4002,

    /// <summary>
    ///     获取动子状态信息
    /// </summary>
    CMD_REQUEST_GET_MOVER_STATE = 0x4003,

    /// <summary>
    ///     获取线体状态信息
    /// </summary>
    CMD_REQUEST_GET_LINEBODY_STATE = 0x4004,

    /// <summary>
    ///     获取系统所有状态
    /// </summary>
    CMD_REQUEST_GET_ALL_SYSTEM_STATE = 0x4005,

    /// <summary>
    ///     获取控制器版本信息
    /// </summary>
    CMD_REQUEST_GET_CONTROLLER_VERSIONS = 0x4006,

    /// <summary>
    ///     获取debug模式下数据
    /// </summary>
    CMD_REQUEST_GET_DEBUG_DATA = 0x4007,

    /// <summary>
    ///     获取初始化运动数据
    /// </summary>
    CMD_REQUEST_GET_AXIS_MOVE_DATA = 0x4008,

    /// <summary>
    ///     获取IO所有状态
    /// </summary>
    CMD_REQUEST_GET_IO_ALL_STATE = 0x4009,

    /// <summary>
    ///     获取旋转轴实际脉冲信息
    /// </summary>
    CMD_REQUEST_GET_ROAXIS_PULSE = 0x4010,

    /// <summary>
    ///     设备控制
    ///     运行、停止、复位、紧急停止
    /// </summary>
    CMD_REQUEST_FACILITY_CTR = 0x5000,
    /// <summary>
    ///     设备控制 磁驱初始化复位
    /// </summary>
    CMD_REQUEST_MaglevInit_Reset = 0x5001
    
}