namespace ModularAT.Driver.Controller;

public enum InitializationStepEnum: uint
{
    /// <summary>
    /// 初始化成功
    /// </summary>
    InitSuccess = 1,
    /// <summary>
    /// 第一步：全局数据复位
    /// </summary>
    Step1GlobalDataReset = 0,
    /// <summary>
    /// 第二步：平台校验
    /// </summary>
    Step2PlatformCheck = 10,
    /// <summary>
    /// 第三步：系统参数配置初始化
    /// </summary>
    Step3SystemParamInit = 20,
    /// <summary>
    /// 第四步：从站信息获取
    /// </summary>
    Step4SlaveInfoGet = 30,
    /// <summary>
    /// 第五步：从站地址映射到控制地址
    /// </summary>
    Step5SlaveAddrMap = 40,
    /// <summary>
    /// 第六步：主从站状态校验
    /// </summary>
    Step6MasterSlaveCheck = 50,
    /// <summary>
    /// 第七步：总线-系统等状态初始化完成
    /// </summary>
    Step7BusSystemInitDone = 60,
    /// <summary>
    /// 
    /// </summary>
    Step7BusSystemInitDone2 = 61,
    /// <summary>
    /// 第八步：运动相关参数初始化
    /// </summary>
    Step8MotionParamInit = 70
}