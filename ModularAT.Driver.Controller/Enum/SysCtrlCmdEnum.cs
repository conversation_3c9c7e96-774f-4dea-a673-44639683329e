using System.ComponentModel;

namespace ModularAT.Driver.Controller;

public enum SysCtrlCmdEnum : byte
{
    //// 控制命令掩码
    //SYS_CTRLCMD_CTRLMASK = 0xFF,
    //// 禁用命令
    //SYS_CTRLCMD_DISABLE = 0x00,
    //// 启用命令
    //SYS_CTRLCMD_ENABLE,
    //// 停止命令
    //SYS_CTRLCMD_STOP = 0x01,
    //// 重置命令
    //SYS_CTRLCMD_RESET,
    //// 运行命令
    //SYS_CTRLCMD_RUN = 0x05,
    //// 暂停命令
    //SYS_CTRLCMD_PAUSE = 0x09,
    //// 急停命令
    //SYS_CTRLCMD_ESTOP = 0x10

    EnableAbove = 1,

    EnableBelow = 0,

    ResetError = 2,

    Run = 4,

    Pause = 8,

    EmergencyStop = 16
}