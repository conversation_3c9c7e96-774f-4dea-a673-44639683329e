using ModularAT.Localization.Resources;
﻿using System;
using System.Collections.Generic;

namespace ModularAT.Driver.Controller;

public class ControllerConst
{
    /// <summary>
    ///     示波器设置参数列表
    ///     分组，参数名，索引值
    /// </summary>
    public static List<Tuple<string, ushort>> TranCtlTypes =>
    [
        new(Lang.ControllerConst_Upper_enable, (ushort)TranCtrlCmdEnum.EnableUp),
        new(Lang.ControllerConst_Lower_enable, (ushort)TranCtrlCmdEnum.EnableDown),
        new(Lang.ControllerConst_Stop, (ushort)TranCtrlCmdEnum.Stop),
        new(Lang.ControllerConst_Reset, (ushort)TranCtrlCmdEnum.Reset),
        new(Lang.ControllerConst_Set_zero_point, (ushort)TranCtrlCmdEnum.SetZero),
        // new(Lang.ControllerConst_Forward_jog, (ushort)TranCtrlCmdEnum.JogForward),
        // new(Lang.ControllerConst_Backward_jog, (ushort)TranCtrlCmdEnum.JogBackward),
        new(Lang.ControllerConst_Absolute_movement, (ushort)TranCtrlCmdEnum.AbsoluteMovement),
        new(Lang.ControllerConst_Relative_movement, (ushort)TranCtrlCmdEnum.RelativeMovement),
        new(Lang.ControllerConst_Workstation_movement, (ushort)TranCtrlCmdEnum.StationMovement),
    ];


    public static List<Tuple<string, SysCtrlCmdEnum>> SysCtrlCmds
    {
        get
        {
            field = new List<Tuple<string, SysCtrlCmdEnum>>
            {
                new(Lang.SysCtrlCmdEnum_Upper_enable, SysCtrlCmdEnum.EnableAbove),
                new(Lang.SysCtrlCmdEnum_Lower_enable, SysCtrlCmdEnum.EnableBelow),
                new(Lang.SysCtrlCmdEnum_Error_reset, SysCtrlCmdEnum.ResetError),
                new(Lang.SysCtrlCmdEnum_Run, SysCtrlCmdEnum.Run),
                new(Lang.SysCtrlCmdEnum_Pause, SysCtrlCmdEnum.Pause),
                new(Lang.SysCtrlCmdEnum_Emergency_stop, SysCtrlCmdEnum.EmergencyStop)
            };

            return field;
        }
    }
}