using System;
using System.Linq;
using ModularAT.Common.Log;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller.Adapter;

/// <summary>
/// 控制器TCP响应类，实现了IFixedHeaderRequestInfo接口，用于处理特定格式的TCP响应数据。
/// </summary>
internal class ControllerTcpResponse : IFixedHeaderRequestInfo
{
    #region Constructor

    private readonly Action m_actionForReset;

    /// <summary>
    /// 初始化ControllerTcpResponse实例。
    /// </summary>
    /// <param name="action">用于重置的动作。</param>
    public ControllerTcpResponse(Action action)
    {
        m_actionForReset = action;
    }

    #endregion

    #region Properties

    /// <summary>
    /// 通信协议，默认为16#AA固定值，数据类型为DINT(4字节)
    /// </summary>
    public int Protocol { get; private set; } = 0xAA;

    /// <summary>
    /// 预留，默认为0，数据类型为UDINT(4字节)
    /// </summary>
    public uint Reserve { get; private set; }

    /// <summary>
    /// 命令类型，数据类型为UINT(2字节)
    /// </summary>
    public uint CmdType { get; set; }

    /// <summary>
    /// 数据大小，是Data数据所占的字节大小，数据类型为DINT(4字节)
    /// </summary>
    public int BodyLength { get; set; }

    /// <summary>
    /// 根据CmdType发送对应的数据（最大为1008个字节）
    /// </summary>
    public byte[] Data { get; set; }

    /// <summary>
    /// 包对象，用于发送数据
    /// </summary>
    public ControllerPackageBase Package { get; set; }

    #endregion


    #region Methods

    /// <summary>
    /// 解析响应体数据。
    /// </summary>
    /// <param name="body">响应体数据。</param>
    /// <returns>如果解析成功返回true，否则返回false。</returns>
    public bool OnParsingBody(byte[] body)
    {
        if (body.Length == BodyLength)
        {
            Data = body;
            return true;
        }

        LogHelper.GetInstance<ControllerTcpResponse>()
            .Warn(
                $"OnParsingBody: 数据长度不匹配！ body:{body.ByBytesToHexString(" ")}, Length数据位:{BodyLength}, 实际长度:{body.Length}");
        m_actionForReset.Invoke();
        return false;
    }

    /// <summary>
    /// 解析响应头数据。
    /// </summary>
    /// <param name="header">响应头数据。</param>
    /// <returns>如果解析成功返回true，否则返回false。</returns>
    public bool OnParsingHeader(byte[] header)
    {
        if (header.Length < 16 || header.First() != Protocol) //包头总字节数
        {
            LogHelper.GetInstance<ControllerTcpResponse>()
                .Warn($"OnParsingHeader: 包头解析失败！header:{header.ByBytesToHexString(" ")}");
            m_actionForReset.Invoke();
            return false;
        }

        using (var datas = new ByteBlock(header, 16))
        {
            Protocol = datas.ReadInt32();
            Reserve = datas.ReadUInt32();
            CmdType = datas.ReadUInt32();
            BodyLength = datas.ReadInt32();
        }

        return true;
    }

    #endregion
}
