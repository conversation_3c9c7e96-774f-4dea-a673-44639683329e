using TouchSocket.Core;

namespace ModularAT.Driver.Controller.Adapter;

/// <summary>
/// 控制器固定头部数据处理适配器类，用于处理特定格式的TCP响应数据。
/// </summary>
internal class ControllerFixedHeaderDataHandlingAdapter : CustomFixedHeaderDataHandlingAdapter<ControllerTcpResponse>
{
    /// <summary>
    /// 头部长度。
    /// </summary>
    public override int HeaderLength => 16;

    /// <summary>
    /// 是否可以发送请求信息。
    /// </summary>
    public override bool CanSendRequestInfo => true;

    /// <summary>
    /// 是否可以拼接发送。
    /// </summary>
    public override bool CanSplicingSend => true;

    /// <summary>
    /// 获取一个新的ControllerTcpResponse实例。
    /// </summary>
    /// <returns>一个新的ControllerTcpResponse实例。</returns>
    protected override ControllerTcpResponse GetInstance()
    {
        return new ControllerTcpResponse(Reset);
    }
}
