using TouchSocket.Core;

namespace ModularAT.Driver.Controller.Adapter;

public class ControllerTcpRequest : IRequestInfoBuilder
{
    #region Constructor

    public ControllerTcpRequest(ControllerPackageBase package = null)
    {
        Package = package;
    }

    #endregion

    #region Variables

    public int MaxLength => 1024;

    #endregion

    #region Methods

    /// <summary>
    ///     生成指令,用于发送
    /// </summary>
    /// <param name="byteBlock">指令</param>
    public void Build(ByteBlock byteBlock)
    {
        //调用顺序不能修改
        byteBlock.Write(Protocol);
        byteBlock.Write(Reserve);
        if (Package != null) //通过Package对象发送数据
        {
            byteBlock.Write(Package.GetCmdType());
            byteBlock.Write(Package.GetDataSize());
            //byteBlock.WritePackage(Package);
            Package.Package(byteBlock); //拼接Data部分
        }
        else if (Data != null) //通过Data数组发送数据
        {
            byteBlock.Write(CmdType);
            byteBlock.Write(Data.Length);
            byteBlock.Write(Data);
        }
        else //发送没有Data的包
        {
            byteBlock.Write(CmdType);
            byteBlock.Write(BodyLength);
        } //只有Data部分的包数据，需要手动传BodyLength

        ByteArray = byteBlock.ToArray();
    }

    #endregion

    #region Properties

    /// <summary>
    ///     通信协议，默认为16#AA固定值，数据类型为DINT(4字节)
    /// </summary>
    public int Protocol { get; } = 0xAA;

    /// <summary>
    ///     预留，默认为0，数据类型为UDINT(4字节)
    /// </summary>
    public uint Reserve { get; } = 0;

    /// <summary>
    ///     命令类型，数据类型为UINT(2字节)
    ///     Codesys会补齐2字节，所以改成uint
    /// </summary>
    public uint CmdType { get; set; }

    /// <summary>
    ///     数据大小，是Data数据所占的字节大小，数据类型为DINT(4字节)
    /// </summary>
    public int BodyLength { get; set; }

    /// <summary>
    ///     根据CmdType发送对应的数据（最大为1008个字节）
    /// </summary>
    public byte[] Data { get; set; }

    /// <summary>
    ///     包对象，用于发送数据
    /// </summary>
    public ControllerPackageBase Package { get; }

    /// <summary>
    ///     构建后的字节数组
    /// </summary>
    public byte[] ByteArray { get; private set; }

    #endregion
}