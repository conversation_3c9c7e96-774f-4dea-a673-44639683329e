<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <OutputType>Library</OutputType>
        <LangVersion>preview</LangVersion>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    </PropertyGroup>
    <PropertyGroup>
        <StartupObject />
    </PropertyGroup>
    <ItemGroup>
        <Compile Remove="Enum\OnlineConfigCmdEnum.cs" />
        <Compile Remove="Common\ControllerPackageMapper.cs" />
        <Compile Remove="Struct\OnlineConfig\SysCfgInfo.cs" />
    </ItemGroup>
    <ItemGroup>
        <None Remove="App.config" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper">
            <Version>10.1.1</Version>
        </PackageReference>
        <PackageReference Include="log4net">
            <Version>3.1.0</Version>
        </PackageReference>
        <PackageReference Include="MiniExcel" Version="1.41.3" />
        <PackageReference Include="TouchSocket">
            <Version>2.0.18</Version>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ModularAT.Common\ModularAT.Common.csproj" />
        <ProjectReference Include="..\ModularAT.Entity\ModularAT.Entity.csproj" />
        <ProjectReference Include="..\ModularAT.Service.Setting\ModularAT.Service.Setting.csproj" />
    </ItemGroup>
</Project>