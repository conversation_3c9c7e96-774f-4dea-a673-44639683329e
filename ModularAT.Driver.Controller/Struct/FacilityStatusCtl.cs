namespace ModularAT.Driver.Controller;

public struct FacilityStatusCtl
{
    public bool Initialize { get; set; }
    public bool Run { get; set; }
    public bool Stop { get; set; }
    public bool Reset { get; set; }
    public bool EmergencyStop { get; set; }
    public bool Manual { get; set; }

    public ushort ToCmd()
    {
        ushort command = 0;

        if (Initialize) command |= (ushort)FacllityStatuCtlEnum.Initialize;
        if (Run) command |= (ushort)FacllityStatuCtlEnum.Run;
        if (Stop) command |= (ushort)FacllityStatuCtlEnum.Stop;
        if (Reset) command |= (ushort)FacllityStatuCtlEnum.Reset;
        if (EmergencyStop) command |= (ushort)FacllityStatuCtlEnum.EmergencyStop;
        if (Manual) command |= (ushort)FacllityStatuCtlEnum.Manual;

        return command;
    }
}