using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct RoAxisCtrlCmd
{
    public ushort uServoCtrlCmd; // 控制命令
    public ushort uiStationID; // 目标工位
    public int iTranID; // 接驳ID
    
    public double lrSetVel; // 设置速度
    public double lrSetAcc; // 设置加速度
    public double lrSetDec; // 设置减速度
    public double lrTarPos; // 目标位置
}
