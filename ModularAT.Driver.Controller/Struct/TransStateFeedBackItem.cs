using System;
using System.Runtime.InteropServices;
using ModularAT.Entity.Controller;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct TransStateFeedBackItem : ITransStateFeedBack
{
    public short ILineBodySegID { get; set; }

    public short ILeftConnectedObjectID { get; set; }

    public short IRightConnectedObjectID { get; set; }
}