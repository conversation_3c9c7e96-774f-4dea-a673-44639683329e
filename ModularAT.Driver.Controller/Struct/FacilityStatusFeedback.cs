namespace ModularAT.Driver.Controller;

/// <summary>
/// 设施状态反馈结构体，用于表示设施的各种状态。
/// </summary>
public struct FacilityStatusFeedback
{
    /// <summary>
    /// 是否正在初始化。
    /// </summary>
    public bool IsInitializing { get; set; }

    /// <summary>
    /// 是否正在运行。
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// 是否已停止。
    /// </summary>
    public bool IsStop { get; set; }

    /// <summary>
    /// 是否正在重置。
    /// </summary>
    public bool IsResetting { get; set; }

    /// <summary>
    /// 是否正在紧急停止。
    /// </summary>
    public bool IsEmergencyStopping { get; set; }

    /// <summary>
    /// 是否处于错误状态。
    /// </summary>
    public bool IsError { get; set; }

    /// <summary>
    /// 是否正在维护。
    /// </summary>
    public bool IsMaintenance { get; set; }

    /// <summary>
    /// 是否处于正常状态。
    /// </summary>
    public bool IsNormal { get; set; }

    /// <summary>
    /// 是否处于手动模式。
    /// </summary>
    public bool IsManual { get; set; }
}
