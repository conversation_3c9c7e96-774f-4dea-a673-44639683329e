using System;
using System.Runtime.InteropServices;
using ModularAT.Entity.Controller;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct AxisFeedBackItem : IAxisFeedBack
{
    public short IAxisCurObject { get; set; }

    public short IAxisCurObjectID { get; set; }

    public uint UiAxisDrvErrCode { get; set; }

    public uint UiAxisMotionErrCode { get; set; }

    public int DiAxisCurPos { get; set; }

    public int DiAxisCurVel { get; set; }

    public uint UdiAxisRunState { get; set; }
}