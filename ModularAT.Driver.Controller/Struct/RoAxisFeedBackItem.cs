using System;
using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct RoAxisFeedBackItem
{
    /// <summary>
    ///     轴的使能状态
    /// </summary>
    public bool BPoweron { get; set; }

    /// <summary>
    ///     运行状态
    /// </summary>
    public bool BRunning { get; set; }

    /// <summary>
    ///     回零完成
    /// </summary>
    public bool BHomeDone { get; set; }

    /// <summary>
    ///     预留，占位
    /// </summary>
    public bool BReserved { get; set; }

    /// <summary>
    ///     轴的错误码
    /// </summary>
    public uint DwAxisErrorID { get; set; }

    /// <summary>
    ///     轴的实际速度
    /// </summary>
    public double LrActVelocity { get; set; }

    /// <summary>
    ///     轴的实际位置
    /// </summary>
    public double LrActPosition { get; set; }
}