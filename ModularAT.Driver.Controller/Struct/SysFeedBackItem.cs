using System;
using System.Runtime.InteropServices;
using ModularAT.Entity.Controller;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct SysFeedBackItem : ISysFeedBack
{
    public short ISysErrorAxisID { get; set; }

    public short ISysErrorDrvID { get; set; }

    public uint UdiSysDrvErrorCode { get; set; }
    public string SSysDrvErrName { get; set; }

    public uint UdiSysErrorCode { get; set; }


    public uint UdiSysState { get; set; }
}