using System;
using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct OnlinePIDCfgPara
{
    /// <summary>
    /// 移动器ID
    /// </summary>
    public short iMoverID;
    
    /// <summary>
    /// 比例系数
    /// </summary>
    public double lrKp;
    
    /// <summary>
    /// 积分系数
    /// </summary>
    public double lrKi;
    
    /// <summary>
    /// 微分系数
    /// </summary>
    public double lrKd;
    
    /// <summary>
    /// 前馈系数
    /// </summary>
    public double lrKf;
}
