using System;
using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 表示在线线体配置参数的结构体
/// </summary>
[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct OnlineLineBodyCfgPara
{
    /// <summary>
    /// 线体段类型
    /// </summary>
    public short iLineBodySegType;

    /// <summary>
    /// 线体段方向
    /// </summary>
    public short iLineBodySegDir;

    /// <summary>
    /// 线体段驱动电机数量
    /// </summary>
    public short iLineBodySegDrvMotorNums;

    /// <summary>
    /// 线体段初始移动器数量
    /// </summary>
    public short iLineBodySegInitMoverNums;

    /// <summary>
    /// 负限位长度
    /// </summary>
    public double lrNegativeLimitLen;

    /// <summary>
    /// 正限位长度
    /// </summary>
    public double lrPositiveLimitLen;
}
