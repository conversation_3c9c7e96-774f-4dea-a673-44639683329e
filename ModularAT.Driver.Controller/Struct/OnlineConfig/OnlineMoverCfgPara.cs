using System;
using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct OnlineMoverCfgPara
{
    // 轴类型
    public short iAxisType;
    // 移动器长度
    public double lrMoverLen;
    // 载体左侧长度
    public double lrCarrierLeftLen;
    // 载体右侧长度
    public double lrCarrierRightLen;
    // 移动器安全长度
    public double lrMoverSafetyLen;
}
