using System;
using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct OnlineMotorCfgPara
{
    /// <summary>
    /// 电机类型
    /// </summary>
    public short iMotorType;


    /// <summary>
    /// 电机数量
    /// </summary>
    public short iMotorNums;


    /// <summary>
    /// 电机长度
    /// </summary>
    public short iMotorLen;


    /// <summary>
    /// 编码器类型
    /// </summary>
    public short iEncoderType;
}