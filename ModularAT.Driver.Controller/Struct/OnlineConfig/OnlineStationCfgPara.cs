using System.Runtime.InteropServices;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct OnlineStationCfgPara
{
    /// <summary>
    /// 站点ID
    /// </summary>
    public short IStationID;
    
    /// <summary>
    /// 所在对象类型
    /// </summary>
    public short IOnObj;
    
    /// <summary>
    /// 所在对象ID
    /// </summary>
    public short IOnObjID;
    
    /// <summary>
    /// 站点位置
    /// </summary>
    public double LrStationPos;
    
    /// <summary>
    /// 站点使能
    /// </summary>
    public short IStnEnable;
}
