using System;
using System.Runtime.InteropServices;

namespace ModularAT.Driver.Controller;

[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct OnlineSlaveNodeCfgPara
{
    /// <summary>
    /// 从节点类型
    /// </summary>
    public short iSlaveNodeType;
    
    /// <summary>
    /// 从节点数量
    /// </summary>
    public short iSlaveNodeNums;
    
    /// <summary>
    /// 从节点驱动数量
    /// </summary>
    public short iSlaveNodeDrvNums;
}
