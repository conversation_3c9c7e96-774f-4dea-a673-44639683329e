using System;
using System.Runtime.InteropServices;
using TouchSocket.Core;

namespace ModularAT.Driver.Controller;

/// <summary>
/// 轴偏移配置参数
/// </summary>
[Serializable]
[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct OnlineAxisOffsetCfgPara
{
    /// <summary>
    /// 动子D
    /// </summary>
    public short iMoverID;

    /// <summary>
    /// 固定误差偏移量
    /// </summary>
    public double lrFixedErrOffset;
}