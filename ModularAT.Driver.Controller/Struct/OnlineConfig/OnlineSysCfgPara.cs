using System;
using System.Runtime.InteropServices;
using ModularAT.Entity.Controller;

namespace ModularAT.Driver.Controller;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct OnlineSysCfgPara
{
    // 线体段数组成
    public short ISysLineBodySegNum;

    // 系统动子数量
    public short ISysAxisNums;

    // 系统工位数量
    public short ISysStationNums;

    // 系统从站节点数量
    public short ISysSlaveNodeNums;

    // 系统驱动电机数量
    public short ISysDrvMotorNums;

    // 系统从站驱动控制模式
    public short ISysSlaveNodeDrvMode;

    // 磁板类型 (0: 288, 1: 144, 2: 240)
    public short ISysMagneticPlateType;

    // 系统反馈模式 (0: 过程数据, 1: 一问一答)
    public short ISysFeedbackMode;
}