using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;
using ModularAT.Localization.Resources;

namespace ModularAT.Localization;

public class LocalizationService : INotifyPropertyChanged
{
    public event PropertyChangedEventHandler PropertyChanged;

    public static LocalizationService Current { get; } = new LocalizationService();

    public Lang LanResources { get; } = new();

    protected virtual void RaisePropertyChanged([CallerMemberName] string propertyName = null)
    {
        var handler = this.PropertyChanged;
        handler?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    internal void ChangedCulture(string? name)
    {
        if (string.IsNullOrEmpty(name)) return;
        Lang.Culture = CultureInfo.GetCultureInfo(name);
        Thread.CurrentThread.CurrentCulture = Lang.Culture;
        Thread.CurrentThread.CurrentUICulture = Lang.Culture;
        this.RaisePropertyChanged(nameof(LanResources));
    }
}