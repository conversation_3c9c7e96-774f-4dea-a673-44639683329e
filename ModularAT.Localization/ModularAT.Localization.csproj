<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
		<LangVersion>preview</LangVersion>
        <TargetFramework>netframework48</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <Compile Update="Resources\Lang.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Lang.resx</DependentUpon>
      </Compile>
      <Compile Remove="LangKeys.cs" />
      <Compile Update="Resources\DbLang.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>DbLang.resx</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Resources\Lang.resx">
        <Generator>PublicResXFileCodeGenerator</Generator>
        <LastGenOutput>Lang.Designer.cs</LastGenOutput>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.Designer.cs</LastGenOutput>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.de-DE.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.en-US.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.es-ES.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.fr-FR.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.it-IT.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.ja-JP.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.ko-KR.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.pt-BR.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.pt-PT.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.ru-RU.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.vi-VN.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.zh-hans.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\DbLang.zh-hant.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>DbLang.de-DE.Designer.cs</LastGenOutput>
        <DependentUpon>DbLang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.de-DE.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.en-US.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.es-ES.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.fr-FR.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.it-IT.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.ja-JP.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.ko-KR.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.pt-BR.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.pt-PT.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.ru-RU.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.vi-VN.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.zh-hans.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
      <EmbeddedResource Update="Resources\Lang.zh-hant.resx">
        <DependentUpon>Lang.resx</DependentUpon>
      </EmbeddedResource>
    </ItemGroup>

</Project>
