using System.ComponentModel;
using ModularAT.Localization.Resources;
﻿using System.Globalization;
using System.Resources;

namespace ModularAT.Localization;

/// <summary>
///     Class provides variables/methods to manage localization.
/// </summary>
public class LocalizationManager
{
    /// <summary>
    ///     Constant for the default culture code.
    /// </summary>
    private const string DefaultCultureCode = "en-US";

    /// <summary>
    ///     Constant with the path to the flag images.
    /// </summary>
    private const string BaseFlagImageUri =
        @"pack://application:,,,/NETworkManager.Localization;component/Resources/Flags/";

    /// <summary>
    ///     Variable for the instance of the class.
    /// </summary>
    private static LocalizationManager _instance;

    /// <summary>
    ///     Variable for the dbLang.
    /// </summary>
    private readonly ResourceManager _dbLangResourceManager = new ComponentResourceManager(typeof(Resources.DbLang));

    /// <summary>
    ///     Create an instance and load the language based on the culture code.
    /// </summary>
    /// <param name="cultureCode">Culture code (default is "en-US"). See also <see cref="LocalizationInfo.Code" />.</param>
    private LocalizationManager(string cultureCode = DefaultCultureCode)
    {
        if (string.IsNullOrEmpty(cultureCode))
            cultureCode = CultureInfo.CurrentCulture.Name;

        var info = GetLocalizationInfoBasedOnCode(cultureCode) ?? List.First();

        if (info.Code != List.First().Code)
        {
            Change(info);
        }
        else
        {
            Current = info;
            Culture = new CultureInfo(info.Code);
        }
    }

    /// <summary>
    ///     List with all <see cref="LocalizationInfo" />s.
    /// </summary>
    public static List<LocalizationInfo> List => new()
    {
        new LocalizationInfo("Chinese (China)", Lang.LocalizationManager_Simplified_chinese, GetImageUri("zh-CN"),
            "zh-CN"),
        new LocalizationInfo("Chinese (TaiGangAo)", Lang.LocalizationManager_Traditional_chinese, GetImageUri("zh-TW"),
            "zh-TW"),
        new LocalizationInfo("English", "English", GetImageUri("en-US"), "en-US"),
        new LocalizationInfo("Japanese (Japan)", Lang.LocalizationManager_Japanese, GetImageUri("ja-JP"), "ja-JP"),
        new LocalizationInfo("Korean (Korea)", "한국어", GetImageUri("ko-KR"), "ko-KR"),
        new LocalizationInfo("Vietnamese (Vietnam)", "Tiếng Việt", GetImageUri("vi-VN"), "vi-VN"),
        new LocalizationInfo("Italian (Italy)", "Italiano", GetImageUri("it-IT"), "it-IT"),
        new LocalizationInfo("Russian (Russia)", "Русский", GetImageUri("ru-RU"), "ru-RU"),
        new LocalizationInfo("German (Germany)", "Deutsch", GetImageUri("de-DE"), "de-DE"),
        new LocalizationInfo("French (France)", "Français", GetImageUri("fr-FR"), "fr-FR"),
        new LocalizationInfo("Spanish (Spain)", "Español", GetImageUri("es-ES"), "es-ES"),
        new LocalizationInfo("Portuguese (Brazil)", "português brasileiro", GetImageUri("pt-BR"), "pt-BR"),
        new LocalizationInfo("Portuguese (Portugal)", "português português", GetImageUri("pt-PT"), "pt-PT"),

        // new LocalizationInfo("Dutch (Netherlands)", "Nederlands", GetImageUri("nl-NL"), "nl-NL"),
        // new LocalizationInfo("Hungarian (Hungary)", "Magyar", GetImageUri("hu-HU"), "hu-HU"),
        // new LocalizationInfo("Polish (Poland)", "Język polski", GetImageUri("pl-PL"), "pl-PL"),
        // new LocalizationInfo("Slovenian (Slovenia)", "slovenski jezik", GetImageUri("sl-SI"), "sl-SI"),
        // new LocalizationInfo("Czech (Czech Republic)", "Čeština", GetImageUri("cs-CZ"), "cs-CZ"),
    };

    /// <summary>
    ///     Variable with the currently used <see cref="LocalizationInfo" />.
    /// </summary>
    public static LocalizationInfo Current { get; private set; } = new();

    /// <summary>
    ///     Variable with the currently used <see cref="CultureInfo" />.
    /// </summary>
    public CultureInfo Culture { get; private set; }

    /// <summary>
    ///     Returns the current instance of the class.
    ///     The language can be set on creation (first call), by passing a culture code (like "en-US") as parameter.
    ///     Use <see cref="Change(LocalizationInfo)" /> to change it later.
    /// </summary>
    /// <param name="cultureCode">Culture code (default is "en-US"). See also <see cref="LocalizationInfo.Code" />.</param>
    /// <returns>Instance of the class.</returns>
    public static LocalizationManager GetInstance(string cultureCode = DefaultCultureCode)
    {
        _instance ??= new LocalizationManager();

        return _instance;
    }

    /// <summary>
    ///     Method to build the uri for a flag image based on the culture code.
    /// </summary>
    /// <param name="cultureCode">Culture code like "en-US".</param>
    /// <returns>Uri of the flag image.</returns>
    private static Uri GetImageUri(string cultureCode)
    {
        return new Uri($"{BaseFlagImageUri}{cultureCode}.png");
    }

    /// <summary>
    ///     Method to get the <see cref="LocalizationInfo" /> based on the culture code.
    /// </summary>
    /// <param name="cultureCode"></param>
    /// <returns>Return the <see cref="LocalizationInfo" /> or null if not found.</returns>
    private static LocalizationInfo GetLocalizationInfoBasedOnCode(string cultureCode)
    {
        return List.FirstOrDefault(x => x.Code == cultureCode);
    }

    /// <summary>
    ///     Method to change the language.
    /// </summary>
    /// <param name="cultureCode"></param>
    public void Change(string? cultureCode)
    {
        if (string.IsNullOrEmpty(cultureCode)) return;
        Current = GetLocalizationInfoBasedOnCode(cultureCode);
        Culture = new CultureInfo(Current.Code);
        LocalizationService.Current.ChangedCulture(Current.Code);
    }

    private void Change(LocalizationInfo info)
    {
        Current = info;
        Culture = new CultureInfo(info.Code);
        LocalizationService.Current.ChangedCulture(info.Code);
    }

    /// <summary>
    /// 获取数据库字段的本地化字符串
    /// </summary>
    /// <param name="name">字段名</param>
    /// <returns></returns>
    public string? GetDbLocalizedString(string name)
    {
        return _dbLangResourceManager.GetString(name, Culture);
    }
}