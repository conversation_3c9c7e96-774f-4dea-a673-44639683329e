<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>在线配置</value>
        <comment>在线配置</comment>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>配置生成</value>
        <comment>配置生成</comment>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>操作日志</value>
        <comment>操作日志</comment>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>数据追溯</value>
        <comment>数据追溯</comment>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>采集</value>
        <comment>采集</comment>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>故障记录清除</value>
        <comment>故障记录清除</comment>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>错误复位</value>
        <comment>错误复位</comment>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>恢复默认参数</value>
        <comment>恢复默认参数</comment>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>全部写入</value>
        <comment>全部写入</comment>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>选择写入</value>
        <comment>选择写入</comment>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>执行</value>
        <comment>执行</comment>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>执行</value>
        <comment>执行</comment>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>停止</value>
        <comment>停止</comment>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>执行</value>
        <comment>执行</comment>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>权限分配</value>
        <comment>权限分配</comment>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>菜单</value>
        <comment>菜单</comment>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>用户</value>
        <comment>用户</comment>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>角色</value>
        <comment>角色</comment>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>示波器</value>
        <comment>示波器</comment>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>伺服配置</value>
        <comment>伺服配置</comment>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>系统控制</value>
        <comment>系统控制</comment>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>接驳状态</value>
        <comment>接驳状态</comment>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>轴控制</value>
        <comment>轴控制</comment>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>系统总成</value>
        <comment>系统总成</comment>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>控制器连接</value>
        <comment>控制器连接</comment>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>驱动器连接</value>
        <comment>驱动器连接</comment>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>基本设置</value>
        <comment>基本设置</comment>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>驱动器</value>
        <comment>驱动器</comment>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>控制器</value>
        <comment>控制器</comment>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>设备连接</value>
        <comment>设备连接</comment>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>电机线电阻(mΩ)</value>
        <comment>电机线电阻(mΩ)</comment>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>电机线电感(mH)</value>
        <comment>电机线电感(mH)</comment>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>电机额定电流(Arms)</value>
        <comment>电机额定电流(Arms)</comment>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>电机额定力矩(N)</value>
        <comment>电机额定力矩(N)</comment>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>电机峰值电流(Arms)</value>
        <comment>电机峰值电流(Arms)</comment>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>电机力矩常数(N/Arms)</value>
        <comment>电机力矩常数(N/Arms)</comment>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>电机反电动势系数(V(pk)/m/s)</value>
        <comment>电机反电动势系数(V(pk)/m/s)</comment>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>电机极对N-N距(mm)</value>
        <comment>电机极对N-N距(mm)</comment>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>电机极对数</value>
        <comment>电机极对数</comment>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>电角度偏移(PosUnit)</value>
        <comment>电角度偏移(PosUnit)</comment>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>电机U相电流(A)</value>
        <comment>电机U相电流(A)</comment>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>电机V相电流(A)</value>
        <comment>电机V相电流(A)</comment>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>电机W相电流(A)</value>
        <comment>电机W相电流(A)</comment>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>母线电压(V)</value>
        <comment>母线电压(V)</comment>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>驱动器类型</value>
        <comment>驱动版本-芯片型号</comment>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>驱动版本-大版本迭代</value>
        <comment>驱动版本-大版本迭代</comment>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>驱动版本-功能迭代</value>
        <comment>驱动版本-功能迭代</comment>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>驱动版本-Bug迭代</value>
        <comment>驱动版本-Bug迭代</comment>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>驱动版本-调试发布(0-调试 1-发布)</value>
        <comment>驱动版本-调试发布(0-调试 1-发布)</comment>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>示波器控制</value>
        <comment>示波器控制</comment>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>示波器通道0</value>
        <comment>示波器通道0</comment>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>示波器通道1</value>
        <comment>示波器通道1</comment>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>示波器通道2</value>
        <comment>示波器通道2</comment>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>示波器通道3</value>
        <comment>示波器通道3</comment>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>示波器通道4</value>
        <comment>示波器通道4</comment>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>示波器通道5</value>
        <comment>示波器通道5</comment>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>示波器通道6</value>
        <comment>示波器通道6</comment>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>示波器通道7</value>
        <comment>示波器通道7</comment>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>编码器类型</value>
        <comment>编码器类型</comment>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>编码器分辨率</value>
        <comment>编码器分辨率</comment>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>编码器版本-大版本迭代</value>
        <comment>编码器版本-大版本迭代</comment>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>编码器版本-功能迭代</value>
        <comment>编码器版本-功能迭代</comment>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>编码器版本-Bug迭代</value>
        <comment>编码器版本-Bug迭代</comment>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>编码器版本-调试版本</value>
        <comment>编码器版本-调试版本</comment>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>编码器调试功能选择</value>
        <comment>编码器调试功能选择</comment>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>编码器0位置调试接口</value>
        <comment>编码器0位置调试接口</comment>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>编码器1位置调试接口</value>
        <comment>编码器1位置调试接口</comment>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>过流检测阈值(A)</value>
        <comment>过流检测阈值(A)</comment>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>过流检测判定时间(ms)</value>
        <comment>过流检测判定时间(ms)</comment>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>过载判定电流阈值</value>
        <comment>过载判定电流阈值</comment>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>过载峰值电流</value>
        <comment>过载峰值电流</comment>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>允许峰值电流持续时间(ms)</value>
        <comment>允许峰值电流持续时间(ms)</comment>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t增加补偿系数</value>
        <comment>I2t增加补偿系数</comment>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t减少补偿系数</value>
        <comment>I2t减少补偿系数</comment>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>电机堵转检测电流阈值(A)</value>
        <comment>电机堵转检测电流阈值(A)</comment>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>电机堵转判定时间(ms)</value>
        <comment>电机堵转判定时间(ms)</comment>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>电机堵转速度判定阈值(mm/s)</value>
        <comment>电机堵转速度判定阈值(mm/s)</comment>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS温度报警阈值(℃)</value>
        <comment>MOS温度报警阈值(℃)</comment>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>编码器通讯错误次数报警阈值</value>
        <comment>编码器通讯错误次数报警阈值</comment>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>电机失速检测阈值(mm/s)</value>
        <comment>电机失速检测阈值(mm/s)</comment>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>过压保护阈值(V)</value>
        <comment>过压保护阈值(V)</comment>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>欠压保护阈值(V)</value>
        <comment>欠压保护阈值(V)</comment>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>最新错误位置</value>
        <comment>最新错误位置</comment>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>开机时错误索引</value>
        <comment>开机时错误索引</comment>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>历史错误0</value>
        <comment>历史错误0</comment>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>历史错误1</value>
        <comment>历史错误1</comment>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>历史错误2</value>
        <comment>历史错误2</comment>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>历史错误3</value>
        <comment>历史错误3</comment>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>历史错误4</value>
        <comment>历史错误4</comment>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>历史错误5</value>
        <comment>历史错误5</comment>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>历史错误6</value>
        <comment>历史错误6</comment>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>历史错误7</value>
        <comment>历史错误7</comment>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>历史错误8</value>
        <comment>历史错误8</comment>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>历史错误9</value>
        <comment>历史错误9</comment>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>历史错误10</value>
        <comment>历史错误10</comment>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>历史错误11</value>
        <comment>历史错误11</comment>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>历史错误12</value>
        <comment>历史错误12</comment>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>历史错误13</value>
        <comment>历史错误13</comment>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>历史错误14</value>
        <comment>历史错误14</comment>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>历史错误15</value>
        <comment>历史错误15</comment>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>历史错误16</value>
        <comment>历史错误16</comment>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>历史错误17</value>
        <comment>历史错误17</comment>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>历史错误18</value>
        <comment>历史错误18</comment>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>历史错误19</value>
        <comment>历史错误19</comment>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>控制字</value>
        <comment>控制字</comment>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>状态字</value>
        <comment>状态字</comment>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>运行状态</value>
        <comment>运行状态</comment>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>实际状态</value>
        <comment>实际状态</comment>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>目标位置(PosUnit)</value>
        <comment>目标位置(PosUnit)</comment>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>实际位置(PosUnit)</value>
        <comment>实际位置(PosUnit)</comment>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>位置环比例系数((mm/s)/PosUnit)</value>
        <comment>位置环比例系数((mm/s)/PosUnit)</comment>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>位置环积分系数</value>
        <comment>位置环积分系数</comment>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>位置环微分系数</value>
        <comment>位置环微分系数</comment>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>位置指令低通滤波截止频率(Hz)</value>
        <comment>位置指令低通滤波截止频率(Hz)</comment>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>位置控制输出钳位UP(mm/s)</value>
        <comment>位置控制输出钳位UP(mm/s)</comment>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>位置控制输出钳位LOW(mm/s)</value>
        <comment>位置控制输出钳位LOW(mm/s)</comment>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>位置指令均值滤波器截止频率(Hz)</value>
        <comment>位置指令均值滤波器截止频率(Hz)</comment>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>目标速度(mm/s)</value>
        <comment>目标速度(mm/s)</comment>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>实际速度(mm/s)</value>
        <comment>实际速度(mm/s)</comment>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>速度环比例系数(A/(mm/s))</value>
        <comment>速度环比例系数(A/(mm/s))</comment>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>速度环积分系数(A/mm)</value>
        <comment>速度环积分系数(A/mm)</comment>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>速度环微分系数(A/(mm/s^2))</value>
        <comment>速度环微分系数(A/(mm/s^2))</comment>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>速度环抗积分饱和系数</value>
        <comment>速度环抗积分饱和系数</comment>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>速度前馈增益系数</value>
        <comment>速度前馈增益系数</comment>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>速度前馈低通滤波截止频率(Hz)</value>
        <comment>速度前馈低通滤波截止频率(Hz)</comment>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>速度反馈低通滤波截止频率(Hz)</value>
        <comment>速度反馈低通滤波截止频率(Hz)</comment>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>速度指令低通滤波截止频率(Hz)</value>
        <comment>速度指令低通滤波截止频率(Hz)</comment>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>速度控制输出钳位UP(A)</value>
        <comment>速度控制输出钳位UP(A)</comment>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>速度控制输出钳位LOW(A)</value>
        <comment>速度控制输出钳位LOW(A)</comment>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q轴电流目标值(A)</value>
        <comment>Q轴电流目标值(A)</comment>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D轴电流目标值(A)</value>
        <comment>D轴电流目标值(A)</comment>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q轴电流反馈值(A)</value>
        <comment>Q轴电流反馈值(A)</comment>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D轴电流反馈值(A)</value>
        <comment>D轴电流反馈值(A)</comment>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>电流环比例系数</value>
        <comment>电流环比例系数</comment>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>电流环积分系数</value>
        <comment>电流环积分系数</comment>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>电流环微分系数</value>
        <comment>电流环微分系数</comment>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D轴反电势补偿系数</value>
        <comment>D轴反电势补偿系数</comment>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q轴反电势补偿系数</value>
        <comment>Q轴反电势补偿系数</comment>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>永磁体反电势补偿系数</value>
        <comment>永磁体反电势补偿系数</comment>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>电流反馈低通滤波截止频率(Hz)</value>
        <comment>电流反馈低通滤波截止频率(Hz)</comment>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>电流指令低通滤波截止频率(Hz)</value>
        <comment>电流指令低通滤波截止频率(Hz)</comment>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>电流前馈增益系数</value>
        <comment>电流前馈增益系数</comment>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>电流前馈低通滤波截止频率(Hz)</value>
        <comment>电流前馈低通滤波截止频率(Hz)</comment>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>电流指令陷波滤波中心频率(Hz)</value>
        <comment>电流指令陷波滤波中心频率(Hz)</comment>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>电流指令陷波滤波带宽(Hz)</value>
        <comment>电流指令陷波滤波带宽(Hz)</comment>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>电流指令陷波滤波深度(dB)</value>
        <comment>电流指令陷波滤波深度(dB)</comment>
    </data>
</root>