<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Configuración en línea</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Generación de configuración</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Registro de operaciones</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Rastreo de datos</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Recopilación</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Eliminación de registros de fallas</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Reinicio de error</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Restaurar parámetros predeterminados</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Escribir todo</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Escribir selección</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Ejecutar</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Ejecutar</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Detener</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Ejecutar</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Asignación de permisos</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menú</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Usuario</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Rol</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Osciloscopio</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Configuración de servo</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Control del sistema</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Estado de conexión</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Control del eje</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Conjunto del sistema</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Conexión del controlador</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Conexión del controlador de dispositivos</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Configuración básica</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Controlador de dispositivos</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Controlador</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Conexión del dispositivo</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Resistencia del cable del motor (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Inductancia del cable del motor (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Corriente nominal del motor (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Par nominal del motor (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Corriente pico del motor (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Constante de par del motor (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Coeficiente de fuerza electromotriz inversa del motor (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Distancia entre polos N - N del motor (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Número de pares de polos del motor</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Desfase de ángulo eléctrico (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Corriente de fase U del motor (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Corriente de fase V del motor (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Corriente de fase W del motor (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Voltaje de la barra (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Versión del controlador de dispositivos - Modelo del chip</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Versión del controlador de dispositivos - Iteración de versión principal</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Versión del controlador de dispositivos - Iteración de función</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Versión del controlador de dispositivos - Iteración de corrección de errores</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Versión del controlador de dispositivos - Depuración/Liberación (0 - Depuración, 1 - Liberación)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Control del osciloscopio</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Canal 0 del osciloscopio</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Canal 1 del osciloscopio</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Canal 2 del osciloscopio</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Canal 3 del osciloscopio</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Canal 4 del osciloscopio</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Canal 5 del osciloscopio</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Canal 6 del osciloscopio</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Canal 7 del osciloscopio</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Tipo de codificador</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Resolución del codificador</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Versión del codificador - Iteración de versión principal</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Versión del codificador - Iteración de función</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Versión del codificador - Iteración de corrección de errores</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Versión del codificador - Versión de depuración</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Selección de funciones de depuración del codificador</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Interfaz de depuración de posición del codificador 0</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Interfaz de depuración de posición del codificador 1</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Umbral de detección de sobrecorriente (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Tiempo de determinación de detección de sobrecorriente (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Umbral de corriente de determinación de sobrecarga</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Corriente pico de sobrecarga</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Tiempo de duración de corriente pico permitido (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>Coeficiente de compensación de aumento de I2t</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>Coeficiente de compensación de disminución de I2t</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Umbral de corriente de detección de bloqueo del motor (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Tiempo de determinación de bloqueo del motor (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Umbral de velocidad de determinación de bloqueo del motor (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>Umbral de temperatura MOS de alerta (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Umbral de número de errores de comunicación del codificador de alerta</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Umbral de detección de deslizamiento del motor (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Umbral de protección contra sobrevoltaje (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Umbral de protección contra subvoltaje (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Ubicación del último error</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Índice de error al inicio</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Error histórico 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Error histórico 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Error histórico 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Error histórico 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Error histórico 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Error histórico 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Error histórico 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Error histórico 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Error histórico 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Error histórico 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Error histórico 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Error histórico 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Error histórico 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Error histórico 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Error histórico 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Error histórico 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Error histórico 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Error histórico 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Error histórico 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Error histórico 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Palabra de control</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Palabra de estado</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Estado de ejecución</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Estado real</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Posición objetivo (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Posición real (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Coeficiente de proporcionalidad del bucle de posición ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Coeficiente de integración del bucle de posición</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Coeficiente de diferenciación del bucle de posición</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de la orden de posición (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Límite superior de salida de control de posición (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Límite inferior de salida de control de posición (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Frecuencia de corte del filtro de media de la orden de posición (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Velocidad objetivo (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Velocidad real (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Coeficiente de proporcionalidad del bucle de velocidad (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Coeficiente de integración del bucle de velocidad (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Coeficiente de diferenciación del bucle de velocidad (A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Coeficiente de antiesaturación de integración del bucle de velocidad</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Coeficiente de ganancia de anticipación de velocidad</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de anticipación de velocidad (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de retroalimentación de velocidad (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de la orden de velocidad (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Límite superior de salida de control de velocidad (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Límite inferior de salida de control de velocidad (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Valor objetivo de corriente del eje Q (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>Valor objetivo de corriente del eje D (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Valor de retroalimentación de corriente del eje Q (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>Valor de retroalimentación de corriente del eje D (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Coeficiente de proporcionalidad del bucle de corriente</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Coeficiente de integración del bucle de corriente</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Coeficiente de diferenciación del bucle de corriente</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>Coeficiente de compensación de fuerza electromotriz inversa del eje D</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Coeficiente de compensación de fuerza electromotriz inversa del eje Q</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Coeficiente de compensación de fuerza electromotriz inversa del imán permanente</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de retroalimentación de corriente (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de la orden de corriente (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Coeficiente de ganancia de anticipación de corriente</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Frecuencia de corte del filtro pasa - bajo de anticipación de corriente (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Frecuencia central del filtro de noxa de la orden de corriente (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Anchura de banda del filtro de noxa de la orden de corriente (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Profundidad del filtro de noxa de la orden de corriente (dB)</value>
    </data>
</root>