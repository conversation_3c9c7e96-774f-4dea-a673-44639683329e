<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Онлайн-конфигурация</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Генерация конфигурации</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Журнал операций</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Трассировка данных</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Сбор</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Очистка записей о неисправностях</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Сброс ошибки</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Восстановление параметров по умолчанию</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Запись всех</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Выборочная запись</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Выполнить</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Выполнить</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Остановить</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Выполнить</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Назначение прав</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Меню</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Пользователь</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Роль</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Осциллограф</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Конфигурация сервопривода</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Системный контроль</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Состояние соединения</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Управление осью</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Системный сбор</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Соединение контроллера</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Соединение драйвера</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Базовые настройки</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Драйвер</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Контроллер</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Подключение устройства</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Сопротивление мотора (мОм)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Индуктивность мотора (мГн)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Номинальный ток мотора (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Номинальный момент мотора (Н)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Пиковый ток мотора (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Моментный коэффициент мотора (Н/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Коэффициент обратной ЭДС мотора (V(pk)/м/с)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Расстояние между полюсами N - N мотора (мм)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Количество пар полюсов мотора</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Смещение электрического угла (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Ток фазы U мотора (А)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Ток фазы V мотора (А)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Ток фазы W мотора (А)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Напряжение шины (В)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Версия драйвера - Модель чипа</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Версия драйвера - Основная итерация версии</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Версия драйвера - Функциональная итерация</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Версия драйвера - Итерация исправления ошибок</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Версия драйвера - Отладка/Релиз (0 - Отладка, 1 - Релиз)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Управление осциллографом</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Канал осциллографа 0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Канал осциллографа 1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Канал осциллографа 2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Канал осциллографа 3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Канал осциллографа 4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Канал осциллографа 5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Канал осциллографа 6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Канал осциллографа 7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Тип энкодера</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Разрешение энкодера</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Версия энкодера - Основная итерация версии</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Версия энкодера - Функциональная итерация</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Версия энкодера - Итерация исправления ошибок</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Версия энкодера - Отладочная версия</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Выбор отладочных функций энкодера</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Интерфейс отладки положения энкодера 0</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Интерфейс отладки положения энкодера 1</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Порог обнаружения избыточного тока (А)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Время определения обнаружения избыточного тока (мс)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Порог тока для определения перегрузки</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Пиковый ток перегрузки</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Допустимое время持续ления пикового тока (мс)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>Коэффициент компенсации увеличения I2t</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>Коэффициент компенсации уменьшения I2t</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Порог тока для обнаружения блокировки мотора (А)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Время определения блокировки мотора (мс)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Порог скорости для определения блокировки мотора (мм/с)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>Порог температуры MOS для оповещения (°С)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Порог количества ошибок связи энкодера для оповещения</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Порог обнаружения потери скорости мотора (мм/с)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Порог защиты от перегрузки напряжения (В)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Порог защиты от недостатка напряжения (В)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Последнее положение ошибки</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Индекс ошибки при запуске</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>История ошибки 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>История ошибки 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>История ошибки 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>История ошибки 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>История ошибки 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>История ошибки 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>История ошибки 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>История ошибки 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>История ошибки 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>История ошибки 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>История ошибки 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>История ошибки 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>История ошибки 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>История ошибки 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>История ошибки 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>История ошибки 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>История ошибки 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>История ошибки 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>История ошибки 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>История ошибки 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Команда управления</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Слово состояния</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Состояние выполнения</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Фактическое состояние</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Целевая позиция (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Фактическая позиция (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Коэффициент пропорциональности положения ((мм/с)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Коэффициент интеграла положения</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Коэффициент дифференциала положения</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра команды положения (Гц)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Предел UP выходного сигнала управления положением (мм/с)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Предел LOW выходного сигнала управления положением (мм/с)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Граница частоты среднеквадратичного фильтра команды положения (Гц)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Целевая скорость (мм/с)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Фактическая скорость (мм/с)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Коэффициент пропорциональности скорости (А/(мм/с))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Коэффициент интеграла скорости (А/мм)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Коэффициент дифференциала скорости (А/(мм/с^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Коэффициент противодействия насыщению интеграла скорости</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Коэффициент усиления подачи скорости вперед</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра подачи скорости вперед (Гц)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра обратной связи скорости (Гц)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра команды скорости (Гц)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Предел UP выходного сигнала управления скоростью (А)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Предел LOW выходного сигнала управления скоростью (А)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Целевой ток оси Q (А)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>Целевой ток оси D (А)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Значение обратной связи тока оси Q (А)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>Значение обратной связи тока оси D (А)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Коэффициент пропорциональности тока</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Коэффициент интеграла тока</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Коэффициент дифференциала тока</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>Коэффициент компенсации обратной ЭДС оси D</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Коэффициент компенсации обратной ЭДС оси Q</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Коэффициент компенсации обратной ЭДС постоянного магнита</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра обратной связи тока (Гц)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра команды тока (Гц)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Коэффициент усиления подачи тока вперед</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Граница частоты низкочастотного фильтра подачи тока вперед (Гц)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Центральная частота резонансного фильтра команды тока (Гц)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Пропускная полоса резонансного фильтра команды тока (Гц)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Глубина резонансного фильтра команды тока (дБ)</value>
    </data>
</root>