<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Язык</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Соединение потеряно</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Соединение успешно</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Автоматически</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Ручной</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Инициализация</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Запуск</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Останов</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>АО</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Включить</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Сброс ошибки оси</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>Перезагрузка системы</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Инициализация рабочего места</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Включить рабочее место</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Маскировка рабочего места</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Неисправность</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Техобслуживание</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>В работе</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Подключение оборудования</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Драйвер</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Контроллер</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Сообщение в открытом виде</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Обновление прошивки</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Настройка офлайн</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>Сборка системы</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Управление осью</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>Состояние соединения</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Управление рабочим местом</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>Управление системой</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Информация о обратной связи</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Ошибка и неисправность</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Настройка онлайн</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Коррекция отклонения</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>В来回 силу кривой</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Генерация настройки</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>Цифровой IO</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Настройка сервопривода</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Осциллограф</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Базовые настройки</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Управление ролями</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>Управление пользователями</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Список функций</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Назначение прав</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Отслеживание данных</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Журнал операций</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Выберите номер оси:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Соединение драйвера:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Соединение контроллера:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Управление осью двигуна</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Режим движения оси:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Движение Jog</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Абсолютное движение</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Относительное движение</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Движение рабочего места</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>ID оси:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Тип оси:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Двигатель</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Вращательный двигатель</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Линийный двигатель</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Режим скорости:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Режим управления осью:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>ID целевой линии:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>ID целевого рабочего места:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Скорость:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Ускорение:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Торможение:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Джирк:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Точность позиционирования:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Точность предотвращения столкновений:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Целевая позиция:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Выберите операцию:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Выполнить</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Прочитать</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Остановить</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Объект, к которому относится ось</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Линия, к которой относится ось</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Ошибка драйвера</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Ошибка оси</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Текущая позиция оси (мм)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Текущая скорость оси</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>Текущее состояние оси</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Соединение контроллера</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Порт</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Подключить</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Отключить</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Отправить:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Журнал:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Очистить</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Генерация настройки</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Количество системных настроек:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Количество настроек двигателя:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Количество настроек подчиненного узла:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Количество настроек сегмента линии:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Количество настроек рабочего места:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Количество настроек двигуна:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Количество настроек вращательной оси:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Количество настроек IO:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Создать файл настройки</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Настройка онлайн</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Выберите настройку:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>Системные настройки</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Настройки рабочего места</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Записать</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Имя параметра</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Тип настройки</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Прочитанное значение</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Установленное значение</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>Управление системой</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Объект управления:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Двигатель</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Вращательный двигатель</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Линийный двигатель</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>Режим работы системы:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Обучение оси</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Обучение соединения</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Автоматический режим работы</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Режим автоматического режима работы:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Синхронный</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Асинхронный</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Процент скорости:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>ID подчиненного узла:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Режим управления:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Выберите операцию:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Выполнить</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Прочитать</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>ID оси ошибки системы</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>Драйвер ошибки системы</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>Код ошибки системы</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>Код ошибки системы</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>Состояние системы</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Управление соединением</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Настройка соединения:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>Текущий ID объекта:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>ID левого объекта:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>Состояние соединения:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Отключить</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Установить соединение</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>ID правого объекта:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Выберите операцию:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Выполнить</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Прочитать</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>ID соединения:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>ID целевого рабочего места:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Скорость:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Ускорение:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Торможение:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Целевая позиция:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Команда управления:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>ID линии</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>ID левого объекта соединения линии</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>ID правого объекта соединения линии</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>Состояние включения</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>Состояние работы</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Возвращение к началу завершено</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Код ошибки</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Фактическая скорость</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Фактическая позиция</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Имя пользователя</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Пароль</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Запомнить пароль</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Войти</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Введите ключевые слова</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Время начала: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Время</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Модуль</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Операция</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Поведение</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Оператор</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>Просмотреть</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Подробно</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Подробное описание:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Остановить</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Сбор</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Сбросить</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Крест</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>Разделение оси X</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Разделение оси Y</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Импорт</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Экспорт</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Проверка ошибок</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Масштабирование</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Частота выборки (1 - 300, единица мс):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Канал</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Выберите объект</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Пожалуйста, выберите</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Значение</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>Видимость</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Смещение</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Коэффициент увеличения</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Отладка</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Соединение драйвера</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Серийный порт</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Скорость передачи</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Данные биты</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Парный бит</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Стоп - биты</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Подключить</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Отключить</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Параметры драйвера</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Выберите операцию:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Выбрать запись</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Записать все</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Восстановить настройки по умолчанию</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Сбросить ошибку</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Удалить запись о неисправности</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Настройка режима управления:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Право управления:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Местный режим управления:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Подрежим:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Выбрать</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Имя параметра</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Тип настройки</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Минимальное значение</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Максимальное значение</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Прочитанное значение</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Установленное значение</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Права</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Коэффициент</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Мониторинг</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Роль:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Права:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Введите ключевые слова</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>Новый</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Меню</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Код привязки</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>Это кнопка?</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>Скрыт?</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Событие кнопки</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Уровень</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Включить</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Создатель</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Дата создания</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Редактор</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Дата изменения</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Операция</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Имя меню:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Родительское меню:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Введите ключевые слова</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>Новый</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Имя роли</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Уровень</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Создатель</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Дата создания</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Редактор</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Дата изменения</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>Включен?</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Операция</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>Чем меньше приоритет, тем больше права</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>Включить текущую роль?</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Введите ключевые слова</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>Новый</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Имя пользователя</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Реальное имя</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Роль</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>Состояние</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Примечание</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Дата создания</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Дата изменения</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Последний вход</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Операция</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Имя входа:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Пароль:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Изменить пароль</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>Ожидает включения</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>У вас нет прав на этот меню</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI-поток:</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>Исключение в UI-потоке:</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>Фатальная ошибка в UI-потоке!</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Фатальная ошибка в не-UI-потоке</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Исключение в не-UI-потоке:</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task-поток:</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Исключение в Task-потоке:</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Главный поток</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>Переключатель</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>У вас нет прав на это действие</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>Состояние включения единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>Состояние выполнения единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>Состояние тревоги единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>Состояние ошибки единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Левая коллизия единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Правая коллизия единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Положительная граница остановки единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Отрицательная граница остановки единичного осевого двигателя</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>Единичный осевой двигатель находится на рабочем месте</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>Единичный осевой двигатель достиг цели</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>Система готова</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>Состояние включения системы</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>Состояние ошибки системы</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>Состояние выполнения системы</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>Состояние шины системы</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>Состояние проверки платформы системы</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Конфигурация оси завершена, можно начать инициализацию последовательности осей</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Конфигурация параметров движения завершена, можно восстановить старое состояние системы</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>Система завершит восстановление старого состояния</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: резерв\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>[SQL-запрос]:</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>[Параметры SQL]:</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>Введенное значение должно быть в указанном диапазоне</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Корневой узел</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Получено успешно</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Корневой узел</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Получено успешно</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Добавлено успешно</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Обновлено успешно</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Удалено успешно</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Получено успешно</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Добавлено успешно</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Обновлено успешно</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Удалено успешно</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Движение вперед Jog</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Движение назад Jog</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Абсолютное движение</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Относительное движение</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Движение по рабочему месту</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Установка нулевого положения</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Сброс оси</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Файл конфигурации сгенерирован успешно</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Отправка конфигурации рабочего места успешно</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Не производится обработка</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>Соединение контроллера разорвано!</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>Соединение контроллера установлено!</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>Контроллер вернул 0 осей, такое действие невозможно!</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>Без контроля</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Позиционное управление двух осей</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Идентификация электрического угла оси 0</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>Тестирование дискретизации直流</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>Тестирование дискретизации交流</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>Файл CSV (*.csv)|*.csv|Все файлы (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Пожалуйста, выберите CSV-файл</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Пожалуйста, выберите путь сохранения</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Экспорт данных успешно</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>Введенный объект не может быть пустым!</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>Новый добавленный контент</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>Это мои написанные данные!</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>Нет соответствующего каталога</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Кнопка</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Отправка данных:</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>Не удалось разобрать данные адаптера!</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>Контроллер не подключен!</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Отправка сердечного пульса контроллера провалена</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Включение сверху</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Включение снизу</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Стоп</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Установка нулевого положения</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Движение вперед на единицу</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Движение назад на единицу</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Абсолютное движение</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Относительное движение</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Движение по рабочему месту</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Включение сверху</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Включение снизу</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Сброс ошибки</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Запуск</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Пауза</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Сброс экстренно</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>Объект управления уже удален из протокола, не используйте это свойство</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>Объект управления уже удален из протокола, не используйте это свойство</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Параметры позиции</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Позиционное возобновление оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Позиционное возобновление оси 1</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Параметры скорости</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>Команда скорости оси 0</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Скоростное возобновление оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>Команда скорости оси 1</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Скоростное возобновление оси 1</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Параметры тока</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>Команда тока оси 0</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Токовое возобновление оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>Команда тока оси 1</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Токовое возобновление оси 1</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Параметры напряжения</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>Ссылка напряжения оси 0 D</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>Ссылка напряжения оси 1 D</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>Ссылка напряжения оси 0 Q</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>Ссылка напряжения оси 1 Q</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>Нагрузочное напряжение оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>Нагрузочное напряжение оси 1</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>Ток U оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>Ток U оси 1</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>Ток V оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>Ток V оси 1</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>Ток W оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>Ток W оси 1</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>Напряжение управления оси 0</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>Напряжение управления оси 1</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-параметры двигателя</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-параметры системы</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-параметры энкодера</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-параметры защиты</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-журнал ошибок</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-статус контроля</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-параметры позиции</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-параметры скорости</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-параметры тока</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>Исключение GetFromDriveContext</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Отправка сердечного пульса серверов не удалась</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>Третий инструкция не существует</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>Имя роли не может быть пустым</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>Введенное значение выходит за пределы!</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>Введенное значение неверно!</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>Система</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Двигатель</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>Служебный узел</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Линия</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Рабочее место</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Ось</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Последовательность осей</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>ПИД оси</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Смещение оси</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Направление подключения оборудования</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Смещение рабочего места</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI-вид</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации системы</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации двигателя</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации служебного узла</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации сегмента линии</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации рабочего места</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации движителя</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации последовательности осей</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации ПИД оси</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Параметры компенсации движителя</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации направления подключения оборудования</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации компенсации рабочего места</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Параметры конфигурации вида линии</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-параметры двигателя</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-параметры системы</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-параметры энкодера</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-параметры защиты</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-журнал ошибок</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-статус контроля</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-параметры позиции</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-параметры скорости</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-параметры тока</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>Исключение ParameterModelExtension</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Китайский (упрощенный)</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Китайский (традиционный)</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Язык Japonais</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Неизвестно</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>Без описания</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>Без значения</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>Терминал удален</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>Новый SerialPort должен быть в состоянии подключения.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>Произошла ошибка при обработке данных</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>Файл конфигурации не может быть пустым.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>Конфигурация последовательности портов не может быть пустой.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>Текущий адаптер не поддерживает отправку объектов.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>Конфигурация представления</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Конфигурация двигателя</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Сл slave-узел</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Конфигурация линии</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Конфигурация работы рабочего места</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Конфигурация оси</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Конфигурация последовательности</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>Конфигурация ПИД</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Конфигурация компенсации оси</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Конфигурация компенсации рабочего места</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>Однонажатие для загрузки на контроллер</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>Однонажатие для загрузки на локальный компьютер</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Загрузить конфигурацию</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Сохранить конфигурацию под другим именем</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Отправлено успешно</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>Имя роли не может быть пустым</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Онлайн-демонстрация</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Аларм</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Обратная связь</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Удалить все уведомления</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Тип</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Источник</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Содержание сообщения</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Глобальная смена данных</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Проверка платформы</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>Инициализация настройки системных параметров</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Получение информации о slave - станциях</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Отображение адреса slave - станции на адрес управления</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Проверка состояния master - slave станций</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Инициализация состояний шин - системы и т.д. выполнена</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Инициализация параметров, связанных с движением</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Успешная инициализация магнитного привода</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>Ошибка системного привода</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Хост:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Порт:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Имя пользователя:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Пароль:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Подключение</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Отключение</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Удаленный каталог: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Назад</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Вперед</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Вверх</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Обновить</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Создать папку</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Скачать на локальный компьютер</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Локальный каталог: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Загрузить на сервер</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Журнал передачи:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>Мягкая смена системы</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Подключение к FTP - серверу...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Подключено к FTP - серверу</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Подключение</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Подключение отключено</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Отключить</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Загрузка удаленного каталога: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Удаленный каталог загружен: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Не удалось загрузить удаленный каталог: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Обзор</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Загрузка локального каталога: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Локальный каталог загружен: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Не удалось загрузить локальный каталог: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Скачивание: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Скачивание завершено: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Скачать</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Не удалось скачать: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Загрузка: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Загрузка завершена: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Загрузить</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Не удалось загрузить: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Каталог создан: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Создать каталог</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Не удалось создать каталог: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Каталог удален: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>Файл удален: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Открыть</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>Система запущена</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>Система готова к работе</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>Система включена</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>Системная шина подключена</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>Система находится в ошибочном состоянии</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Ошибка привода оси</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Ошибка движения оси</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>Состояние ошибки оси</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Аларм оси</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Положительная граница оси</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Отрицательная граница оси</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Предупреждение оси</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>Ось достигнула левой границы</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>Ось достигнула правой границы</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>Ось достигла целевого положения</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>Ось находится на рабочем месте</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Уведомление оси</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>Ось работает</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>Ось включена</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>Состояние оси</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>Вращающаяся ось работает</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Вращающаяся ось вернулась в исходное положение</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>Вращающаяся ось включена</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Вращающаяся ось</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>Привод успешно подключен!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>Привод отключен!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>Параметры привода успешно восстановлены!</value>
  </data>
</root>