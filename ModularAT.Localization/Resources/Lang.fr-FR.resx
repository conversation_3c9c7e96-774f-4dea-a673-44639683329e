<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Connexion perdue</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Connexion réussie</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Automatique</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Manuel</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Initialisation</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Démarrer</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Arrêter</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>AU</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Activer</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Réinitialiser l'erreur d'axe</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>Redémarrer le système</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Initialisation de poste de travail</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Activer le poste de travail</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Masquer le poste de travail</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Défaut</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Maintenance</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>En cours d'exécution</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Connexion d'équipement</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Pilote</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Contrôleur</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Message en clair</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Mise à jour du firmware</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Configuration hors ligne</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>Assemblage du système</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Contrôle d'axe</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>État de connexion</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Contrôle de poste de travail</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>Contrôle du système</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Informations de retour</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Erreur et défaillance</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Configuration en ligne</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Compensation de déviation</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>Aller-retour de force en courbe</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Génération de configuration</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>IO numérique</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Configuration de servo</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Oscilloscope</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Paramètres de base</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Gestion des rôles</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>Gestion des utilisateurs</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Liste de fonctions</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Attribution de droits</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Repérage de données</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Journal d'opérations</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Sélectionnez le numéro d'axe :</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Connexion du pilote :</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Connexion du contrôleur :</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Contrôle d'axe de mobile</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Mode de mouvement d'axe :</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Mouvement Jog</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Mouvement absolu</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Mouvement relatif</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Mouvement de poste de travail</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>ID d'axe :</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Type d'axe :</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Moteur rotatif</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Moteur linéaire</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Mode de vitesse :</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Mode de contrôle d'axe :</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>ID de ligne cible :</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>ID de poste de travail cible :</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Vitesse :</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Accélération :</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Décélération :</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Jerk :</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Précision de positionnement :</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Précision d'évitement de collision :</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Position cible :</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Sélectionnez une opération :</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Exécuter</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Lire</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Arrêter</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Objet auquel appartient l'axe</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Ligne à laquelle appartient l'axe</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Erreur de pilote</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Erreur d'axe</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Position actuelle de l'axe (mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Vitesse actuelle de l'axe</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>État actuel de l'axe</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Connexion du contrôleur</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Connecter</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Déconnecter</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Envoyer :</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Journal :</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Effacer</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Génération de configuration</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Nombre de configurations de système :</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Nombre de configurations de moteur :</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Nombre de configurations de nœud esclave :</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Nombre de configurations de segment de ligne :</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Nombre de configurations de poste de travail :</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Nombre de configurations de mobile :</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Nombre de configurations d'axe rotatif :</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Nombre de configurations d'IO :</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Générer un fichier de configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Configuration en ligne</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Sélectionnez une configuration :</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>Configuration du système</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Configuration de poste de travail</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Écrire</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Nom de paramètre</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Type de paramètre</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Valeur lue</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Valeur définie</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>Contrôle du système</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Objet de contrôle :</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Moteur rotatif</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Moteur linéaire</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>Mode de fonctionnement du système :</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Apprentissage d'axe</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Apprentissage de connexion</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Fonctionnement automatique</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Mode de fonctionnement automatique :</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Synchrone</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Asynchrone</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Taux de vitesse :</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>ID de nœud esclave :</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Mode de contrôle :</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Sélectionnez une opération :</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Exécuter</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Lire</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>ID d'axe d'erreur de système</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>Pilote d'erreur de système</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>Code d'erreur de système</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>Code d'erreur de système</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>État du système</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Contrôle de connexion</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Configuration de connexion :</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>ID d'objet actuel :</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>ID d'objet gauche :</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>État de connexion :</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Déconnecter</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Établir une connexion</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>ID d'objet droit :</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Sélectionnez une opération :</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Exécuter</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Lire</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>ID de connexion :</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>ID de poste de travail cible :</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Vitesse :</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Accélération :</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Décélération :</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Position cible :</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Commande de contrôle :</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>ID de ligne</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>ID d'objet de connexion gauche de la ligne</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>ID d'objet de connexion droit de la ligne</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>État d'activation</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>État de fonctionnement</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Revenir à zéro terminé</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Code d'erreur</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Vitesse réelle</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Position réelle</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Se souvenir du mot de passe</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Veuillez saisir des mots clés</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Heure de début : </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Temps</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Opération</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Comportement</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Opérateur</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>Voir</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Détails</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Description détaillée :</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Arrêter</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Collecter</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Croix</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>Échelle de l'axe X</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Échelle de l'axe Y</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Importer</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Exporter</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Vérification d'erreur</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Fréquence d'échantillonnage (1 - 300, unité ms) :</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Canal</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Sélectionnez un objet</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Veuillez sélectionner</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Valeur</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>Visible?</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Décalage</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Facteur d'agrandissement</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Couleur</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Débogage</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Connexion du pilote</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Port série</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Vitesse de transmission</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Bits de données</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Bit de parité</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Bits d'arrêt</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Connecter</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Déconnecter</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Paramètres du pilote</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Sélectionnez une opération :</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Sélectionner l'écriture</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Tout écrire</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Rétablir les paramètres par défaut</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Réinitialiser l'erreur</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Effacer les enregistrements de défaillance</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Configuration du mode d'entraînement :</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Droit de contrôle :</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Mode de contrôle local :</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Sous - mode :</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Sélectionner</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Nom de paramètre</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Type de paramètre</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Valeur minimale</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Valeur maximale</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Valeur lue</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Valeur définie</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Droits</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Coefficient</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Surveillance</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Rôle :</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Droits :</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Veuillez saisir des mots clés</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Code de liaison</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>Est - ce un bouton?</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>Est - ce caché?</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Événement de bouton</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Activer</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Créateur</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Modificateur</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Date de modification</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Opération</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Éditer</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Nom du menu :</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Menu parent :</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Veuillez saisir des mots clés</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Nom de rôle</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Créateur</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Modificateur</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Date de modification</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>Est - ce activé?</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Opération</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Éditer</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>Plus la priorité est faible, plus les droits sont importants</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>Activer le rôle actuel?</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Veuillez saisir des mots clés</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Nom réel</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Rôle</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>État</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Remarque</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Date de modification</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Dernière connexion</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Opération</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Éditer</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Nom de connexion :</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Mot de passe :</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Changer le mot de passe</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>En attente d'activation</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>Vous n'avez pas les droits pour ce menu</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>Thread UI :</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>Exception du thread UI :</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>Une erreur fatale s'est produite dans le thread UI !</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Une erreur fatale s'est produite dans un thread non - UI</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Exception d'un thread non - UI :</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Thread Task :</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Exception du thread Task :</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Thread principal</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>Interrupteur</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>Vous n'avez pas la permission pour cette opération</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>État d'activation d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>État de fonctionnement d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>État d'alerte d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>État d'erreur d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Collision gauche d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Collision droite d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Limite positive d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Limite négative d'un axe unique</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>Axe unique sur le poste de travail</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>Axe unique a atteint la position cible</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>Le système est prêt</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>État d'activation du système</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>État d'erreur du système</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>État de fonctionnement du système</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>État du bus du système</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>État de vérification de la plateforme du système</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Configuration des axes terminée, l'initialisation de la séquence d'axes peut être effectuée</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Configuration des paramètres de mouvement terminée, la restauration de l'ancien état du système peut être effectuée</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>Restauration de l'ancien état du système terminée</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8 - 31 : Réservé\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【Instruction SQL】 :</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【Paramètres SQL】 :</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>La valeur d'entrée doit être dans la plage spécifiée</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Noeud racine</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Obtention réussie</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Noeud racine</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Obtention réussie</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Ajout réussi</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Mise à jour réussie</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Suppression réussie</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Obtention réussie</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Ajout réussi</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Mise à jour réussie</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Suppression réussie</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Mouvement Jog positif</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Mouvement Jog négatif</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Mouvement absolu</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Mouvement relatif</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Mouvement vers le poste de travail</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Réglage du zéro</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Réinitialisation de l'axe</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Génération du fichier de configuration réussie</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Envoi de la configuration du poste de travail réussi</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Aucun traitement</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>Connexion au contrôleur perdue !</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>Connexion au contrôleur réussie !</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>Le contrôleur a renvoyé un nombre d'axes égal à 0, cette opération ne peut pas être effectuée !</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>Aucun contrôle</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Contrôle de position des deux axes</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Identification de l'angle électrique de l'axe 0</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>Test d'échantillonnage en courant continu</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>Test d'échantillonnage en courant alternatif</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>Fichiers CSV (*.csv)|*.csv|Tous les fichiers (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Veuillez sélectionner un fichier CSV</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Veuillez sélectionner un chemin de sauvegarde</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Exportation des données réussie</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>L'objet passé ne peut pas être nul !</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>Nouveau contenu ajouté</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>C'est le contenu que j'ai écrit.</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>Le répertoire correspondant n'existe pas</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Bouton</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Envoi de données :</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>L'adaptateur a échoué à analyser les données !</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>Le contrôleur n'est pas connecté !</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Échec de l'envoi du pouls du contrôleur</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Activation supérieure</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Activation inférieure</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Arrêt</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Réinitialisation</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Réglage du zéro</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Pointage vers l'avant</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Pointage vers l'arrière</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Mouvement absolu</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Mouvement relatif</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Mouvement vers le poste de travail</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Activation supérieure</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Activation inférieure</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Réinitialisation des erreurs</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Fonctionnement</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Mise en pause</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Arrêt d'urgence</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>L'objet de contrôle a été supprimé du protocole, n'utilisez pas cette propriété</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>L'objet de contrôle a été supprimé du protocole, n'utilisez pas cette propriété</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Paramètres de position</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Retour de position de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Retour de position de l'axe 1</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Paramètres de vitesse</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>Commande de vitesse de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Retour de vitesse de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>Commande de vitesse de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Retour de vitesse de l'axe 1</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Paramètres de courant</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>Commande de courant de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Retour de courant de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>Commande de courant de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Retour de courant de l'axe 1</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Paramètres de tension</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>Tension de référence de l'axe D de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>Tension de référence de l'axe D de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>Tension de référence de l'axe Q de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>Tension de référence de l'axe Q de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>Tension de bus de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>Tension de bus de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>Courant de la phase U de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>Courant de la phase U de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>Courant de la phase V de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>Courant de la phase V de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>Courant de la phase W de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>Courant de la phase W de l'axe 1</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>Tension de contrôle de l'axe 0</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>Tension de contrôle de l'axe 1</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1 - Paramètres du moteur</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2 - Paramètres du système</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3 - Paramètres de l'encodeur</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4 - Paramètres de protection</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5 - Enregistrement des pannes</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6 - État de contrôle</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7 - Paramètres de position</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8 - Paramètres de vitesse</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9 - Paramètres de couple</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>Exception de GetFromDriveContext</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Échec de l'envoi du pouls du servo</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>La troisième instruction n'existe pas</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>Le nom du rôle ne peut pas être nul</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>La valeur d'entrée dépasse la limite !</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>Valeur d'entrée incorrecte !</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>Système</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Moteur</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>Noeud d'esclave</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Ligne de production</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Poste de travail</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Axe</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Séquence d'axes</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>PID de l'axe</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Décalage de l'axe</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Sens de câblage de l'appareil</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Décalage du poste de travail</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>Vue UI</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du système</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du moteur</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du noeud d'esclave</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du segment de ligne de production</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration de fonctionnement du poste de travail</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du mobile</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration de la séquence d'axes</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du PID de fonctionnement de l'axe</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Paramètres de compensation du mobile</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration du sens de câblage de la ligne de production</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Paramètres de compensation du poste de travail</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Paramètres de configuration de la vue de la ligne de production</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1 - Paramètres du moteur</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2 - Paramètres du système</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3 - Paramètres de l'encodeur</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4 - Paramètres de protection</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5 - Enregistrement des pannes</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6 - État de contrôle</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7 - Paramètres de position</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8 - Paramètres de vitesse</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9 - Paramètres de couple</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>Exception de ParameterModelExtension</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Chinois simplifié</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Chinois traditionnel</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Japonais</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Inconnu</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>Aucune description</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>Aucune valeur</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>Le terminal distant est fermé</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>Le nouveau SerialPort doit être en état de connexion.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>Une erreur s'est produite lors du traitement des données</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>Le fichier de configuration ne peut pas être nul.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>La configuration du port série ne peut pas être nulle.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>L'adaptateur actuel ne prend pas en charge l'envoi d'objets.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>Configuration de la vue</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Configuration du moteur</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Nœud esclave</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Configuration de la ligne de production</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Configuration du fonctionnement de la poste de travail</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Configuration de l'axe</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Configuration de la séquence</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>Configuration PID</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Configuration de la compensation de l'axe</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Configuration de la compensation de la poste de travail</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>Téléverser en un clic sur le contrôleur</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>Télécharger en un clic en local</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Charger la configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Enregistrer la configuration sous un autre nom</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Envoi réussi</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>Le nom du rôle ne peut pas être vide</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Démonstration en ligne</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Alarme</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Informations de feedback</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Effacer toutes les notifications</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Contenu du message</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Réinitialiser les données globales</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Vérification de la plateforme</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>Initialisation de la configuration des paramètres système</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Obtention des informations de la station esclave</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Mapper l'adresse de la station esclave à l'adresse de commande</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Vérification de l'état de la station maître-esclave</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Initialisation de l'état du bus-système, etc. terminée</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Initialisation des paramètres liés au mouvement</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Initialisation réussie du dispositif d'entraînement magnétique</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>Erreur de la commande du système</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Hôte:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Port:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Nom d'utilisateur:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Mot de passe:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Se déconnecter</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Répertoire distant : </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Avancer</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Monter</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Actualiser</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Créer un dossier</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Télécharger localement</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Répertoire local : </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Télécharger sur le serveur</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Journal de transmission :</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>Réinitialisation logicielle du système</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Connexion au serveur FTP...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Connecté au serveur FTP</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Déconnecté</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Déconnecter</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Chargement du répertoire distant : </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Répertoire distant chargé : </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Échec du chargement du répertoire distant : </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Explorer</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Chargement du répertoire local : </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Répertoire local chargé : </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Échec du chargement du répertoire local : </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Téléchargement en cours : </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Téléchargement terminé : </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Télécharger</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Échec du téléchargement : </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Téléchargement en cours : </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Téléchargement terminé : </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Télécharger</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Échec du téléchargement : </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Dossier créé : </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Créer un dossier</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Échec de la création du dossier : </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Dossier supprimé : </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>Fichier supprimé : </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Ouvrir</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>Le système est en cours d'exécution</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>Le système est prêt</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>Le système est activé</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>Le bus système est connecté</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>Le système est en erreur</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Erreur de l'entraînement de l'axe</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Erreur de mouvement de l'axe</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>État d'erreur de l'axe</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Alarme de l'axe</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Limite positive de l'axe</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Limite négative de l'axe</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Avertissement de l'axe</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>L'axe est arrivé à gauche</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>L'axe est arrivé à droite</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>L'axe est arrivé à la position cible</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>L'axe est sur la station de travail</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Notification de l'axe</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>L'axe est en cours d'exécution</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>L'axe est activé</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>État de l'axe</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>L'axe rotatif est en cours d'exécution</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Initialisation de l'axe rotatif terminée</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>L'axe rotatif est activé</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Axe rotatif</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>Entraînement connecté avec succès!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>Entraînement déconnecté!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>Récupération des paramètres de l'entraînement réussie!</value>
  </data>
</root>