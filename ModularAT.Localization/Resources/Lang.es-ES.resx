<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Idioma</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Conexión perdida</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Conexión exitosa</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Automático</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Inicializar</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Iniciar</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Detener</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>ParadaE</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Reiniciar</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Habilitar</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Reiniciar error de eje</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>Reiniciar sistema</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Inicialización de estación de trabajo</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Habilitar estación de trabajo</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Ocultar estación de trabajo</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Fallo</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Mantenimiento</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>En ejecución</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Conexión de equipo</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Controlador</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Controlador</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Mensaje en claro</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Actualización de firmware</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Configuración offline</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>Conjunto del sistema</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Control de eje</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>Estado de conexión</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Control de estación de trabajo</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>Control del sistema</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Información de retroalimentación</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Error y fallo</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Configuración en línea</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Compensación de desviación</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>Recorrido de fuerza en curva</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Generación de configuración</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>IO digital</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Configuración de servo</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Osciloscopio</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Configuraciones básicas</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Gestión de roles</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>Gestión de usuarios</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Lista de funciones</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Asignación de permisos</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Rastreo de datos</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Registro de operaciones</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Seleccionar número de eje:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Conexión de controlador:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Conexión de controlador:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Control de eje de móvil</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Modo de movimiento de eje:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Movimiento Jog</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Movimiento absoluto</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Movimiento relativo</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Movimiento de estación de trabajo</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>ID de eje:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Tipo de eje:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Móvil</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Motor rotatorio</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Motor lineal</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Modo de velocidad:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Modo de control de eje:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>ID de línea objetivo:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>ID de estación de trabajo objetivo:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Velocidad:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Aceleración:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Desaceleración:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Jerk:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Precisión de posicionamiento:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Precisión de prevención de colisión:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Posición objetivo:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Seleccionar operación:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Ejecutar</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Leer</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Detener</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Objeto al que pertenece el eje</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Línea a la que pertenece el eje</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Error de controlador</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Error de eje</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Posición actual del eje (mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Velocidad actual del eje</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>Estado actual del eje</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Conexión de controlador</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Puerto</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Enviar:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Registro:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Generación de configuración</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Número de configuraciones de sistema:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Número de configuraciones de motor:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Número de configuraciones de nodo esclavo:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Número de configuraciones de segmento de línea:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Número de configuraciones de estación de trabajo:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Número de configuraciones de móvil:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Número de configuraciones de eje rotatorio:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Número de configuraciones de IO:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Generar archivo de configuración</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Configuración en línea</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Seleccionar configuración:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>Configuración de sistema</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Configuración de estación de trabajo</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Escribir</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Nombre de parámetro</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Tipo de configuración</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Valor leído</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Valor configurado</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>Control del sistema</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Objeto de control:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Móvil</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Motor rotatorio</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Motor lineal</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>Modo de funcionamiento del sistema:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Aprendizaje de eje</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Aprendizaje de conexión</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Funcionamiento automático</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Modo de funcionamiento automático:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Sincrónico</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Asincrónico</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Porcentaje de velocidad:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>ID de nodo esclavo:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Modo de control:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Seleccionar operación:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Ejecutar</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Leer</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>ID de eje de error de sistema</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>Controlador de error de sistema</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>Código de error de sistema</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>Código de error de sistema</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>Estado del sistema</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Control de conexión</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Configuración de conexión:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>ID de objeto actual:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>ID de objeto izquierdo:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>Estado de conexión:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Establecer conexión</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>ID de objeto derecho:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Seleccionar operación:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Ejecutar</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Leer</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>ID de conexión:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>ID de estación de trabajo objetivo:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Velocidad:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Aceleración:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Desaceleración:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Posición objetivo:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Comando de control:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>ID de línea</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>ID de objeto de conexión izquierdo de la línea</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>ID de objeto de conexión derecho de la línea</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>Estado de habilitación</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>Estado de ejecución</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Retorno a cero completado</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Código de error</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Velocidad real</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Posición real</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Nombre de usuario</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Contraseña</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Recordar contraseña</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Iniciar sesión</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Ingrese palabras clave</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Hora de inicio: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Tiempo</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Módulo</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Operación</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Comportamiento</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Operador</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>Ver</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Detalles</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Descripción detallada:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Detener</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Recopilar</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Reiniciar</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Cruz</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>Escala del eje X</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Escala del eje Y</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Importar</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Exportar</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Verificación de error</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Frecuencia de muestreo (1 - 300, unidad ms):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Canal</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Seleccionar objeto</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Seleccione</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Valor</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>¿Visible?</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Desplazamiento</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Factor de ampliación</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Depuración</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Conexión de controlador</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Puerto serie</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Velocidad de transmisión</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Bits de datos</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Bit de paridad</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Bits de parada</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Parámetros de controlador</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Seleccionar operación:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Seleccionar escritura</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Escribir todo</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Restablecer parámetros predeterminados</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Reiniciar error</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Borrar registro de fallos</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Configuración de modo de control:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Control:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Modo de control local:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Sub - modo:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Seleccionar</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Nombre de parámetro</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Tipo de configuración</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Valor mínimo</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Valor máximo</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Valor leído</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Valor configurado</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Permisos</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Coeficiente</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Monitoreo</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Rol:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Permisos:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Ingrese palabras clave</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>Nuevo</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Menú</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Código de enlace</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>¿Es un botón?</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>¿Está oculto?</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Evento de botón</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Nivel</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Habilitar</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Creador</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Fecha de creación</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Modificador</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Fecha de modificación</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Operación</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Nombre del menú:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Menú padre:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Ingrese palabras clave</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>Nuevo</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Nombre de rol</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Nivel</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Creador</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Fecha de creación</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Modificador</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Fecha de modificación</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>¿Habilitado?</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Operación</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>Cuanto menor es la prioridad, mayores son los permisos</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>¿Habilitar el rol actual?</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Ingrese palabras clave</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>Nuevo</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Nombre de usuario</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Nombre real</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Rol</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Observación</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Fecha de creación</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Fecha de modificación</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Último inicio de sesión</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Operación</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Nombre de inicio de sesión:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Contraseña:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Cambiar contraseña</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>Pendiente de habilitación</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>No tiene permisos para este menú</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>Interface de usuário:</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>Interface de usuário com exceção:</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>Interface de usuário ocorreu um erro fatal!</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Interface de usuário não ocorreu um erro fatal</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Interface de usuário com exceção:</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Thread de tarefa:</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Thread de tarefa com exceção:</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Thread principal</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>Interruptor</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>Você não tem permissão para essa operação</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>Estado de habilitação do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>Estado de execução do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>Estado de alarme do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>Estado de erro do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Colisão esquerda do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Colisão direita do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Limite positivo do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Limite negativo do eixo</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>O eixo está no posto de trabalho</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>O eixo atingiu a posição alvo</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>O sistema está pronto</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>Estado de habilitação do sistema</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>Estado de erro do sistema</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>Estado de execução do sistema</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>Estado do barramento do sistema</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>Estado de verificação do sistema de plataforma</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Configuração do eixo concluída, pode-se inicializar a sequência de eixos</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Configuração de parâmetros de movimento concluída, pode-se restaurar o estado antigo do sistema</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>O sistema restaurou o estado antigo concluído</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: Reservado\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【Declaração SQL】:</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【Parâmetros SQL】:</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>O valor de entrada deve estar dentro do intervalo especificado</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Nó raiz</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Obtenção bem-sucedida</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Nó raiz</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Obtenção bem-sucedida</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Adição bem-sucedida</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Atualização bem-sucedida</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Exclusão bem-sucedida</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Obtenção bem-sucedida</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Adição bem-sucedida</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Atualização bem-sucedida</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Exclusão bem-sucedida</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Movimento Jog positivo</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Movimento Jog negativo</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Movimento absoluto</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Movimento relativo</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Movimento do posto de trabalho</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Definir o ponto zero</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Redefinição do eixo</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Geração do arquivo de configuração bem-sucedida</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Envio do arquivo de configuração do posto de trabalho bem-sucedido</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Não fazer nada</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>A conexão do controlador foi interrompida!</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>A conexão do controlador foi bem-sucedida!</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>O controlador retornou que o número de eixos é 0, não é possível fazer essa operação!</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>Sem controle</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Controle de posição em dois eixos</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Identificação do ângulo elétrico do eixo 0</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>Teste de amostragem em corrente contínua</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>Teste de amostragem em corrente alternada</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>Arquivo CSV (*.csv)|*.csv|Todos os arquivos (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Selecione um arquivo CSV</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Selecione o caminho de salvamento</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Exportação de dados bem-sucedida</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>O objeto passado não pode ser vazio!</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>Conteúdo adicionado</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>Esta é a minha escrita aqui</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>Não existe um diretório correspondente</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Botão</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Enviar dados:</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>Falha na análise de dados do adaptador!</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>O controlador não está conectado!</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Falha no envio do heartbeat do controlador</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Habilitação superior</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Habilitação inferior</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Parar</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Resetar</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Definir o ponto zero</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Avançar em passos</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Recuar em passos</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Movimento absoluto</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Movimento relativo</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Movimento do posto de trabalho</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Habilitação superior</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Habilitação inferior</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Reset de erro</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Executar</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Pausar</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Parada urgente</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>O objeto de controle foi removido do protocolo, não use esse atributo</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>O objeto de controle foi removido do protocolo, não use esse atributo</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Parâmetro de posição</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Feedback de posição do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Feedback de posição do eixo 1</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Parâmetro de velocidade</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>Instrução de velocidade do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Feedback de velocidade do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>Instrução de velocidade do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Feedback de velocidade do eixo 1</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Parâmetro de corrente</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>Instrução de corrente do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Feedback de corrente do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>Instrução de corrente do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Feedback de corrente do eixo 1</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Parâmetro de tensão</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>Referência de tensão D do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>Referência de tensão D do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>Referência de tensão Q do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>Referência de tensão Q do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>Tensão da barramento do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>Tensão da barramento do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>Fase U do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>Fase U do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>Fase V do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>Fase V do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>Fase W do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>Fase W do eixo 1</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>Tensão de controle do eixo 0</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>Tensão de controle do eixo 1</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-Parâmetros do motor</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-Parâmetros do sistema</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-Parâmetros do codificador</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-Parâmetros de proteção</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-Registros de falha</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-Estado de controle</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-Parâmetros de posição</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-Parâmetros de velocidade</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-Parâmetros de torque</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>Falha GetFromDriveContext</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Falha no envio do heartbeat do servo</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>A terceira instrução não existe</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>O nome do papel não pode ser vazio</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>O valor de entrada ultrapassa o limite!</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>O valor de entrada é inválido!</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>Sistema</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Motor</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value> Nó do estação secundária</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Linha</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Posto de trabalho</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Eixo</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Sequência de eixos</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>PID do eixo</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Deslocamento do eixo</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Direção de ligação do equipamento</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Deslocamento do posto de trabalho</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>Interface do usuário</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do sistema</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do motor</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do nó do estação secundária</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração da seção da linha</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do posto de trabalho</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Parâmetros do atuador</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração da sequência de eixos</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do PID do eixo</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do compensador do atuador</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração da direção de ligação do equipamento</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração do compensador do posto de trabalho</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Parâmetros de configuração da interface do usuário</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-Parâmetros do motor</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-Parâmetros do sistema</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-Parâmetros do codificador</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-Parâmetros de proteção</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-Registros de falha</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-Estado de controle</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-Parâmetros de posição</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-Parâmetros de velocidade</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-Parâmetros de torque</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>Falha ParameterModelExtension</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Simplificado chinês</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Chinês tradicional</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Japones</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Desconhecido</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>Sem descrição</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>Sem valor</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>O terminal remoto foi fechado</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>A nova SerialPort deve estar no estado de conexão.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>Ocorreu um erro ao processar os dados</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>O arquivo de configuração não pode ser vazio.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>A configuração da porta serial não pode ser vazia.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>O adaptador atual não suporta a transmissão de objetos.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>Configuración de vista</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Configuración de motor</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Nodo esclavo</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Configuración de línea de producción</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Configuración de funcionamiento de estación de trabajo</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Configuración de eje</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Configuración de secuencia</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>Configuración PID</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Configuración de compensación de eje</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Configuración de compensación de estación de trabajo</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>Subir a controlador con un clic</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>Descargar a local con un clic</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Cargar configuración</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Guardar configuración como</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Envío exitoso</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>El nombre del rol no puede estar vacío</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Demostración en línea</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Alarma</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Información de retroalimentación</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Borrar todas las notificaciones</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Tipo</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Origen</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Contenido del mensaje</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Restablecimiento de datos globales</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Verificación de la plataforma</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>Inicialización de la configuración de parámetros del sistema</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Obtención de información de la estación esclava</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Mapeo de la dirección de la estación esclava a la dirección de control</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Verificación del estado de la estación maestra-esclava</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Inicialización del estado del bus-sistema, etc. completada</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Inicialización de los parámetros relacionados con el movimiento</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Inicialización exitosa del controlador magnético</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>Error del controlador del sistema</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Anfitrión:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Puerto:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Nombre de usuario:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Contraseña:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Directorio remoto: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Atrás</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Adelante</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Arriba</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Crear carpeta</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Descargar en el directorio local</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Directorio local: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Subir al servidor</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Registro de transmisión:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>Restablecimiento soft del sistema</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Conectándose al servidor FTP...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Conectado al servidor FTP</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Desconectado</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Cargando directorio remoto: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Directorio remoto cargado: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Error al cargar el directorio remoto: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Examinar</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Cargando directorio local: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Directorio local cargado: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Error al cargar el directorio local: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Descargando: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Descarga completada: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Descargar</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Error en la descarga: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Subiendo: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Subida completada: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Subir</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Error en la subida: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Directorio creado: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Crear directorio</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Error al crear el directorio: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Directorio eliminado: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>Archivo eliminado: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Abrir</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>El sistema está en ejecución</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>El sistema está listo</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>El sistema está habilitado</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>El bus del sistema está conectado</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>El sistema está en estado de error</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Error del controlador del eje</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Error de movimiento del eje</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>Estado de error del eje</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Alarma del eje</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Límite positivo del eje</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Límite negativo del eje</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Advertencia del eje</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>El eje ha llegado a la posición izquierda</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>El eje ha llegado a la posición derecha</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>El eje ha alcanzado la posición objetivo</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>El eje está en la estación de trabajo</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Notificación del eje</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>El eje está en ejecución</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>El eje está habilitado</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>Estado del eje</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>El eje giratorio está en ejecución</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Finalizada la inicialización del eje giratorio</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>El eje giratorio está habilitado</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Eje giratorio</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>¡Controlador conectado correctamente!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>¡Controlador desconectado!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>¡Parámetros del controlador restaurados correctamente!</value>
  </data>
</root>