<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Cấu hình trực tuyến</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Tạo cấu hình</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Lịch sử thao tác</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Truy vết dữ liệu</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Thu thập</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Xóa ghi chép sự cố</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Đặt lại lỗi</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Khôi phục tham số mặc định</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Ghi tất cả</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Chọn ghi</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Thực thi</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Thực thi</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Dừng</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Thực thi</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Phân quyền</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menu</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Người dùng</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Vai trò</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Sóng kế</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Cấu hình servo</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Kiểm soát hệ thống</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Trạng thái kết nối</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Kiểm soát trục</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Tổng thành phần hệ thống</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Kết nối bộ điều khiển</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Kết nối ổ cắm</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Cài đặt cơ bản</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Ổ cắm</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Bộ điều khiển</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Kết nối thiết bị</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Điện trở dây động cơ (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Độ tự cảm dây động cơ (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Dòng điện định mức động cơ (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Mô men định mức động cơ (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Dòng điện đỉnh cao động cơ (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Hằng số mô men động cơ (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Hệ số điện động lực phản động động cơ (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Khoảng cách cực cặp N - N động cơ (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Số cặp cực động cơ</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Sai lệch góc điện (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Dòng điện pha U động cơ (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Dòng điện pha V động cơ (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Dòng điện pha W động cơ (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Điện áp bus (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Phiên bản ổ cắm - Mẫu chip</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Phiên bản ổ cắm - Phiên bản lớn</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Phiên bản ổ cắm - Cập nhật chức năng</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Phiên bản ổ cắm - Sửa lỗi</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Phiên bản ổ cắm - Đã phát hành (0 - Đang debug, 1 - Đã phát hành)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Kiểm soát sóng kế</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Kênh sóng kế 0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Kênh sóng kế 1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Kênh sóng kế 2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Kênh sóng kế 3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Kênh sóng kế 4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Kênh sóng kế 5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Kênh sóng kế 6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Kênh sóng kế 7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Loại encoder</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Độ phân giải encoder</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Phiên bản encoder - Phiên bản lớn</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Phiên bản encoder - Cập nhật chức năng</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Phiên bản encoder - Sửa lỗi</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Phiên bản encoder - Phiên bản debug</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Chọn chức năng debug encoder</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Giao diện debug vị trí encoder 0</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Giao diện debug vị trí encoder 1</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Ngưỡng phát hiện dòng điện quá lớn (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Thời gian xác định phát hiện dòng điện quá lớn (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Ngưỡng dòng điện xác định quá tải</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Dòng điện đỉnh cao quá tải</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Thời gian dòng điện đỉnh cao cho phép (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>Hệ số bù tăng I2t</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>Hệ số bù giảm I2t</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Ngưỡng dòng điện phát hiện động cơ bị kẹt (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Thời gian xác định động cơ bị kẹt (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Ngưỡng tốc độ xác định động cơ bị kẹt (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>Ngưỡng cảnh báo nhiệt độ MOS (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Ngưỡng cảnh báo số lần lỗi truyền encoder</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Ngưỡng phát hiện động cơ mất tốc độ (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Ngưỡng bảo vệ quá điện áp (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Ngưỡng bảo vệ dưới điện áp (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Vị trí lỗi mới nhất</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Chỉ số lỗi khi khởi động</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Lỗi lịch sử 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Lỗi lịch sử 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Lỗi lịch sử 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Lỗi lịch sử 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Lỗi lịch sử 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Lỗi lịch sử 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Lỗi lịch sử 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Lỗi lịch sử 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Lỗi lịch sử 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Lỗi lịch sử 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Lỗi lịch sử 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Lỗi lịch sử 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Lỗi lịch sử 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Lỗi lịch sử 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Lỗi lịch sử 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Lỗi lịch sử 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Lỗi lịch sử 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Lỗi lịch sử 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Lỗi lịch sử 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Lỗi lịch sử 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Từ điều khiển</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Từ trạng thái</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Trạng thái hoạt động</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Trạng thái thực tế</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Vị trí mục tiêu (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Vị trí thực tế (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Hệ số tỷ lệ vòng vị trí ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Hệ số tích phân vòng vị trí</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Hệ số vi phân vòng vị trí</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp lệnh vị trí (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Giới hạn UP đầu ra điều khiển vị trí (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Giới hạn LOW đầu ra điều khiển vị trí (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Tần số cắt bộ lọc trung bình lệnh vị trí (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Tốc độ mục tiêu (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Tốc độ thực tế (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Hệ số tỷ lệ vòng tốc độ (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Hệ số tích phân vòng tốc độ (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Hệ số vi phân vòng tốc độ (A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Hệ số chống bão hòa tích phân vòng tốc độ</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Hệ số lợi ích khuỷu tay tốc độ</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp khuỷu tay tốc độ (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp phản hồi tốc độ (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp lệnh tốc độ (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Giới hạn UP đầu ra điều khiển tốc độ (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Giới hạn LOW đầu ra điều khiển tốc độ (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Giá trị mục tiêu dòng điện trục Q (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>Giá trị mục tiêu dòng điện trục D (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Giá trị phản hồi dòng điện trục Q (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>Giá trị phản hồi dòng điện trục D (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Hệ số tỷ lệ vòng dòng điện</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Hệ số tích phân vòng dòng điện</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Hệ số vi phân vòng dòng điện</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>Hệ số bù điện động lực phản động trục D</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Hệ số bù điện động lực phản động trục Q</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Hệ số bù điện động lực phản động nam châm vĩnh cửu</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp phản hồi dòng điện (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp lệnh dòng điện (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Hệ số lợi ích khuỷu tay dòng điện</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Tần số cắt bộ lọc thông thấp khuỷu tay dòng điện (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Tần số trung tâm bộ lọc câm lệnh dòng điện (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Bánh rộng bộ lọc câm lệnh dòng điện (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Độ sâu bộ lọc câm lệnh dòng điện (dB)</value>
    </data>
</root>