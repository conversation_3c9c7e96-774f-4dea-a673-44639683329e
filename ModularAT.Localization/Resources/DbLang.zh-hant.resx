<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>線上配置</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>配置生成</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>操作日誌</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>數據追溯</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>採集</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>故障記錄清除</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>錯誤復位</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>恢復默認參數</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>全部寫入</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>選擇寫入</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>執行</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>執行</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>停止</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>執行</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>權限分配</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>菜單</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>用戶</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>角色</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>示波器</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>伺服配置</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>系統控制</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>接駁狀態</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>軸控制</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>系統總成</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>控制器連接</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>驅動器連接</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>基本設置</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>驅動器</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>控制器</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>設備連接</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>電機線電阻(mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>電機線電感(mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>電機額定電流(Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>電機額定力矩(N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>電機峰值電流(Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>電機力矩常數(N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>電機反電動勢係數(V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>電機極對N-N距(mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>電機極對數</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>電角度偏移(PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>電機U相電流(A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>電機V相電流(A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>電機W相電流(A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>母線電壓(V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>驅動版本-芯片型號</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>驅動版本-大版本迭代</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>驅動版本-功能迭代</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>驅動版本-Bug迭代</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>驅動版本-調試發布(0-調試 1-發布)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>示波器控制</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>示波器通道0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>示波器通道1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>示波器通道2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>示波器通道3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>示波器通道4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>示波器通道5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>示波器通道6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>示波器通道7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>編碼器類型</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>編碼器分辨率</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>編碼器版本-大版本迭代</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>編碼器版本-功能迭代</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>編碼器版本-Bug迭代</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>編碼器版本-調試版本</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>編碼器調試功能選擇</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>編碼器0位置調試接口</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>編碼器1位置調試接口</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>過流檢測閾值(A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>過流檢測判定時間(ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>過載判定電流閾值</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>過載峰值電流</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>允許峰值電流持續時間(ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t增加補償係數</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t減少補償係數</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>電機堵轉檢測電流閾值(A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>電機堵轉判定時間(ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>電機堵轉速度判定閾值(mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS溫度報警閾值(℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>編碼器通訊錯誤次數報警閾值</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>電機失速檢測閾值(mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>過壓保護閾值(V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>欠壓保護閾值(V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>最新錯誤位置</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>開機時錯誤索引</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>歷史錯誤0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>歷史錯誤1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>歷史錯誤2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>歷史錯誤3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>歷史錯誤4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>歷史錯誤5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>歷史錯誤6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>歷史錯誤7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>歷史錯誤8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>歷史錯誤9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>歷史錯誤10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>歷史錯誤11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>歷史錯誤12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>歷史錯誤13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>歷史錯誤14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>歷史錯誤15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>歷史錯誤16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>歷史錯誤17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>歷史錯誤18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>歷史錯誤19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>控制字</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>狀態字</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>運行狀態</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>實際狀態</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>目標位置(PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>實際位置(PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>位置環比例係數((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>位置環積分係數</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>位置環微分係數</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>位置指令低通濾波截止頻率(Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>位置控制輸出鉗位UP(mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>位置控制輸出鉗位LOW(mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>位置指令均值濾波器截止頻率(Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>目標速度(mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>實際速度(mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>速度環比例係數(A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>速度環積分係數(A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>速度環微分係數(A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>速度環抗積分飽和係數</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>速度前饋增益係數</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>速度前饋低通濾波截止頻率(Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>速度反饋低通濾波截止頻率(Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>速度指令低通濾波截止頻率(Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>速度控制輸出鉗位UP(A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>速度控制輸出鉗位LOW(A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q軸電流目標值(A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D軸電流目標值(A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q軸電流反饋值(A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D軸電流反饋值(A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>電流環比例係數</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>電流環積分係數</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>電流環微分係數</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D軸反電勢補償係數</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q軸反電勢補償係數</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>永磁體反電勢補償係數</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>電流反饋低通濾波截止頻率(Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>電流指令低通濾波截止頻率(Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>電流前饋增益係數</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>電流前饋低通濾波截止頻率(Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>電流指令陷波濾波中心頻率(Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>電流指令陷波濾波帶寬(Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>電流指令陷波濾波深度(dB)</value>
    </data>
</root>