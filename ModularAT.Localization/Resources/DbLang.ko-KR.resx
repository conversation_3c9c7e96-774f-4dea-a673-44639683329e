<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>온라인 구성</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>구성 생성</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>작업 로그</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>데이터 추적</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>수집</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>고장 기록 삭제</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>오류 리셋</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>기본 매개변수 복원</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>전체 쓰기</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>선택적 쓰기</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>실행</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>실행</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>중지</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>실행</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>권한 할당</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>메뉴</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>사용자</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>역할</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>오실로스코프</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>서보 구성</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>시스템 제어</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>접속 상태</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>축 제어</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>시스템 조립</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>컨트롤러 연결</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>드라이버 연결</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>기본 설정</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>드라이버</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>컨트롤러</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>장치 연결</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>모터 와이어 저항(mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>모터 와이어 인덕턴스(mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>모터 정격 전류(Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>모터 정격 토크(N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>모터 피크 전류(Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>모터 토크 상수(N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>모터 역기전력 계수(V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>모터 극 쌍 N-N 거리(mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>모터 극 쌍 수</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>전기적 각도 오프셋(PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>모터 U상 전류(A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>모터 V상 전류(A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>모터 W상 전류(A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>버스 전압(V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>드라이버 버전 - 칩 모델</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>드라이버 버전 - 주요 버전 반복</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>드라이버 버전 - 기능 반복</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>드라이버 버전 - 버그 반복</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>드라이버 버전 - 디버그/릴리스(0 - 디버그, 1 - 릴리스)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>오실로스코프 제어</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>오실로스코프 채널 0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>오실로스코프 채널 1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>오실로스코프 채널 2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>오실로스코프 채널 3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>오실로스코프 채널 4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>오실로스코프 채널 5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>오실로스코프 채널 6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>오실로스코프 채널 7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>인코더 유형</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>인코더 분해능</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>인코더 버전 - 주요 버전 반복</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>인코더 버전 - 기능 반복</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>인코더 버전 - 버그 반복</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>인코더 버전 - 디버그 버전</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>인코더 디버그 기능 선택</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>인코더 0 위치 디버그 인터페이스</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>인코더 1 위치 디버그 인터페이스</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>과전류 검출 임계값(A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>과전류 검출 판단 시간(ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>과부하 판단 전류 임계값</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>과부하 피크 전류</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>허용 피크 전류 지속 시간(ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t 증가 보상 계수</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t 감소 보상 계수</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>모터 체결 검출 전류 임계값(A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>모터 체결 판단 시간(ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>모터 체결 속도 판단 임계값(mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS 온도 경보 임계값(℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>인코더 통신 오류 횟수 경보 임계값</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>모터 실속 검출 임계값(mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>과전압 보호 임계값(V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>저전압 보호 임계값(V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>최신 오류 위치</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>부팅 시 오류 인덱스</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>과거 오류 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>과거 오류 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>과거 오류 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>과거 오류 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>과거 오류 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>과거 오류 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>과거 오류 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>과거 오류 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>과거 오류 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>과거 오류 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>과거 오류 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>과거 오류 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>과거 오류 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>과거 오류 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>과거 오류 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>과거 오류 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>과거 오류 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>과거 오류 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>과거 오류 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>과거 오류 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>제어 워드</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>상태 워드</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>운영 상태</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>실제 상태</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>목표 위치(PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>실제 위치(PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>위치 루프 비례 계수((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>위치 루프 적분 계수</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>위치 루프 미분 계수</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>위치 명령 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>위치 제어 출력 클램프 UP(mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>위치 제어 출력 클램프 LOW(mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>위치 명령 평균 필터 차단 주파수(Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>목표 속도(mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>실제 속도(mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>속도 루프 비례 계수(A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>속도 루프 적분 계수(A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>속도 루프 미분 계수(A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>속도 루프 적분 포화 방지 계수</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>속도 피드 포워드 이득 계수</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>속도 피드 포워드 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>속도 피드백 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>속도 명령 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>속도 제어 출력 클램프 UP(A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>속도 제어 출력 클램프 LOW(A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q축 전류 목표 값(A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D축 전류 목표 값(A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q축 전류 피드백 값(A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D축 전류 피드백 값(A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>전류 루프 비례 계수</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>전류 루프 적분 계수</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>전류 루프 미분 계수</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D축 역기전력 보상 계수</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q축 역기전력 보상 계수</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>영구자석 역기전력 보상 계수</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>전류 피드백 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>전류 명령 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>전류 피드 포워드 이득 계수</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>전류 피드 포워드 저역 통과 필터 차단 주파수(Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>전류 명령 노치 필터 중심 주파수(Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>전류 명령 노치 필터 대역폭(Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>전류 명령 노치 필터 깊이(dB)</value>
    </data>
</root>