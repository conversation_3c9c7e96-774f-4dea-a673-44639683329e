//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ModularAT.Localization.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class DbLang {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal DbLang() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ModularAT.Localization.Resources.DbLang", typeof(DbLang).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 基本设置.
        /// </summary>
        internal static string _Base {
            get {
                return ResourceManager.GetString("/Base", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 权限分配.
        /// </summary>
        internal static string _BasePermAssign {
            get {
                return ResourceManager.GetString("/BasePermAssign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 菜单.
        /// </summary>
        internal static string _BasePermission {
            get {
                return ResourceManager.GetString("/BasePermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色.
        /// </summary>
        internal static string _BaseRole {
            get {
                return ResourceManager.GetString("/BaseRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 用户.
        /// </summary>
        internal static string _BaseUser {
            get {
                return ResourceManager.GetString("/BaseUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴控制.
        /// </summary>
        internal static string _ControlerAxis {
            get {
                return ResourceManager.GetString("/ControlerAxis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 执行.
        /// </summary>
        internal static string _ControlerAxis_Execute {
            get {
                return ResourceManager.GetString("/ControlerAxis/Execute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 停止.
        /// </summary>
        internal static string _ControlerAxis_Stop {
            get {
                return ResourceManager.GetString("/ControlerAxis/Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器连接.
        /// </summary>
        internal static string _ControlerClient {
            get {
                return ResourceManager.GetString("/ControlerClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置生成.
        /// </summary>
        internal static string _ControlerGenerateConfig {
            get {
                return ResourceManager.GetString("/ControlerGenerateConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在线配置.
        /// </summary>
        internal static string _ControlerOnlineConfig {
            get {
                return ResourceManager.GetString("/ControlerOnlineConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统控制.
        /// </summary>
        internal static string _ControlerSys {
            get {
                return ResourceManager.GetString("/ControlerSys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 执行.
        /// </summary>
        internal static string _ControlerSys_Execute {
            get {
                return ResourceManager.GetString("/ControlerSys/Execute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接驳状态.
        /// </summary>
        internal static string _ControlerTranStatus {
            get {
                return ResourceManager.GetString("/ControlerTranStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 执行.
        /// </summary>
        internal static string _ControlerTranStatus_Execute {
            get {
                return ResourceManager.GetString("/ControlerTranStatus/Execute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器.
        /// </summary>
        internal static string _Controller {
            get {
                return ResourceManager.GetString("/Controller", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数据追溯.
        /// </summary>
        internal static string _DataTrace {
            get {
                return ResourceManager.GetString("/DataTrace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作日志.
        /// </summary>
        internal static string _DataTrace_OperateLog {
            get {
                return ResourceManager.GetString("/DataTrace/OperateLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设备连接.
        /// </summary>
        internal static string _Devices {
            get {
                return ResourceManager.GetString("/Devices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器.
        /// </summary>
        internal static string _Scope {
            get {
                return ResourceManager.GetString("/Scope", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 采集.
        /// </summary>
        internal static string _Scope_StartRun {
            get {
                return ResourceManager.GetString("/Scope/StartRun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器.
        /// </summary>
        internal static string _Servo {
            get {
                return ResourceManager.GetString("/Servo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器连接.
        /// </summary>
        internal static string _ServoSerialPort {
            get {
                return ResourceManager.GetString("/ServoSerialPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 伺服配置.
        /// </summary>
        internal static string _ServoSetting {
            get {
                return ResourceManager.GetString("/ServoSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 故障记录清除.
        /// </summary>
        internal static string _ServoSetting_ErrorRecordClear {
            get {
                return ResourceManager.GetString("/ServoSetting/ErrorRecordClear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 错误复位.
        /// </summary>
        internal static string _ServoSetting_ErrorReset {
            get {
                return ResourceManager.GetString("/ServoSetting/ErrorReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 恢复默认参数.
        /// </summary>
        internal static string _ServoSetting_ParaClear {
            get {
                return ResourceManager.GetString("/ServoSetting/ParaClear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择写入.
        /// </summary>
        internal static string _ServoSetting_SetPara {
            get {
                return ResourceManager.GetString("/ServoSetting/SetPara", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 全部写入.
        /// </summary>
        internal static string _ServoSetting_SetParamsAll {
            get {
                return ResourceManager.GetString("/ServoSetting/SetParamsAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统总成.
        /// </summary>
        internal static string _Simulation {
            get {
                return ResourceManager.GetString("/Simulation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 实际位置(PosUnit).
        /// </summary>
        internal static string Actual_Position {
            get {
                return ResourceManager.GetString("Actual_Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 实际速度(mm/s).
        /// </summary>
        internal static string Actual_Velocity {
            get {
                return ResourceManager.GetString("Actual_Velocity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机反电动势系数(V(pk)/m/s).
        /// </summary>
        internal static string Back_Emf_Coeff {
            get {
                return ResourceManager.GetString("Back_Emf_Coeff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 母线电压(V).
        /// </summary>
        internal static string Bus_Voltage {
            get {
                return ResourceManager.GetString("Bus_Voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流指令低通滤波截止频率(Hz).
        /// </summary>
        internal static string CILP_CutFreq {
            get {
                return ResourceManager.GetString("CILP_CutFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流指令陷波滤波带宽(Hz).
        /// </summary>
        internal static string CINF_CutFreq {
            get {
                return ResourceManager.GetString("CINF_CutFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流指令陷波滤波深度(dB).
        /// </summary>
        internal static string CINF_Depth {
            get {
                return ResourceManager.GetString("CINF_Depth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流指令陷波滤波中心频率(Hz).
        /// </summary>
        internal static string CINF_NotchFreq {
            get {
                return ResourceManager.GetString("CINF_NotchFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制字.
        /// </summary>
        internal static string ControlWord {
            get {
                return ResourceManager.GetString("ControlWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I2t减少补偿系数.
        /// </summary>
        internal static string Cool_Coeff {
            get {
                return ResourceManager.GetString("Cool_Coeff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流反馈低通滤波截止频率(Hz).
        /// </summary>
        internal static string Cur_FB_CutFreq {
            get {
                return ResourceManager.GetString("Cur_FB_CutFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流前馈增益系数.
        /// </summary>
        internal static string Cur_FF_Gain {
            get {
                return ResourceManager.GetString("Cur_FF_Gain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流前馈低通滤波截止频率(Hz).
        /// </summary>
        internal static string Cur_FFLPF_CutFreq {
            get {
                return ResourceManager.GetString("Cur_FFLPF_CutFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流环微分系数.
        /// </summary>
        internal static string Current_Kd {
            get {
                return ResourceManager.GetString("Current_Kd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D轴反电势补偿系数.
        /// </summary>
        internal static string Current_Ke_D {
            get {
                return ResourceManager.GetString("Current_Ke_D", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Q轴反电势补偿系数.
        /// </summary>
        internal static string Current_Ke_Q {
            get {
                return ResourceManager.GetString("Current_Ke_Q", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 永磁体反电势补偿系数.
        /// </summary>
        internal static string Current_Kf {
            get {
                return ResourceManager.GetString("Current_Kf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流环积分系数.
        /// </summary>
        internal static string Current_Ki {
            get {
                return ResourceManager.GetString("Current_Ki", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流环比例系数.
        /// </summary>
        internal static string Current_Kp {
            get {
                return ResourceManager.GetString("Current_Kp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器类型.
        /// </summary>
        internal static string DRIVER_VERSION_0 {
            get {
                return ResourceManager.GetString("DRIVER_VERSION_0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动版本-大版本迭代.
        /// </summary>
        internal static string DRIVER_VERSION_1 {
            get {
                return ResourceManager.GetString("DRIVER_VERSION_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动版本-功能迭代.
        /// </summary>
        internal static string DRIVER_VERSION_2 {
            get {
                return ResourceManager.GetString("DRIVER_VERSION_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动版本-Bug迭代.
        /// </summary>
        internal static string DRIVER_VERSION_3 {
            get {
                return ResourceManager.GetString("DRIVER_VERSION_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动版本-调试发布(0-调试 1-发布).
        /// </summary>
        internal static string DRIVER_VERSION_4 {
            get {
                return ResourceManager.GetString("DRIVER_VERSION_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 允许峰值电流持续时间(ms).
        /// </summary>
        internal static string Dur_Of_PeakCur {
            get {
                return ResourceManager.GetString("Dur_Of_PeakCur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电角度偏移(PosUnit).
        /// </summary>
        internal static string Elec_Offset {
            get {
                return ResourceManager.GetString("Elec_Offset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机极对N-N距(mm).
        /// </summary>
        internal static string Electrode_Distance {
            get {
                return ResourceManager.GetString("Electrode_Distance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器调试功能选择.
        /// </summary>
        internal static string EncDebugFunc {
            get {
                return ResourceManager.GetString("EncDebugFunc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器通讯错误次数报警阈值.
        /// </summary>
        internal static string Encoder_Commu_Err {
            get {
                return ResourceManager.GetString("Encoder_Commu_Err", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器0位置调试接口.
        /// </summary>
        internal static string EncoderPos0 {
            get {
                return ResourceManager.GetString("EncoderPos0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器1位置调试接口.
        /// </summary>
        internal static string EncoderPos1 {
            get {
                return ResourceManager.GetString("EncoderPos1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器分辨率.
        /// </summary>
        internal static string EncoderResolution {
            get {
                return ResourceManager.GetString("EncoderResolution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器类型.
        /// </summary>
        internal static string EncoderType {
            get {
                return ResourceManager.GetString("EncoderType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器版本-Bug迭代.
        /// </summary>
        internal static string EncVersion_Bug {
            get {
                return ResourceManager.GetString("EncVersion_Bug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器版本-调试版本.
        /// </summary>
        internal static string EncVersion_Debug {
            get {
                return ResourceManager.GetString("EncVersion_Debug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器版本-功能迭代.
        /// </summary>
        internal static string EncVersion_Func {
            get {
                return ResourceManager.GetString("EncVersion_Func", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编码器版本-大版本迭代.
        /// </summary>
        internal static string EncVersion_Master {
            get {
                return ResourceManager.GetString("EncVersion_Master", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I2t增加补偿系数.
        /// </summary>
        internal static string Heat_Coeff {
            get {
                return ResourceManager.GetString("Heat_Coeff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误0.
        /// </summary>
        internal static string His_Err_Code0 {
            get {
                return ResourceManager.GetString("His_Err_Code0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误1.
        /// </summary>
        internal static string His_Err_Code1 {
            get {
                return ResourceManager.GetString("His_Err_Code1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误10.
        /// </summary>
        internal static string His_Err_Code10 {
            get {
                return ResourceManager.GetString("His_Err_Code10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误11.
        /// </summary>
        internal static string His_Err_Code11 {
            get {
                return ResourceManager.GetString("His_Err_Code11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误12.
        /// </summary>
        internal static string His_Err_Code12 {
            get {
                return ResourceManager.GetString("His_Err_Code12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误13.
        /// </summary>
        internal static string His_Err_Code13 {
            get {
                return ResourceManager.GetString("His_Err_Code13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误14.
        /// </summary>
        internal static string His_Err_Code14 {
            get {
                return ResourceManager.GetString("His_Err_Code14", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误15.
        /// </summary>
        internal static string His_Err_Code15 {
            get {
                return ResourceManager.GetString("His_Err_Code15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误16.
        /// </summary>
        internal static string His_Err_Code16 {
            get {
                return ResourceManager.GetString("His_Err_Code16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误17.
        /// </summary>
        internal static string His_Err_Code17 {
            get {
                return ResourceManager.GetString("His_Err_Code17", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误18.
        /// </summary>
        internal static string His_Err_Code18 {
            get {
                return ResourceManager.GetString("His_Err_Code18", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误19.
        /// </summary>
        internal static string His_Err_Code19 {
            get {
                return ResourceManager.GetString("His_Err_Code19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误2.
        /// </summary>
        internal static string His_Err_Code2 {
            get {
                return ResourceManager.GetString("His_Err_Code2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误3.
        /// </summary>
        internal static string His_Err_Code3 {
            get {
                return ResourceManager.GetString("His_Err_Code3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误4.
        /// </summary>
        internal static string His_Err_Code4 {
            get {
                return ResourceManager.GetString("His_Err_Code4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误5.
        /// </summary>
        internal static string His_Err_Code5 {
            get {
                return ResourceManager.GetString("His_Err_Code5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误6.
        /// </summary>
        internal static string His_Err_Code6 {
            get {
                return ResourceManager.GetString("His_Err_Code6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误7.
        /// </summary>
        internal static string His_Err_Code7 {
            get {
                return ResourceManager.GetString("His_Err_Code7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误8.
        /// </summary>
        internal static string His_Err_Code8 {
            get {
                return ResourceManager.GetString("His_Err_Code8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 历史错误9.
        /// </summary>
        internal static string His_Err_Code9 {
            get {
                return ResourceManager.GetString("His_Err_Code9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D轴电流目标值(A).
        /// </summary>
        internal static string Id_CMD {
            get {
                return ResourceManager.GetString("Id_CMD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D轴电流反馈值(A).
        /// </summary>
        internal static string Id_FB {
            get {
                return ResourceManager.GetString("Id_FB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Q轴电流目标值(A).
        /// </summary>
        internal static string Iq_CMD {
            get {
                return ResourceManager.GetString("Iq_CMD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Q轴电流反馈值(A).
        /// </summary>
        internal static string Iq_FB {
            get {
                return ResourceManager.GetString("Iq_FB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机线电感(mH).
        /// </summary>
        internal static string LL_Inductance {
            get {
                return ResourceManager.GetString("LL_Inductance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机线电阻(mΩ).
        /// </summary>
        internal static string LL_Resistance {
            get {
                return ResourceManager.GetString("LL_Resistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机堵转检测电流阈值(A).
        /// </summary>
        internal static string Locked_rotor_Current {
            get {
                return ResourceManager.GetString("Locked_rotor_Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机堵转判定时间(ms).
        /// </summary>
        internal static string Locked_rotor_Time {
            get {
                return ResourceManager.GetString("Locked_rotor_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机堵转速度判定阈值(mm/s).
        /// </summary>
        internal static string Locked_rotor_Vel {
            get {
                return ResourceManager.GetString("Locked_rotor_Vel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 运行状态.
        /// </summary>
        internal static string ModeOfOperation {
            get {
                return ResourceManager.GetString("ModeOfOperation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 实际状态.
        /// </summary>
        internal static string ModesOfOperationDisplay {
            get {
                return ResourceManager.GetString("ModesOfOperationDisplay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MOS温度报警阈值(℃).
        /// </summary>
        internal static string MOS_Temp {
            get {
                return ResourceManager.GetString("MOS_Temp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最新错误位置.
        /// </summary>
        internal static string New_ErrIndex {
            get {
                return ResourceManager.GetString("New_ErrIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机极对数.
        /// </summary>
        internal static string Number_Of_Poles {
            get {
                return ResourceManager.GetString("Number_Of_Poles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 过流检测阈值(A).
        /// </summary>
        internal static string OCD_Threshold {
            get {
                return ResourceManager.GetString("OCD_Threshold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 过流检测判定时间(ms).
        /// </summary>
        internal static string OCD_Time {
            get {
                return ResourceManager.GetString("OCD_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 过载峰值电流.
        /// </summary>
        internal static string OLD_PeakCur {
            get {
                return ResourceManager.GetString("OLD_PeakCur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 过载判定电流阈值.
        /// </summary>
        internal static string OLD_RateCur {
            get {
                return ResourceManager.GetString("OLD_RateCur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 过压保护阈值(V).
        /// </summary>
        internal static string Over_Voltage {
            get {
                return ResourceManager.GetString("Over_Voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机峰值电流(Arms).
        /// </summary>
        internal static string Peak_Current {
            get {
                return ResourceManager.GetString("Peak_Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置指令低通滤波截止频率(Hz).
        /// </summary>
        internal static string PILF_Cutoff_Freq {
            get {
                return ResourceManager.GetString("PILF_Cutoff_Freq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置指令均值滤波器截止频率(Hz).
        /// </summary>
        internal static string PISA_Cutoff {
            get {
                return ResourceManager.GetString("PISA_Cutoff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置控制输出钳位LOW(mm/s).
        /// </summary>
        internal static string PosCtrl_ClamLow {
            get {
                return ResourceManager.GetString("PosCtrl_ClamLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置控制输出钳位UP(mm/s).
        /// </summary>
        internal static string PosCtrl_ClamUp {
            get {
                return ResourceManager.GetString("PosCtrl_ClamUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置环微分系数.
        /// </summary>
        internal static string Position_Kd {
            get {
                return ResourceManager.GetString("Position_Kd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置环积分系数.
        /// </summary>
        internal static string Position_Ki {
            get {
                return ResourceManager.GetString("Position_Ki", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置环比例系数((mm/s)/PosUnit).
        /// </summary>
        internal static string Position_Kp {
            get {
                return ResourceManager.GetString("Position_Kp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 开机时错误索引.
        /// </summary>
        internal static string Pre_ErrIndex {
            get {
                return ResourceManager.GetString("Pre_ErrIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机额定电流(Arms).
        /// </summary>
        internal static string Rate_Current {
            get {
                return ResourceManager.GetString("Rate_Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机额定力矩(N).
        /// </summary>
        internal static string Rate_Torque {
            get {
                return ResourceManager.GetString("Rate_Torque", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器控制.
        /// </summary>
        internal static string ScopeCtl {
            get {
                return ResourceManager.GetString("ScopeCtl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道0.
        /// </summary>
        internal static string ScopeMapList0 {
            get {
                return ResourceManager.GetString("ScopeMapList0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道1.
        /// </summary>
        internal static string ScopeMapList1 {
            get {
                return ResourceManager.GetString("ScopeMapList1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道2.
        /// </summary>
        internal static string ScopeMapList2 {
            get {
                return ResourceManager.GetString("ScopeMapList2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道3.
        /// </summary>
        internal static string ScopeMapList3 {
            get {
                return ResourceManager.GetString("ScopeMapList3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道4.
        /// </summary>
        internal static string ScopeMapList4 {
            get {
                return ResourceManager.GetString("ScopeMapList4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道5.
        /// </summary>
        internal static string ScopeMapList5 {
            get {
                return ResourceManager.GetString("ScopeMapList5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道6.
        /// </summary>
        internal static string ScopeMapList6 {
            get {
                return ResourceManager.GetString("ScopeMapList6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器通道7.
        /// </summary>
        internal static string ScopeMapList7 {
            get {
                return ResourceManager.GetString("ScopeMapList7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机失速检测阈值(mm/s).
        /// </summary>
        internal static string Stall_Dect {
            get {
                return ResourceManager.GetString("Stall_Dect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状态字.
        /// </summary>
        internal static string StatusWord {
            get {
                return ResourceManager.GetString("StatusWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标位置(PosUnit).
        /// </summary>
        internal static string Target_Position {
            get {
                return ResourceManager.GetString("Target_Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标速度(mm/s).
        /// </summary>
        internal static string Target_Velocity {
            get {
                return ResourceManager.GetString("Target_Velocity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机力矩常数(N/Arms).
        /// </summary>
        internal static string Torque_Constant {
            get {
                return ResourceManager.GetString("Torque_Constant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机U相电流(A).
        /// </summary>
        internal static string U_Current {
            get {
                return ResourceManager.GetString("U_Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 欠压保护阈值(V).
        /// </summary>
        internal static string Under_Voltage {
            get {
                return ResourceManager.GetString("Under_Voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机V相电流(A).
        /// </summary>
        internal static string V_Current {
            get {
                return ResourceManager.GetString("V_Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度反馈低通滤波截止频率(Hz).
        /// </summary>
        internal static string Vel_FBLPF_CutFreq {
            get {
                return ResourceManager.GetString("Vel_FBLPF_CutFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度前馈增益系数.
        /// </summary>
        internal static string Vel_FF_Gain {
            get {
                return ResourceManager.GetString("Vel_FF_Gain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度前馈低通滤波截止频率(Hz).
        /// </summary>
        internal static string Vel_FFLPF_CutFreq {
            get {
                return ResourceManager.GetString("Vel_FFLPF_CutFreq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度控制输出钳位LOW(A).
        /// </summary>
        internal static string VelCtrl_ClamLow {
            get {
                return ResourceManager.GetString("VelCtrl_ClamLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度控制输出钳位UP(A).
        /// </summary>
        internal static string VelCtrl_ClamUp {
            get {
                return ResourceManager.GetString("VelCtrl_ClamUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度环抗积分饱和系数.
        /// </summary>
        internal static string Velocity_Kc {
            get {
                return ResourceManager.GetString("Velocity_Kc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度环微分系数(A/(mm/s^2)).
        /// </summary>
        internal static string Velocity_Kd {
            get {
                return ResourceManager.GetString("Velocity_Kd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度环积分系数(A/mm).
        /// </summary>
        internal static string Velocity_Ki {
            get {
                return ResourceManager.GetString("Velocity_Ki", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度环比例系数(A/(mm/s)).
        /// </summary>
        internal static string Velocity_Kp {
            get {
                return ResourceManager.GetString("Velocity_Kp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度指令低通滤波截止频率(Hz).
        /// </summary>
        internal static string VILP_Cutoff_Freq {
            get {
                return ResourceManager.GetString("VILP_Cutoff_Freq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机W相电流(A).
        /// </summary>
        internal static string W_Current {
            get {
                return ResourceManager.GetString("W_Current", resourceCulture);
            }
        }
    }
}
