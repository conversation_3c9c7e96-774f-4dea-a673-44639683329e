<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Connection Disconnected</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Connection Successful</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Initialize</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>Emergency</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Axis Error Reset</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>Restart System</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Station Initialization</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Enable Station</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Mask Station</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Fault</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Maintenance</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>Running</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Equipment Connection</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Driver</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Controller</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Plaintext Message</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Firmware Upgrade</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Offline Configuration</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>System Assembly</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Axis Control</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>Connection Status</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Station Control</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>System Control</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Feedback Information</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Error and Fault</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Online Configuration</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Deviation Compensation</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>Reciprocating in Curve Strength</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Configuration Generation</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>Digital IO</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Servo Configuration</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Oscilloscope</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Basic Settings</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Role Management</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>User Management</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Function List</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Permission Assignment</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Data Tracing</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Operation Log</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Select Axis Serial Number:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Driver Connection:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Controller Connection:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Mover Axis Control</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Axis Motion Mode:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Jog Motion</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Absolute Motion</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Relative Motion</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Station Motion</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>Axis ID:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Axis Type:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Mover</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Rotary Motor</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Linear Motor</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Speed Mode:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Axis Control Mode:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>Target Line ID:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>Target Station ID:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Speed:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Acceleration:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Deceleration:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Jerk:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Positioning Accuracy:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Anti - collision Accuracy:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Target Position:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Select Operation:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Execute</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Object the Axis Belongs to</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Line the Axis Belongs to</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Drive Error</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Axis Error</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Current Axis Position (mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Current Axis Speed</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>Current Axis Status</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Controller Connection</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Connect</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Disconnect</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Send:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Log:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Configuration Generation</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Number of System Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Number of Motor Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Number of Slave Node Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Number of Line Segment Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Number of Station Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Number of Mover Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Number of Rotary Axis Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Number of IO Configurations:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Generate Configuration File</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Online Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Select Configuration:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>System Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Station Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Write</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Parameter Name</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Setting Type</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Read Value</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Set Value</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>System Control</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Control Object:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Mover</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Rotary Motor</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Linear Motor</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>System Operating Mode:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Axis Teaching</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Connection Teaching</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Automatic Operation</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Automatic Operating Mode:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Synchronous</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Asynchronous</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Speed Percentage:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>Slave Node ID:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Control Mode:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Select Operation:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Execute</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>System Error Axis ID</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>System Error Drive</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>System Error Code</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>System Error Number</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>System Status</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Connection Control</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Connection Configuration:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>Current Object ID:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>Left Object ID:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>Connection Status:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Disconnect</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Establish Connection</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>Right Object ID:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Select Operation:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Execute</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Read</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>Connection ID:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>Target Station ID:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Speed:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Acceleration:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Deceleration:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Target Position:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Control Command:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>Line ID</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>Left Connection Object ID of the Line</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>Right Connection Object ID of the Line</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>Enable Status</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>Running Status</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Homing Completed</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Error Code</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Actual Speed</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Actual Position</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Remember Password</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Please Enter Keywords</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Start Time: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Behavior</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Operator</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Detailed Description:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Collect</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Crosshair</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>X - axis Scale</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Y - axis Scale</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Error Check</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Sampling Frequency (1 - 300, unit ms):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Channel</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Select Object</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Please Select</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>Visibility</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Magnification</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Debugging</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Driver Connection</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Serial Port</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Baud Rate</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Data Bits</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Parity Bit</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Stop Bits</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Connect</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Disconnect</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Driver Parameters</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Select Operation:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Select Write</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Write All</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Restore Default Parameters</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Error Reset</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Fault Record Clearance</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Drive Mode Setting:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Control Right:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Local Control Mode:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Sub - mode:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Parameter Name</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Setting Type</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Minimum Value</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Maximum Value</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Read Value</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Set Value</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Permission</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Coefficient</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Monitor</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Role:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Permission:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Please Enter Keywords</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Binding Code</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>Is Button</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>Is Hidden</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Button Event</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Creator</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Creation Time</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Modification Time</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Menu Name:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Parent Menu:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Please Enter Keywords</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Role Name</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Creator</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Creation Time</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Modification Time</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>Is Enabled</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>The smaller the priority, the greater the permission</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>Is the current role enabled:</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Please Enter Keywords</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Real Name</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Remark</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Creation Time</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Modification Time</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Last Login</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Login Name:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Password:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>Pending Enablement</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>You do not have permission for this menu</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI Thread:</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>UI Thread Exception:</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>UI Thread Fatal Error Occurred!</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Fatal Error Occurred in Non-UI Thread</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Non-UI Thread Exception:</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task Thread:</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Task Thread Exception:</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Main Thread</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>Switch</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>You Do Not Have Permission for This Operation</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>Single Axis Enable Status</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>Single Axis Running Status</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>Single Axis Alarm Status</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>Single Axis Error Status</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Single Axis Left Collision</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Single Axis Right Collision</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Single Axis Positive Limit</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Single Axis Negative Limit</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>Single Axis on the Workstation</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>Single Axis Has Reached the Target Position</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>The System Is Ready</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>System Enable Status</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>System Error Status</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>System Running Status</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>System Bus Status</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>System Platform Verification Status</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Axis Configuration Completed, Axis Sequence Initialization Can Be Performed</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Motion Parameter Configuration Completed, System Old State Restoration Can Be Performed</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>System Restoration of Old State Completed</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: Reserved\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【SQL Statement】:</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【SQL Parameters】:</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>The Input Value Must Be within the Specified Range</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Root Node</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Get Success</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Root Node</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Get Success</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Add Success</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Update Success</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Delete Success</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Get Success</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Add Success</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Update Success</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Delete Success</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Jog Forward Movement</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Jog Reverse Movement</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Absolute Movement</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Relative Movement</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Workstation Movement</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Set Zero Point</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Axis Reset</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Configuration File Generation Success</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Workstation Configuration Distribution Success</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Do Nothing</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>Controller Connection Disconnected!</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>Controller Connection Success!</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>Controller Feedback Axis Number Is 0, This Operation Cannot Be Performed!</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>No Control</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Dual Axis Position Control</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Axis 0 Electrical Angle Identification</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>DC Sampling Test</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>AC Sampling Test</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>CSV File (*.csv)|*.csv|All Files (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Please Select a CSV File</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Please Select the Save Path</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Data Export Success</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>The Passed Object Cannot Be Empty!</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>Newly Appended Content</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>This Is What I Wrote!</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>The Corresponding Directory Does Not Exist</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Button</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Send Data:</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>Adapter Parsing Data Failed!</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>Controller Not Connected!</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Controller Heartbeat Send Failed</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Upper Enable</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Lower Enable</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Set Zero Point</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Forward Jog</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Backward Jog</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Absolute Movement</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Relative Movement</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Workstation Movement</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Upper Enable</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Lower Enable</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Error Reset</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Pause</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Emergency Stop</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>The Control Object Has Been Removed from the Protocol, Do Not Use This Attribute</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>The Control Object Has Been Removed from the Protocol, Do Not Use This Attribute</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Position Parameter</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Axis 0 Position Feedback</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Axis 1 Position Feedback</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Speed Parameter</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>Axis 0 Speed Instruction</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Axis 0 Speed Feedback</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>Axis 1 Speed Instruction</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Axis 1 Speed Feedback</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Current Parameter</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>Axis 0 Current Instruction</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Axis 0 Current Feedback</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>Axis 1 Current Instruction</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Axis 1 Current Feedback</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Voltage Parameter</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>Axis 0 D-axis Reference Voltage</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>Axis 1 D-axis Reference Voltage</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>Axis 0 Q-axis Reference Voltage</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>Axis 1 Q-axis Reference Voltage</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>Axis 0 Bus Voltage</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>Axis 1 Bus Voltage</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>Axis 0_U-phase Current</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>Axis 1_U-phase Current</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>Axis 0_V-phase Current</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>Axis 1_V-phase Current</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>Axis 0_W-phase Current</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>Axis 1_W-phase Current</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>Axis 0_Control Voltage</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>Axis 1_Control Voltage</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-Motor Parameter</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-System Parameter</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-Encoder Parameter</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-Protection Parameter</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-Fault Record</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-Control Status</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-Position Parameter</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-Speed Parameter</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-Torque Parameter</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext Exception</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Servo Heartbeat Send Failed</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>The Third Instruction Does Not Exist</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>The Role Name Cannot Be Empty</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>The Input Value Exceeds the Limit!</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>The Input Value Is Incorrect!</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Motor</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>Slave Node</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Workstation</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Axis</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Axis Sequence</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>Axis PID</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Axis Offset</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Device Wiring Direction</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Workstation Offset</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI View</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>System Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Motor Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Slave Node Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Line Segment Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Workstation Running Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Rotor Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Axis Sequence Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Axis Running PID Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Rotor Compensation Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Line Wiring Direction Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Workstation Compensation Configuration Parameter</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Line View Configuration Parameter</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-Motor Parameter</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-System Parameter</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-Encoder Parameter</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-Protection Parameter</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-Fault Record</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-Control Status</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-Position Parameter</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-Speed Parameter</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-Torque Parameter</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension Exception</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Simplified Chinese</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Traditional Chinese</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Japanese</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>No Description</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>No Value</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>Remote Terminal Has Been Closed</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>The New SerialPort Must Be in the Connected State.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>An Error Occurred While Processing the Data</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>The Configuration File Cannot Be Empty.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>The Serial Port Configuration Cannot Be Empty.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>The Current Adapter Does Not Support Object Sending.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>View Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Motor Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Slave Node</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Line Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Workstation Operation Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Axis Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Sequence Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>PID Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Axis Compensation Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Workstation Compensation Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>One-click Upload to Controller</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>One-click Download to Local</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Load Configuration</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Save Configuration As</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Send Success</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>The role name cannot be empty</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Online Demonstration</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Alarm</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Feedback Information</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Clear All Notifications</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Message Content</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Global Data Reset</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Platform Verification</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>System Parameter Configuration Initialization</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Slave Station Information Acquisition</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Mapping of Slave Station Address to Control Address</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Master-Slave Station Status Verification</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Completion of Status Initialization of Bus-System, etc.</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Initialization of Movement-Related Parameters</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Successful Initialization of Magnetic Drive</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>System Drive Error</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Host:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Port:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Username:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Password:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Connect</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Disconnect</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Remote Directory: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Forward</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Up</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Create Folder</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Download to Local</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Local Directory: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Upload to Server</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Transmission Log:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>System Soft Reset</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Connecting to FTP Server...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Connected to FTP Server</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Connect</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Disconnected</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Disconnect</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Loading Remote Directory: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Remote Directory Loaded: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Failed to Load Remote Directory: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Browse</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Loading Local Directory: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Local Directory Loaded: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Failed to Load Local Directory: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Downloading: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Download Completed: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Download Failed: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Uploading: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Upload Completed: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Upload Failed: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Directory Created: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Create Directory</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Failed to Create Directory: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Directory Deleted: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>File Deleted: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>System is Running</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>System is Ready</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>System is Enabled</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>System Bus is Connected</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>System is in Error State</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Axis Drive Error</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Axis Movement Error</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>Axis Error Status</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Axis Alarm</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Positive Limit of Axis</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Negative Limit of Axis</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Axis Warning</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>Axis in Left Position</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>Axis in Right Position</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>Axis Has Reached the Target Position</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>Axis is at the Workstation</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Axis Notification</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>Axis is Running</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>Axis is Enabled</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>Axis Status</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>Rotary Axis is Running</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Rotary Axis Homing Completed</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>Rotary Axis is Enabled</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Rotary Axis</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>Drive Connected Successfully!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>Drive Disconnected!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>Drive Parameter Recovery Successful!</value>
  </data>
</root>