<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Configuration en ligne</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Génération de configuration</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Journal d'opérations</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Repérage des données</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Collecte</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Suppression des enregistrements de pannes</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Réinitialisation des erreurs</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Réinitialisation des paramètres par défaut</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Écriture totale</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Écriture sélective</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Exécuter</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Exécuter</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Arrêter</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Exécuter</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Attribution des autorisations</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menu</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Utilisateur</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Rôle</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Oscilloscope</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Configuration du servo</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Contrôle du système</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>État de la connexion</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Contrôle de l'axe</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Ensemble du système</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Connexion du contrôleur</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Connexion du pilote</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Paramètres de base</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Pilote</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Contrôleur</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Connexion de l'appareil</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Résistance du fil de moteur (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Inductance du fil de moteur (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Courant nominal du moteur (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Couple nominal du moteur (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Courant maximal du moteur (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Constante de couple du moteur (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Coefficient de force électromotrice inverse du moteur (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Distance entre pôles N - N du moteur (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Nombre de paires de pôles du moteur</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Décalage d'angle électrique (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Courant de phase U du moteur (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Courant de phase V du moteur (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Courant de phase W du moteur (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Tension du bus (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Version du pilote - Modèle de puce</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Version du pilote - Itération de version majeure</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Version du pilote - Itération de fonctionnalité</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Version du pilote - Itération de correction de bogue</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Version du pilote - Debug/Lancement (0 - Debug, 1 - Lancement)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Contrôle de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Canal 0 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Canal 1 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Canal 2 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Canal 3 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Canal 4 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Canal 5 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Canal 6 de l'oscilloscope</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Canal 7 de l'oscilloscope</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Type d'encodeur</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Résolution de l'encodeur</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Version de l'encodeur - Itération de version majeure</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Version de l'encodeur - Itération de fonctionnalité</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Version de l'encodeur - Itération de correction de bogue</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Version de l'encodeur - Version de debug</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Sélection des fonctionnalités de debug de l'encodeur</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Interface de debug de la position de l'encodeur 0</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Interface de debug de la position de l'encodeur 1</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Seuil de détection de surintensité (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Temps de détermination de la détection de surintensité (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Seuil de courant de détermination de surcharge</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Courant maximal de surcharge</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Durée autorisée du courant maximal (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>Coefficient de compensation d'augmentation de I2t</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>Coefficient de compensation de diminution de I2t</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Seuil de courant de détection d'immobilisation du moteur (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Temps de détermination d'immobilisation du moteur (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Seuil de vitesse de détermination d'immobilisation du moteur (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>Seuil de température MOS d'alerte (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Seuil du nombre d'erreurs de communication de l'encodeur d'alerte</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Seuil de détection de dérapage du moteur (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Seuil de protection contre la surtension (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Seuil de protection contre la sous - tension (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Dernière position d'erreur</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Index d'erreur au démarrage</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Erreur historique 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Erreur historique 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Erreur historique 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Erreur historique 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Erreur historique 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Erreur historique 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Erreur historique 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Erreur historique 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Erreur historique 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Erreur historique 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Erreur historique 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Erreur historique 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Erreur historique 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Erreur historique 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Erreur historique 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Erreur historique 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Erreur historique 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Erreur historique 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Erreur historique 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Erreur historique 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Mot de commande</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Mot d'état</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>État de fonctionnement</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>État réel</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Position cible (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Position réelle (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Coefficient de proportionnalité de la boucle de position ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Coefficient d'intégration de la boucle de position</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Coefficient de différentiation de la boucle de position</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas de la commande de position (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Limite supérieure de la sortie de commande de position (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Limite inférieure de la sortie de commande de position (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Fréquence de coupure du filtre moyenne de la commande de position (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Vitesse cible (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Vitesse réelle (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Coefficient de proportionnalité de la boucle de vitesse (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Coefficient d'intégration de la boucle de vitesse (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Coefficient de différentiation de la boucle de vitesse (A/(mm/s²))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Coefficient anti - saturation d'intégration de la boucle de vitesse</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Coefficient de gain d'avance en vitesse</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas d'avance en vitesse (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas de rétroaction de vitesse (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas de la commande de vitesse (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Limite supérieure de la sortie de commande de vitesse (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Limite inférieure de la sortie de commande de vitesse (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Valeur cible du courant de l'axe Q (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>Valeur cible du courant de l'axe D (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Valeur de rétroaction du courant de l'axe Q (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>Valeur de rétroaction du courant de l'axe D (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Coefficient de proportionnalité de la boucle de courant</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Coefficient d'intégration de la boucle de courant</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Coefficient de différentiation de la boucle de courant</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>Coefficient de compensation de la force électromotrice inverse de l'axe D</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Coefficient de compensation de la force électromotrice inverse de l'axe Q</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Coefficient de compensation de la force électromotrice inverse du aimant permanent</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas de rétroaction de courant (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas de la commande de courant (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Coefficient de gain d'avance en courant</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Fréquence de coupure du filtre passe - bas d'avance en courant (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Fréquence centrale du filtre creux de la commande de courant (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Largeur de bande du filtre creux de la commande de courant (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Profondeur du filtre creux de la commande de courant (dB)</value>
    </data>
</root>