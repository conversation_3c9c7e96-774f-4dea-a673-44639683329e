<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Configurazione online</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Generazione configurazione</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Registro operazioni</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Rintracciabilità dati</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Raccolta</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Cancellazione registri guasti</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Reset errore</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Ripristino parametri predefiniti</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Scrittura completa</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Scrittura selezionata</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Esegui</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Esegui</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Ferma</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Esegui</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Assegnazione permessi</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menu</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Utente</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Ruolo</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Oscilloscopio</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Configurazione servo</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Controllo sistema</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Stato connessione</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Controllo asse</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Montaggio sistema</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Connessione controllore</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Connessione driver</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Impostazioni di base</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Driver</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Controllore</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Connessione dispositivo</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Resistenza filo motore (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Induttanza filo motore (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Corrente nominale motore (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Momento nominale motore (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Corrente picco motore (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Costante momento motore (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Coefficiente forza elettromotrice inversa motore (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Distanza polo - polo N - N motore (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Numero di coppie di poli motore</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Spostamento angolo elettrico (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Corrente fase U motore (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Corrente fase V motore (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Corrente fase W motore (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Tensione bus (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Versione driver - Modello chip</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Versione driver - Iterazione versione principale</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Versione driver - Iterazione funzionalità</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Versione driver - Iterazione bug</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Versione driver - Debug/Release (0 - Debug, 1 - Release)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Controllo oscilloscopio</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Canale oscilloscopio 0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Canale oscilloscopio 1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Canale oscilloscopio 2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Canale oscilloscopio 3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Canale oscilloscopio 4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Canale oscilloscopio 5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Canale oscilloscopio 6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Canale oscilloscopio 7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Tipo encoder</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Risoluzione encoder</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Versione encoder - Iterazione versione principale</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Versione encoder - Iterazione funzionalità</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Versione encoder - Iterazione bug</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Versione encoder - Versione debug</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Selezione funzionalità debug encoder</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Interfaccia debug posizione encoder 0</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Interfaccia debug posizione encoder 1</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Soglia rilevazione sovraccorrente (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Tempo di determinazione rilevazione sovraccorrente (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Soglia corrente per determinazione sovraccarico</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Corrente picco sovraccarico</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Tempo di durata corrente picco consentito (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>Coefficiente compensazione aumento I2t</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>Coefficiente compensazione diminuzione I2t</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Soglia corrente rilevazione blocco motore (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Tempo di determinazione blocco motore (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Soglia velocità determinazione blocco motore (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>Soglia temperatura MOS per allarme (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Soglia numero errori comunicazione encoder per allarme</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Soglia rilevazione slittamento motore (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Soglia protezione sovratensione (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Soglia protezione sottotensione (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Posizione errore più recente</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Indice errore all'avvio</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Errore storico 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Errore storico 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Errore storico 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Errore storico 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Errore storico 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Errore storico 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Errore storico 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Errore storico 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Errore storico 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Errore storico 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Errore storico 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Errore storico 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Errore storico 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Errore storico 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Errore storico 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Errore storico 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Errore storico 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Errore storico 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Errore storico 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Errore storico 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Parola di controllo</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Parola di stato</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Stato di esecuzione</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Stato reale</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Posizione obiettivo (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Posizione reale (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Coefficiente proporzionale ciclo posizione ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Coefficiente integrale ciclo posizione</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Coefficiente differenziale ciclo posizione</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso comando posizione (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Limite superiore uscita controllo posizione (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Limite inferiore uscita controllo posizione (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Frequenza di taglio filtro media comando posizione (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Velocità obiettivo (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Velocità reale (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Coefficiente proporzionale ciclo velocità (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Coefficiente integrale ciclo velocità (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Coefficiente differenziale ciclo velocità (A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Coefficiente antincastro integrale ciclo velocità</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Coefficiente guadagno feed - forward velocità</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso feed - forward velocità (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso feedback velocità (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso comando velocità (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Limite superiore uscita controllo velocità (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Limite inferiore uscita controllo velocità (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Valore obiettivo corrente asse Q (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>Valore obiettivo corrente asse D (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Valore feedback corrente asse Q (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>Valore feedback corrente asse D (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Coefficiente proporzionale ciclo corrente</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Coefficiente integrale ciclo corrente</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Coefficiente differenziale ciclo corrente</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>Coefficiente compensazione forza elettromotrice inversa asse D</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Coefficiente compensazione forza elettromotrice inversa asse Q</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Coefficiente compensazione forza elettromotrice inversa magnete permanente</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso feedback corrente (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso comando corrente (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Coefficiente guadagno feed - forward corrente</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Frequenza di taglio filtro passa - basso feed - forward corrente (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Frequenza centrale filtro notch comando corrente (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Larghezza di banda filtro notch comando corrente (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Profondità filtro notch comando corrente (dB)</value>
    </data>
</root>