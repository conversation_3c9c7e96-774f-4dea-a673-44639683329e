<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Lingua</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Connessione persa</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Connessione riuscita</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Automatico</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Manuale</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Inizializzazione</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Avvio</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Ferma</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>FermaU</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Abilita</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Reset errore asse</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>Riavvia sistema</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Inizializzazione stazione</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Abilita stazione</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Maschera stazione</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Guasto</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Manutenzione</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>In esecuzione</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Connessione apparecchio</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Driver</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Controllore</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Messaggio in chiaro</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Aggiornamento firmware</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Configurazione offline</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>Assemblaggio sistema</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Controllo asse</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>Stato connessione</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Controllo stazione</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>Controllo sistema</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Informazioni di feedback</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Errore e guasto</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Configurazione online</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Compensazione deviazione</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>Andata e ritorno forza curva</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Generazione configurazione</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>IO digitale</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Configurazione servo</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Oscilloscopio</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Impostazioni base</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Gestione ruoli</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>Gestione utenti</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Elenco funzioni</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Assegnazione permessi</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Rilevanza dati</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Registro operazioni</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Seleziona numero di asse:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Connessione driver:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Connessione controllore:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Controllo asse mobile</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Modalità di movimento asse:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Movimento Jog</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Movimento assoluto</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Movimento relativo</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Movimento stazione</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>ID asse:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Tipo di asse:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Motore rotante</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Motore lineare</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Modalità di velocità:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Modalità di controllo asse:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>ID linea destinazione:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>ID stazione destinazione:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Velocità:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Accelerazione:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Decelerazione:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Jerk:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Precisione di posizionamento:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Precisione antincrocio:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Posizione destinazione:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Seleziona operazione:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Esegui</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Leggi</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Ferma</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Oggetto a cui appartiene l'asse</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Linea a cui appartiene l'asse</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Errore driver</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Errore asse</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Posizione attuale dell'asse (mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Velocità attuale dell'asse</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>Stato attuale dell'asse</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Connessione controllore</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Porta</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Connetti</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Disconnetti</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Invia:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Registro:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Pulisci</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Generazione configurazione</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di sistema:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di motore:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di nodo slave:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di segmento di linea:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di stazione:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di mobile:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di asse rotante:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Numero di configurazioni di IO:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Genera file di configurazione</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Configurazione online</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Seleziona configurazione:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>Configurazione sistema</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Configurazione stazione</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Scrivi</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Nome parametro</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Tipo di impostazione</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Valore letto</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Valore impostato</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Descrizione</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>Controllo sistema</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Oggetto di controllo:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Motore rotante</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Motore lineare</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>Modalità di funzionamento sistema:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Allenamento asse</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Allenamento connessione</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Funzionamento automatico</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Modalità di funzionamento automatico:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Sincrono</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Asincrono</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Percentuale di velocità:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>ID nodo slave:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Modalità di controllo:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Seleziona operazione:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Esegui</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Leggi</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>ID asse errore sistema</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>Driver errore sistema</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>Codice errore sistema</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>Codice errore sistema</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>Stato sistema</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Controllo connessione</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Configurazione connessione:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>ID oggetto corrente:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>ID oggetto sinistro:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>Stato connessione:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Disconnetti</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Stabilisci connessione</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>ID oggetto destro:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Seleziona operazione:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Esegui</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Leggi</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>ID connessione:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>ID stazione destinazione:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Velocità:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Accelerazione:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Decelerazione:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Posizione destinazione:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Comando di controllo:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>ID linea</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>ID oggetto di connessione sinistro della linea</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>ID oggetto di connessione destro della linea</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>Stato abilitato</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>Stato di esecuzione</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Rientro a zero completato</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Codice errore</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Velocità reale</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Posizione reale</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Nome utente</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Ricorda password</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Accedi</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Inserisci parole chiave</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Aggiorna</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Ora di inizio: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Tempo</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Modulo</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Operazione</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Comportamento</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Descrizione</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Operatore</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>Visualizza</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Dettagli</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Descrizione dettagliata:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Ferma</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Raccolta</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Reimposta</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Croce</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>Graduazione asse X</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Graduazione asse Y</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Importa</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Esporta</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Controllo errori</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Ingrandisci/Riduci</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Frequenza di campionamento (1 - 300, unità ms):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Canale</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Seleziona oggetto</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Seleziona</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Valore</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>Visibilità</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Moltiplicatore</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Colore</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Debug</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Connessione driver</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Porta seriale</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Velocità di trasmissione</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Bits di dati</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Bit di parità</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Bits di stop</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Connetti</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Disconnetti</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Parametri driver</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Seleziona operazione:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Seleziona scrittura</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Scrivi tutto</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Ripristina parametri predefiniti</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Reset errore</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Cancella registro guasti</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Impostazione modalità di guida:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Controllo:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Modalità di controllo locale:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Sottomodulo:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Seleziona</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Nome parametro</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Tipo di impostazione</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Valore minimo</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Valore massimo</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Valore letto</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Valore impostato</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Permessi</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Coefficiente</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Monitoraggio</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Descrizione</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Ruolo:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Aggiorna</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Permessi:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Inserisci parole chiave</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>Nuovo</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Aggiorna</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Menu</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Codice di associazione</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>È un pulsante</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>È nascosto</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Evento pulsante</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Descrizione</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Livello</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Abilita</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Creatore</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Data di creazione</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Modificatore</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Data di modifica</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Operazione</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Modifica</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Elimina</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Nome menu:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Menu padre:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Inserisci parole chiave</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>Nuovo</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Aggiorna</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Nome ruolo</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Descrizione</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Livello</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Creatore</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Data di creazione</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Modificatore</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Data di modifica</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>È abilitato</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Operazione</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Modifica</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Elimina</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>Più il valore di priorità è basso, maggiore sono i permessi</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>Abilitare il ruolo corrente?</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Inserisci parole chiave</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>Nuovo</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Aggiorna</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Nome utente</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Nome reale</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Ruolo</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>Stato</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Nota</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Data di creazione</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Data di modifica</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Ultimo accesso</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Operazione</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Modifica</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Elimina</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Nome di accesso:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Password:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Cambia password</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>In attesa di abilitazione</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Salva</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>Non hai i permessi per questo menu</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI thread: </value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>UI thread exception: </value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>UI thread has a fatal error! </value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Non-UI thread has a fatal error</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Non-UI thread exception: </value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task thread: </value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Task thread exception: </value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Main thread</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>Switch</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>You do not have permission for this operation</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>Single-axis enabling state</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>Single-axis running state</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>Single-axis alarm state</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>Single-axis error state</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Single-axis left collision</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Single-axis right collision</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Single-axis positive limit</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Single-axis negative limit</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>Single-axis on the station</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>Single-axis has reached the target position</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>The system is ready</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>System enabling state</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>System error state</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>System running state</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>System bus state</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>System platform verification state</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Axis configuration is completed and axis sequence initialization can be performed</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Motion parameter configuration is completed and system old state restoration can be performed</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>System restoration of the old state is completed</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: Reserved\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【SQL statement】: </value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【SQL parameters】: </value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>The input value must be within the specified range</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Root node</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Get success</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Root node</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Get success</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Add success</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Update success</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Delete success</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Get success</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Add success</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Update success</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Delete success</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Jog forward movement</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Jog reverse movement</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Absolute movement</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Relative movement</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Workstation movement</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Set zero point</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Axis reset</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Configuration file generation is successful</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Workstation configuration distribution is successful</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Do not handle</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>Controller connection is disconnected! </value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>Controller connection is successful! </value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>The controller feedback axis number is 0 and this operation cannot be performed! </value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>No control</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Dual-axis position control</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Axis 0 electrical angle identification</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>DC sampling test</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>AC sampling test</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>CSV file (*.csv)|*.csv|All files (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Please select a CSV file</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Please select the save path</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Data export is successful</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>The passed-in object cannot be empty! </value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>New appended content</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>This is the content I wrote啊</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>The corresponding directory does not exist</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Button</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Send data: </value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>Adapter parsing data failed! </value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>Controller is not connected! </value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Controller heartbeat sending failed</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Upper enable</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Lower enable</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Set zero point</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Forward jog</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Backward jog</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Absolute movement</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Relative movement</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Workstation movement</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Upper enable</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Lower enable</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Error reset</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Pause</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Emergency stop</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>The control object has been removed from the protocol, do not use this property</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>The control object has been removed from the protocol, do not use this property</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Position parameter</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Axis 0 position feedback</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Axis 1 position feedback</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Speed parameter</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>Axis 0 speed instruction</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Axis 0 speed feedback</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>Axis 1 speed instruction</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Axis 1 speed feedback</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Current parameter</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>Axis 0 current instruction</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Axis 0 current feedback</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>Axis 1 current instruction</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Axis 1 current feedback</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Voltage parameter</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>Axis 0 D-axis reference voltage</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>Axis 1 D-axis reference voltage</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>Axis 0 Q-axis reference voltage</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>Axis 1 Q-axis reference voltage</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>Axis 0 bus voltage</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>Axis 1 bus voltage</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>Axis 0_U phase current</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>Axis 1_U phase current</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>Axis 0_V phase current</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>Axis 1_V phase current</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>Axis 0_W phase current</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>Axis 1_W phase current</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>Axis 0_control voltage</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>Axis 1_control voltage</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-Motor parameters</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-System parameters</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-Encoder parameters</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-Protection parameters</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-Fault records</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-Control status</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-Position parameters</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-Speed parameters</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-Torque parameters</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext exception</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Servo heartbeat sending failed</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>The third instruction does not exist</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>Role name cannot be empty</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>Input value exceeds the limit! </value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>Input value is incorrect! </value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Motor</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>Slave node</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Workstation</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Axis</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Axis sequence</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>Axis PID</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Axis offset</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Equipment wiring direction</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Workstation offset</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI view</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Configuration parameters</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>System configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Motor configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Slave node configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Line segment configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Workstation running configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Rotor configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Axis sequence configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Axis running PID configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Rotor compensation configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Line wiring direction configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Workstation compensation configuration parameters</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Line view configuration parameters</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-Motor parameters</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-System parameters</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-Encoder parameters</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-Protection parameters</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-Fault records</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-Control status</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-Position parameters</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-Speed parameters</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-Torque parameters</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension exception</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Simplified Chinese</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Traditional Chinese</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Japanese</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>No description</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>No value</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>Remote terminal is closed</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>The new SerialPort must be in the connected state.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>An error occurred while processing the data</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>The configuration file cannot be empty.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>The serial port configuration cannot be empty.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>The current adapter does not support object sending.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>Configurazione della visualizzazione</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Configurazione del motore</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Nodo slavaggio</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Configurazione della linea di produzione</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Configurazione dell'esecuzione della postazione di lavoro</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Configurazione dell'asse</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Configurazione della sequenza</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>Configurazione PID</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Configurazione della compensazione dell'asse</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Configurazione della compensazione della postazione di lavoro</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>Carica su controllore con un clic</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>Scarica in locale con un clic</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Carica la configurazione</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Salva la configurazione con un altro nome</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Inoltro eseguito correttamente</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>Il nome del ruolo non può essere vuoto</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Demonstrazione online</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Allarme</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Informazioni di feedback</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Cancella tutte le notifiche</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Tipo</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Origine</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Contenuto del messaggio</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Reset dati globali</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Verifica della piattaforma</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>Inizializzazione della configurazione dei parametri del sistema</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Ottieni informazioni sulla stazione secondaria</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Mappa l'indirizzo della stazione secondaria in quell' di controllo</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Verifica dello stato della stazione master - slave</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Inizializzazione dello stato del bus - sistema, ecc. completata</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Inizializzazione dei parametri relativi al movimento</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Inizializzazione corretta del drive magnetico</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>Errore di drive del sistema</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Host:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Porta:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Nome utente:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Password:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Connetti</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Disconnetti</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Directory remota: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Indietro</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Avanti</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Su</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Aggiorna</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Crea cartella</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Elimina</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Scarica in locale</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Directory locale: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Carica sul server</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Log di trasmissione:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>Reset software del sistema</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Stiamo connettendo al server FTP...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Connesso al server FTP</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Connetti</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Disconnesso</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Disconnetti</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Stiamo caricando la directory remota: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Directory remota caricata: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Impossibile caricare la directory remota: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Esplora</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Stiamo caricando la directory locale: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Directory locale caricata: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Impossibile caricare la directory locale: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Stiamo scaricando: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Scaricamento completato: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Scarica</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Scaricamento fallito: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Stiamo caricando: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Caricamento completato: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Carica</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Caricamento fallito: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Directory creata: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Crea directory</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Impossibile creare la directory: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Directory eliminata: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Elimina</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>File eliminato: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Apri</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>Il sistema sta eseguendo</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>Il sistema è pronto</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>Il sistema è abilitato</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>La busta del sistema è connessa</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>Il sistema è in stato di errore</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Errore del driver dell'asse</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Errore di movimento dell'asse</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>Stato di errore dell'asse</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Allarme dell'asse</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Limite positivo dell'asse</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Limite negativo dell'asse</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Avviso dell'asse</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>L'asse è arrivato in posizione sinistra</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>L'asse è arrivato in posizione destra</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>L'asse è arrivato alla posizione obiettivo</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>L'asse è sulla postazione di lavoro</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Notifica dell'asse</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>L'asse sta eseguendo</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>L'asse è abilitato</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>Stato dell'asse</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>L'asse rotante sta eseguendo</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Il ritorno a zero dell'asse rotante è completato</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>L'asse rotante è abilitato</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Asse rotante</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>Driver connesso con successo!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>Driver disconnesso!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>Parametri del driver ripristinati con successo!</value>
  </data>
</root>