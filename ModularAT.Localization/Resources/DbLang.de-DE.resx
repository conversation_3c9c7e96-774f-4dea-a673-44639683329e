<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Online-Konfiguration</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Konfigurationserstellung</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Vorgangsprotokoll</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Datenverfolgung</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Erfassung</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Fehlerprotokoll löschen</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Fehlerzurücksetzen</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Standardparameter wiederherstellen</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Alles schreiben</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Auswahl schreiben</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Ausführen</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Ausführen</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Stoppen</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Ausführen</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Berechtigungszuweisung</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menü</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Benutzer</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Rolle</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Oszilloskop</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Servokonfiguration</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Systemsteuerung</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Verbindungsstatus</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Achsensteuerung</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Systemaggregat</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Controllerverbindung</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Treiberverbindung</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Grundeinstellungen</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Treiber</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Controller</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Geräteverbindung</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Motordrehwiderstand (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Motordrehinduktivität (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Motornennstrom (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Motornennmoment (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Motorspitzenstrom (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Motormomentkonstante (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Motor-Rück-EMK-Koeffizient (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Motorpolpaar-N-N-Abstand (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Anzahl der Motorpolpaare</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Elektrischer Winkelversatz (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Motorphasestrom U (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Motorphasestrom V (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Motorphasestrom W (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Bussspannung (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Treiberversion - Chipmodell</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Treiberversion - Hauptversionsiteration</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Treiberversion - Funktionsiteration</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Treiberversion - Bug-Iteration</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Treiberversion - Debug/Release (0 - Debug, 1 - Release)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Oszilloskopsteuerung</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Oszilloskopkanal 0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Oszilloskopkanal 1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Oszilloskopkanal 2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Oszilloskopkanal 3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Oszilloskopkanal 4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Oszilloskopkanal 5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Oszilloskopkanal 6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Oszilloskopkanal 7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Encoder-Typ</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Encoderauflösung</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Encoderversion - Hauptversionsiteration</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Encoderversion - Funktionsiteration</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Encoderversion - Bug-Iteration</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Encoderversion - Debugversion</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Encoder-Debugfunktionsauswahl</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Encoder 0 - Positionsdebugschnittstelle</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Encoder 1 - Positionsdebugschnittstelle</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Überstromdetektionsschwelle (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Überstromdetektionszeit (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Überlaststromschwelle</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Überlastspannung</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Erlaubte Dauer des Spitzenstroms (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t - Anstiegskompensationsfaktor</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t - Abnahme-Kompensationsfaktor</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Motorklemmstromdetektionsschwelle (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Motorklemmzeit (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Motorklemmgeschwindigkeitsdetektionsschwelle (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS-Temperaturwarnschwelle (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Encoder-Kommunikationsfehleranzahl-Warnschwelle</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Motordrehzahlausfallschwelle (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Überspannungsschutzschwelle (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Unterspannungsschutzschwelle (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Letzte Fehlerposition</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Fehlerindex beim Start</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Fehlerverlauf 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Fehlerverlauf 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Fehlerverlauf 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Fehlerverlauf 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Fehlerverlauf 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Fehlerverlauf 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Fehlerverlauf 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Fehlerverlauf 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Fehlerverlauf 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Fehlerverlauf 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Fehlerverlauf 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Fehlerverlauf 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Fehlerverlauf 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Fehlerverlauf 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Fehlerverlauf 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Fehlerverlauf 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Fehlerverlauf 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Fehlerverlauf 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Fehlerverlauf 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Fehlerverlauf 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Steuerwort</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Statuswort</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Betriebszustand</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Aktueller Zustand</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Zielposition (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Aktuelle Position (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Positionsreglerproportionalitätsfaktor ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Positionsregler-Integrationsfaktor</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Positionsregler-Differenzierfaktor</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Positionsanweisung (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Positionssteuerausgangsgrenze UP (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Positionssteuerausgangsgrenze LOW (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Mittelwertfilterabschaltungsfrequenz der Positionsanweisung (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Zielgeschwindigkeit (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Aktuelle Geschwindigkeit (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Geschwindigkeitsreglerproportionalitätsfaktor (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Geschwindigkeitsregler-Integrationsfaktor (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Geschwindigkeitsregler-Differenzierfaktor (A/(mm/s²))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Geschwindigkeitsregler-Anti-Integrationsüberlauffaktor</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Geschwindigkeitsvorgabe-Gewichtungsfaktor</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Geschwindigkeitsvorgabe (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Geschwindigkeitsrückmeldung (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Geschwindigkeitsanweisung (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Geschwindigkeitssteuerausgangsgrenze UP (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Geschwindigkeitssteuerausgangsgrenze LOW (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q-Achsenstromzielwert (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D-Achsenstromzielwert (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q-Achsenstromrückmeldung (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D-Achsenstromrückmeldung (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Stromreglerproportionalitätsfaktor</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Stromregler-Integrationsfaktor</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Stromregler-Differenzierfaktor</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D-Achsen-Rück-EMK-Kompensationsfaktor</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q-Achsen-Rück-EMK-Kompensationsfaktor</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Permanentmagnet-Rück-EMK-Kompensationsfaktor</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Stromrückmeldung (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Stromanweisung (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Stromvorgabe-Gewichtungsfaktor</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Tiefpassfilterabschaltungsfrequenz der Stromvorgabe (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Knotenfilterzentralfrequenz der Stromanweisung (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Knotenfilterbandbreite der Stromanweisung (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Knotenfiltertiefe der Stromanweisung (dB)</value>
    </data>
</root>