<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Online Configuration</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Configuration Generation</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Operation Log</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Data Traceability</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Collection</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Fault Record Clearance</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Error Reset</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Restore Default Parameters</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Write All</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Selective Write</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Execute</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Execute</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Stop</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Execute</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Permission Allocation</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menu</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>User</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Role</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Oscilloscope</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Servo Configuration</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>System Control</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Connection Status</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Axis Control</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>System Assembly</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Controller Connection</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Driver Connection</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Basic Settings</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Driver</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Controller</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Device Connection</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Motor Wire Resistance (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Motor Wire Inductance (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Motor Rated Current (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Motor Rated Torque (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Motor Peak Current (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Motor Torque Constant (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Motor Back EMF Coefficient (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Motor Pole-Pair N-N Distance (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Motor Pole Pairs</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Electrical Angle Offset (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Motor Phase U Current (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Motor Phase V Current (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Motor Phase W Current (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Bus Voltage (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Driver Version - Chip Model</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Driver Version - Major Version Iteration</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Driver Version - Feature Iteration</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Driver Version - Bug Iteration</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Driver Version - Debug/Release (0 - Debug, 1 - Release)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Oscilloscope Control</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Oscilloscope Channel 0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Oscilloscope Channel 1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Oscilloscope Channel 2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Oscilloscope Channel 3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Oscilloscope Channel 4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Oscilloscope Channel 5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Oscilloscope Channel 6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Oscilloscope Channel 7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Encoder Type</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Encoder Resolution</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Encoder Version - Major Version Iteration</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Encoder Version - Feature Iteration</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Encoder Version - Bug Iteration</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Encoder Version - Debug Version</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Encoder Debug Function Selection</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Encoder 0 Position Debug Interface</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Encoder 1 Position Debug Interface</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Overcurrent Detection Threshold (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Overcurrent Detection Judgment Time (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Overload Judgment Current Threshold</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Overload Peak Current</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Allowed Peak Current Duration (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t Increase Compensation Coefficient</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t Decrease Compensation Coefficient</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Motor Stall Detection Current Threshold (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Motor Stall Judgment Time (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Motor Stall Speed Judgment Threshold (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS Temperature Alarm Threshold (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Encoder Communication Error Count Alarm Threshold</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Motor Stall Detection Threshold (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Overvoltage Protection Threshold (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Undervoltage Protection Threshold (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Latest Error Location</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Error Index at Startup</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Historical Error 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Historical Error 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Historical Error 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Historical Error 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Historical Error 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Historical Error 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Historical Error 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Historical Error 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Historical Error 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Historical Error 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Historical Error 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Historical Error 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Historical Error 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Historical Error 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Historical Error 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Historical Error 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Historical Error 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Historical Error 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Historical Error 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Historical Error 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Control Word</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Status Word</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Operating Status</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Actual Status</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Target Position (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Actual Position (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Position Loop Proportional Coefficient ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Position Loop Integral Coefficient</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Position Loop Differential Coefficient</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Position Command Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Position Control Output Clamp UP (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Position Control Output Clamp LOW (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Position Command Mean Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Target Speed (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Actual Speed (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Speed Loop Proportional Coefficient (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Speed Loop Integral Coefficient (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Speed Loop Differential Coefficient (A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Speed Loop Anti - Integral Windup Coefficient</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Speed Feed - Forward Gain Coefficient</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Speed Feed - Forward Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Speed Feedback Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Speed Command Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Speed Control Output Clamp UP (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Speed Control Output Clamp LOW (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q - Axis Current Target Value (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D - Axis Current Target Value (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q - Axis Current Feedback Value (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D - Axis Current Feedback Value (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Current Loop Proportional Coefficient</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Current Loop Integral Coefficient</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Current Loop Differential Coefficient</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D - Axis Back EMF Compensation Coefficient</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q - Axis Back EMF Compensation Coefficient</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Permanent Magnet Back EMF Compensation Coefficient</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Current Feedback Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Current Command Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Current Feed - Forward Gain Coefficient</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Current Feed - Forward Low - Pass Filter Cutoff Frequency (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Current Command Notch Filter Center Frequency (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Current Command Notch Filter Bandwidth (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Current Command Notch Filter Depth (dB)</value>
    </data>
</root>