<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>在线配置</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>配置生成</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>操作日志</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>数据追溯</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>采集</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>故障记录清除</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>错误复位</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>恢复默认参数</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>全部写入</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>选择写入</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>执行</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>执行</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>停止</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>执行</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>权限分配</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>菜单</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>用户</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>角色</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>示波器</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>伺服配置</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>系统控制</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>接驳状态</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>轴控制</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>系统总成</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>控制器连接</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>驱动器连接</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>基本设置</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>驱动器</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>控制器</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>设备连接</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>电机线电阻(mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>电机线电感(mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>电机额定电流(Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>电机额定力矩(N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>电机峰值电流(Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>电机力矩常数(N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>电机反电动势系数(V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>电机极对N-N距(mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>电机极对数</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>电角度偏移(PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>电机U相电流(A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>电机V相电流(A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>电机W相电流(A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>母线电压(V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>驱动器类型</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>驱动版本-大版本迭代</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>驱动版本-功能迭代</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>驱动版本-Bug迭代</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>驱动版本-调试发布(0-调试 1-发布)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>示波器控制</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>示波器通道0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>示波器通道1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>示波器通道2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>示波器通道3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>示波器通道4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>示波器通道5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>示波器通道6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>示波器通道7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>编码器类型</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>编码器分辨率</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>编码器版本-大版本迭代</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>编码器版本-功能迭代</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>编码器版本-Bug迭代</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>编码器版本-调试版本</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>编码器调试功能选择</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>编码器0位置调试接口</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>编码器1位置调试接口</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>过流检测阈值(A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>过流检测判定时间(ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>过载判定电流阈值</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>过载峰值电流</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>允许峰值电流持续时间(ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t增加补偿系数</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t减少补偿系数</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>电机堵转检测电流阈值(A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>电机堵转判定时间(ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>电机堵转速度判定阈值(mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS温度报警阈值(℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>编码器通讯错误次数报警阈值</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>电机失速检测阈值(mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>过压保护阈值(V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>欠压保护阈值(V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>最新错误位置</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>开机时错误索引</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>历史错误0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>历史错误1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>历史错误2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>历史错误3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>历史错误4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>历史错误5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>历史错误6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>历史错误7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>历史错误8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>历史错误9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>历史错误10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>历史错误11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>历史错误12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>历史错误13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>历史错误14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>历史错误15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>历史错误16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>历史错误17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>历史错误18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>历史错误19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>控制字</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>状态字</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>运行状态</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>实际状态</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>目标位置(PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>实际位置(PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>位置环比例系数((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>位置环积分系数</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>位置环微分系数</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>位置指令低通滤波截止频率(Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>位置控制输出钳位UP(mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>位置控制输出钳位LOW(mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>位置指令均值滤波器截止频率(Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>目标速度(mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>实际速度(mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>速度环比例系数(A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>速度环积分系数(A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>速度环微分系数(A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>速度环抗积分饱和系数</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>速度前馈增益系数</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>速度前馈低通滤波截止频率(Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>速度反馈低通滤波截止频率(Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>速度指令低通滤波截止频率(Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>速度控制输出钳位UP(A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>速度控制输出钳位LOW(A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q轴电流目标值(A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D轴电流目标值(A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q轴电流反馈值(A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D轴电流反馈值(A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>电流环比例系数</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>电流环积分系数</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>电流环微分系数</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D轴反电势补偿系数</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q轴反电势补偿系数</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>永磁体反电势补偿系数</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>电流反馈低通滤波截止频率(Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>电流指令低通滤波截止频率(Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>电流前馈增益系数</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>电流前馈低通滤波截止频率(Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>电流指令陷波滤波中心频率(Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>电流指令陷波滤波带宽(Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>电流指令陷波滤波深度(dB)</value>
    </data>
</root>