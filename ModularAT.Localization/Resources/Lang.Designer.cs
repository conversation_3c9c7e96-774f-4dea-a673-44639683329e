//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ModularAT.Localization.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Lang {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Lang() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ModularAT.Localization.Resources.Lang", typeof(Lang).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非UI线程异常：.
        /// </summary>
        public static string App_xaml_Non_ui_thread_exception {
            get {
                return ResourceManager.GetString("App_xaml_Non_ui_thread_exception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 非UI线程发生致命错误.
        /// </summary>
        public static string App_xaml_Non_ui_thread_fatal_error {
            get {
                return ResourceManager.GetString("App_xaml_Non_ui_thread_fatal_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task线程：.
        /// </summary>
        public static string App_xaml_Task_thread {
            get {
                return ResourceManager.GetString("App_xaml_Task_thread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task线程异常：.
        /// </summary>
        public static string App_xaml_Task_thread_exception {
            get {
                return ResourceManager.GetString("App_xaml_Task_thread_exception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UI线程：.
        /// </summary>
        public static string App_xaml_Ui_thread {
            get {
                return ResourceManager.GetString("App_xaml_Ui_thread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UI线程异常：.
        /// </summary>
        public static string App_xaml_Ui_thread_exception {
            get {
                return ResourceManager.GetString("App_xaml_Ui_thread_exception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UI线程发生致命错误！.
        /// </summary>
        public static string App_xaml_Ui_thread_fatal_error {
            get {
                return ResourceManager.GetString("App_xaml_Ui_thread_fatal_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制对象已经从协议中移除，请勿使用此属性.
        /// </summary>
        public static string AxisCtrlCmdPackage_Control_object_removed {
            get {
                return ResourceManager.GetString("AxisCtrlCmdPackage_Control_object_removed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴报警状态.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_alarm_status {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_alarm_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴使能状态.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_enable_status {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_enable_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴错误状态.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_error_status {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_error_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴左碰撞.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_left_collision {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_left_collision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴负限位.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_negative_limit {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_negative_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴在工位上.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_on_workstation {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_on_workstation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴正限位.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_positive_limit {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_positive_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴已到达目标位置.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_reached_target {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_reached_target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴右碰撞.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_right_collision {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_right_collision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 单轴运行状态.
        /// </summary>
        public static string AxisFeedBackMapping_Single_axis_running_status {
            get {
                return ResourceManager.GetString("AxisFeedBackMapping_Single_axis_running_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 权限:.
        /// </summary>
        public static string BasePermAssign_Perm {
            get {
                return ResourceManager.GetString("BasePermAssign_Perm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 刷新.
        /// </summary>
        public static string BasePermAssign_Refresh {
            get {
                return ResourceManager.GetString("BasePermAssign_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色:.
        /// </summary>
        public static string BasePermAssign_Role {
            get {
                return ResourceManager.GetString("BasePermAssign_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        public static string BasePermAssign_Save {
            get {
                return ResourceManager.GetString("BasePermAssign_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 获取成功.
        /// </summary>
        public static string BasePermAssignViewModel_Get_success {
            get {
                return ResourceManager.GetString("BasePermAssignViewModel_Get_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 根节点.
        /// </summary>
        public static string BasePermAssignViewModel_Root_node {
            get {
                return ResourceManager.GetString("BasePermAssignViewModel_Root_node", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 绑定代码.
        /// </summary>
        public static string BasePermission_Bind_code {
            get {
                return ResourceManager.GetString("BasePermission_Bind_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 按钮事件.
        /// </summary>
        public static string BasePermission_Btn_event {
            get {
                return ResourceManager.GetString("BasePermission_Btn_event", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 取消.
        /// </summary>
        public static string BasePermission_Cancel {
            get {
                return ResourceManager.GetString("BasePermission_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建时间.
        /// </summary>
        public static string BasePermission_Create_time {
            get {
                return ResourceManager.GetString("BasePermission_Create_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建者.
        /// </summary>
        public static string BasePermission_Creator {
            get {
                return ResourceManager.GetString("BasePermission_Creator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除.
        /// </summary>
        public static string BasePermission_Delete {
            get {
                return ResourceManager.GetString("BasePermission_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 描述.
        /// </summary>
        public static string BasePermission_Desc {
            get {
                return ResourceManager.GetString("BasePermission_Desc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编辑.
        /// </summary>
        public static string BasePermission_Edit {
            get {
                return ResourceManager.GetString("BasePermission_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 启用.
        /// </summary>
        public static string BasePermission_Enable {
            get {
                return ResourceManager.GetString("BasePermission_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请输入关键字.
        /// </summary>
        public static string BasePermission_Enter_keywords {
            get {
                return ResourceManager.GetString("BasePermission_Enter_keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 是否是按钮.
        /// </summary>
        public static string BasePermission_Is_button {
            get {
                return ResourceManager.GetString("BasePermission_Is_button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 是否隐藏.
        /// </summary>
        public static string BasePermission_Is_hidden {
            get {
                return ResourceManager.GetString("BasePermission_Is_hidden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 级别.
        /// </summary>
        public static string BasePermission_Level {
            get {
                return ResourceManager.GetString("BasePermission_Level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 菜单.
        /// </summary>
        public static string BasePermission_Menu {
            get {
                return ResourceManager.GetString("BasePermission_Menu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 菜单名称:.
        /// </summary>
        public static string BasePermission_Menu_name {
            get {
                return ResourceManager.GetString("BasePermission_Menu_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 修改时间.
        /// </summary>
        public static string BasePermission_Mod_time {
            get {
                return ResourceManager.GetString("BasePermission_Mod_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 修改者.
        /// </summary>
        public static string BasePermission_Modifier {
            get {
                return ResourceManager.GetString("BasePermission_Modifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新建.
        /// </summary>
        public static string BasePermission_New {
            get {
                return ResourceManager.GetString("BasePermission_New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作.
        /// </summary>
        public static string BasePermission_Op {
            get {
                return ResourceManager.GetString("BasePermission_Op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 父级菜单:.
        /// </summary>
        public static string BasePermission_Parent_menu {
            get {
                return ResourceManager.GetString("BasePermission_Parent_menu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 刷新.
        /// </summary>
        public static string BasePermission_Refresh {
            get {
                return ResourceManager.GetString("BasePermission_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        public static string BasePermission_Save {
            get {
                return ResourceManager.GetString("BasePermission_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 添加成功.
        /// </summary>
        public static string BasePermissionViewModel_Add_success {
            get {
                return ResourceManager.GetString("BasePermissionViewModel_Add_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除成功.
        /// </summary>
        public static string BasePermissionViewModel_Delete_success {
            get {
                return ResourceManager.GetString("BasePermissionViewModel_Delete_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 获取成功.
        /// </summary>
        public static string BasePermissionViewModel_Get_success {
            get {
                return ResourceManager.GetString("BasePermissionViewModel_Get_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 根节点.
        /// </summary>
        public static string BasePermissionViewModel_Root_node {
            get {
                return ResourceManager.GetString("BasePermissionViewModel_Root_node", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 更新成功.
        /// </summary>
        public static string BasePermissionViewModel_Update_success {
            get {
                return ResourceManager.GetString("BasePermissionViewModel_Update_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 取消.
        /// </summary>
        public static string BaseRole_Cancel {
            get {
                return ResourceManager.GetString("BaseRole_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建时间.
        /// </summary>
        public static string BaseRole_Create_time {
            get {
                return ResourceManager.GetString("BaseRole_Create_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建者.
        /// </summary>
        public static string BaseRole_Creator {
            get {
                return ResourceManager.GetString("BaseRole_Creator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除.
        /// </summary>
        public static string BaseRole_Delete {
            get {
                return ResourceManager.GetString("BaseRole_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 描述.
        /// </summary>
        public static string BaseRole_Desc {
            get {
                return ResourceManager.GetString("BaseRole_Desc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编辑.
        /// </summary>
        public static string BaseRole_Edit {
            get {
                return ResourceManager.GetString("BaseRole_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 是否启用当前角色:.
        /// </summary>
        public static string BaseRole_Enable_curr_role {
            get {
                return ResourceManager.GetString("BaseRole_Enable_curr_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请输入关键字.
        /// </summary>
        public static string BaseRole_Enter_keywords {
            get {
                return ResourceManager.GetString("BaseRole_Enter_keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 是否启用.
        /// </summary>
        public static string BaseRole_Is_enabled {
            get {
                return ResourceManager.GetString("BaseRole_Is_enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 级别.
        /// </summary>
        public static string BaseRole_Level {
            get {
                return ResourceManager.GetString("BaseRole_Level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 修改时间.
        /// </summary>
        public static string BaseRole_Mod_time {
            get {
                return ResourceManager.GetString("BaseRole_Mod_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 修改者.
        /// </summary>
        public static string BaseRole_Modifier {
            get {
                return ResourceManager.GetString("BaseRole_Modifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新建.
        /// </summary>
        public static string BaseRole_New {
            get {
                return ResourceManager.GetString("BaseRole_New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作.
        /// </summary>
        public static string BaseRole_Op {
            get {
                return ResourceManager.GetString("BaseRole_Op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 优先级越小，权限越大.
        /// </summary>
        public static string BaseRole_Pri_smaller_perm_bigger {
            get {
                return ResourceManager.GetString("BaseRole_Pri_smaller_perm_bigger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 刷新.
        /// </summary>
        public static string BaseRole_Refresh {
            get {
                return ResourceManager.GetString("BaseRole_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色名.
        /// </summary>
        public static string BaseRole_Role_name {
            get {
                return ResourceManager.GetString("BaseRole_Role_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        public static string BaseRole_Save {
            get {
                return ResourceManager.GetString("BaseRole_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 取消.
        /// </summary>
        public static string BaseUser_Cancel {
            get {
                return ResourceManager.GetString("BaseUser_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 修改密码.
        /// </summary>
        public static string BaseUser_Change_passwd {
            get {
                return ResourceManager.GetString("BaseUser_Change_passwd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建时间.
        /// </summary>
        public static string BaseUser_Create_time {
            get {
                return ResourceManager.GetString("BaseUser_Create_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除.
        /// </summary>
        public static string BaseUser_Delete {
            get {
                return ResourceManager.GetString("BaseUser_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 编辑.
        /// </summary>
        public static string BaseUser_Edit {
            get {
                return ResourceManager.GetString("BaseUser_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请输入关键字.
        /// </summary>
        public static string BaseUser_Enter_keywords {
            get {
                return ResourceManager.GetString("BaseUser_Enter_keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最后登陆.
        /// </summary>
        public static string BaseUser_Last_login {
            get {
                return ResourceManager.GetString("BaseUser_Last_login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 登陆名:.
        /// </summary>
        public static string BaseUser_Login_name {
            get {
                return ResourceManager.GetString("BaseUser_Login_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 修改时间.
        /// </summary>
        public static string BaseUser_Mod_time {
            get {
                return ResourceManager.GetString("BaseUser_Mod_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新建.
        /// </summary>
        public static string BaseUser_New {
            get {
                return ResourceManager.GetString("BaseUser_New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作.
        /// </summary>
        public static string BaseUser_Op {
            get {
                return ResourceManager.GetString("BaseUser_Op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 密码:.
        /// </summary>
        public static string BaseUser_Passwd {
            get {
                return ResourceManager.GetString("BaseUser_Passwd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 待启用.
        /// </summary>
        public static string BaseUser_Pending_enable {
            get {
                return ResourceManager.GetString("BaseUser_Pending_enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 真实姓名.
        /// </summary>
        public static string BaseUser_Real_name {
            get {
                return ResourceManager.GetString("BaseUser_Real_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 刷新.
        /// </summary>
        public static string BaseUser_Refresh {
            get {
                return ResourceManager.GetString("BaseUser_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 备注.
        /// </summary>
        public static string BaseUser_Remark {
            get {
                return ResourceManager.GetString("BaseUser_Remark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色.
        /// </summary>
        public static string BaseUser_Role {
            get {
                return ResourceManager.GetString("BaseUser_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        public static string BaseUser_Save {
            get {
                return ResourceManager.GetString("BaseUser_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 状态.
        /// </summary>
        public static string BaseUser_Status {
            get {
                return ResourceManager.GetString("BaseUser_Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 用户名.
        /// </summary>
        public static string BaseUser_User_name {
            get {
                return ResourceManager.GetString("BaseUser_User_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 添加成功.
        /// </summary>
        public static string BaseUserViewModel_Add_success {
            get {
                return ResourceManager.GetString("BaseUserViewModel_Add_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除成功.
        /// </summary>
        public static string BaseUserViewModel_Delete_success {
            get {
                return ResourceManager.GetString("BaseUserViewModel_Delete_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 获取成功.
        /// </summary>
        public static string BaseUserViewModel_Get_success {
            get {
                return ResourceManager.GetString("BaseUserViewModel_Get_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 更新成功.
        /// </summary>
        public static string BaseUserViewModel_Update_success {
            get {
                return ResourceManager.GetString("BaseUserViewModel_Update_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 绝对运动.
        /// </summary>
        public static string ControlerAxis_Abs_mot {
            get {
                return ResourceManager.GetString("ControlerAxis_Abs_mot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 加速度：.
        /// </summary>
        public static string ControlerAxis_Accel {
            get {
                return ResourceManager.GetString("ControlerAxis_Accel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 防碰撞精度：.
        /// </summary>
        public static string ControlerAxis_Anti_coll_accu {
            get {
                return ResourceManager.GetString("ControlerAxis_Anti_coll_accu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴控制模式：.
        /// </summary>
        public static string ControlerAxis_Axis_ctrl_mode {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_ctrl_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴当前位置（mm）.
        /// </summary>
        public static string ControlerAxis_Axis_curr_pos_mm {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_curr_pos_mm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴当前速度.
        /// </summary>
        public static string ControlerAxis_Axis_curr_speed {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_curr_speed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴当前状态.
        /// </summary>
        public static string ControlerAxis_Axis_curr_stat {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_curr_stat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴错误.
        /// </summary>
        public static string ControlerAxis_Axis_err {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_err", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴ID:.
        /// </summary>
        public static string ControlerAxis_Axis_id {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴所处线体.
        /// </summary>
        public static string ControlerAxis_Axis_line {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴运动模式：.
        /// </summary>
        public static string ControlerAxis_Axis_mot_mode {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_mot_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴所处对象.
        /// </summary>
        public static string ControlerAxis_Axis_obj {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_obj", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴类型：.
        /// </summary>
        public static string ControlerAxis_Axis_type {
            get {
                return ResourceManager.GetString("ControlerAxis_Axis_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 减速度：.
        /// </summary>
        public static string ControlerAxis_Decel {
            get {
                return ResourceManager.GetString("ControlerAxis_Decel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动错误.
        /// </summary>
        public static string ControlerAxis_Driver_err {
            get {
                return ResourceManager.GetString("ControlerAxis_Driver_err", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 执行.
        /// </summary>
        public static string ControlerAxis_Exec {
            get {
                return ResourceManager.GetString("ControlerAxis_Exec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 加加速度：.
        /// </summary>
        public static string ControlerAxis_Jerk {
            get {
                return ResourceManager.GetString("ControlerAxis_Jerk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jog运动.
        /// </summary>
        public static string ControlerAxis_Jog_mot {
            get {
                return ResourceManager.GetString("ControlerAxis_Jog_mot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 直线电机.
        /// </summary>
        public static string ControlerAxis_Linear_motor {
            get {
                return ResourceManager.GetString("ControlerAxis_Linear_motor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 动子.
        /// </summary>
        public static string ControlerAxis_Mover {
            get {
                return ResourceManager.GetString("ControlerAxis_Mover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 动子轴控制.
        /// </summary>
        public static string ControlerAxis_Mover_axis_ctrl {
            get {
                return ResourceManager.GetString("ControlerAxis_Mover_axis_ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 定位精度：.
        /// </summary>
        public static string ControlerAxis_Pos_accu {
            get {
                return ResourceManager.GetString("ControlerAxis_Pos_accu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 读取.
        /// </summary>
        public static string ControlerAxis_Read {
            get {
                return ResourceManager.GetString("ControlerAxis_Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 相对运动.
        /// </summary>
        public static string ControlerAxis_Rel_mot {
            get {
                return ResourceManager.GetString("ControlerAxis_Rel_mot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转电机.
        /// </summary>
        public static string ControlerAxis_Rotary_motor {
            get {
                return ResourceManager.GetString("ControlerAxis_Rotary_motor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择操作：.
        /// </summary>
        public static string ControlerAxis_Sel_op {
            get {
                return ResourceManager.GetString("ControlerAxis_Sel_op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度：.
        /// </summary>
        public static string ControlerAxis_Speed {
            get {
                return ResourceManager.GetString("ControlerAxis_Speed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度模式：.
        /// </summary>
        public static string ControlerAxis_Speed_mode {
            get {
                return ResourceManager.GetString("ControlerAxis_Speed_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位运动.
        /// </summary>
        public static string ControlerAxis_Station_mot {
            get {
                return ResourceManager.GetString("ControlerAxis_Station_mot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 停止.
        /// </summary>
        public static string ControlerAxis_Stop {
            get {
                return ResourceManager.GetString("ControlerAxis_Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标线体ID:.
        /// </summary>
        public static string ControlerAxis_Target_line_id {
            get {
                return ResourceManager.GetString("ControlerAxis_Target_line_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标位置：.
        /// </summary>
        public static string ControlerAxis_Target_pos {
            get {
                return ResourceManager.GetString("ControlerAxis_Target_pos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标工位ID:.
        /// </summary>
        public static string ControlerAxis_Target_station_id {
            get {
                return ResourceManager.GetString("ControlerAxis_Target_station_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 绝对运动.
        /// </summary>
        public static string ControlerAxisViewModel_Absolute_movement {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Absolute_movement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴复位.
        /// </summary>
        public static string ControlerAxisViewModel_Axis_reset {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Axis_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jog正向运动.
        /// </summary>
        public static string ControlerAxisViewModel_Jog_forward {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Jog_forward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jog反向运动.
        /// </summary>
        public static string ControlerAxisViewModel_Jog_reverse {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Jog_reverse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 相对运动.
        /// </summary>
        public static string ControlerAxisViewModel_Relative_movement {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Relative_movement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设置零点.
        /// </summary>
        public static string ControlerAxisViewModel_Set_zero_point {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Set_zero_point", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位运动.
        /// </summary>
        public static string ControlerAxisViewModel_Workstation_movement {
            get {
                return ResourceManager.GetString("ControlerAxisViewModel_Workstation_movement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 总线-系统等状态初始化完成.
        /// </summary>
        public static string ControlerClient_Completion_of_status_initialization_of_bus_system_etc {
            get {
                return ResourceManager.GetString("ControlerClient_Completion_of_status_initialization_of_bus_system_etc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接.
        /// </summary>
        public static string ControlerClient_Connect {
            get {
                return ResourceManager.GetString("ControlerClient_Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器连接.
        /// </summary>
        public static string ControlerClient_Ctrl_conn {
            get {
                return ResourceManager.GetString("ControlerClient_Ctrl_conn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 断开.
        /// </summary>
        public static string ControlerClient_Disconnect {
            get {
                return ResourceManager.GetString("ControlerClient_Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 全局数据复位.
        /// </summary>
        public static string ControlerClient_Global_data_reset {
            get {
                return ResourceManager.GetString("ControlerClient_Global_data_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 运动相关参数初始化.
        /// </summary>
        public static string ControlerClient_Initialization_of_movement_related_parameters {
            get {
                return ResourceManager.GetString("ControlerClient_Initialization_of_movement_related_parameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站地址映射到控制地址.
        /// </summary>
        public static string ControlerClient_Mapping_of_slave_station_address_to_control_address {
            get {
                return ResourceManager.GetString("ControlerClient_Mapping_of_slave_station_address_to_control_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 主从站状态校验.
        /// </summary>
        public static string ControlerClient_Master_slave_station_status_verification {
            get {
                return ResourceManager.GetString("ControlerClient_Master_slave_station_status_verification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 平台校验.
        /// </summary>
        public static string ControlerClient_Platform_verification {
            get {
                return ResourceManager.GetString("ControlerClient_Platform_verification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 端口.
        /// </summary>
        public static string ControlerClient_Port {
            get {
                return ResourceManager.GetString("ControlerClient_Port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        public static string ControlerClient_Save {
            get {
                return ResourceManager.GetString("ControlerClient_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站信息获取.
        /// </summary>
        public static string ControlerClient_Slave_station_information_acquisition {
            get {
                return ResourceManager.GetString("ControlerClient_Slave_station_information_acquisition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 磁驱初始化成功.
        /// </summary>
        public static string ControlerClient_Successful_initialization_of_magnetic_drive {
            get {
                return ResourceManager.GetString("ControlerClient_Successful_initialization_of_magnetic_drive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统参数配置初始化.
        /// </summary>
        public static string ControlerClient_System_parameter_configuration_initialization {
            get {
                return ResourceManager.GetString("ControlerClient_System_parameter_configuration_initialization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 清空.
        /// </summary>
        public static string ControlerDebug_Clear {
            get {
                return ResourceManager.GetString("ControlerDebug_Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日志：.
        /// </summary>
        public static string ControlerDebug_Log {
            get {
                return ResourceManager.GetString("ControlerDebug_Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 发送：.
        /// </summary>
        public static string ControlerDebug_Send {
            get {
                return ResourceManager.GetString("ControlerDebug_Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置生成.
        /// </summary>
        public static string ControlerGenerateConfig_Conf_gen {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Conf_gen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 生成配置文件.
        /// </summary>
        public static string ControlerGenerateConfig_Gen_conf_file {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Gen_conf_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IO配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Io_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Io_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体段配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Line_seg_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Line_seg_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Motor_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Motor_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 动子配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Mover_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Mover_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转轴配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Rot_axis_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Rot_axis_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站节点配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Slave_node_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Slave_node_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Station_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Station_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统配置个数：.
        /// </summary>
        public static string ControlerGenerateConfig_Sys_conf_num {
            get {
                return ResourceManager.GetString("ControlerGenerateConfig_Sys_conf_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置文件生成成功.
        /// </summary>
        public static string ControlerGenerateConfigViewModel_Config_file_generated {
            get {
                return ResourceManager.GetString("ControlerGenerateConfigViewModel_Config_file_generated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴补偿配置.
        /// </summary>
        public static string ControlerOnlineConfig_Axis_compensation_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Axis_compensation_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴配置.
        /// </summary>
        public static string ControlerOnlineConfig_Axis_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Axis_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 描述.
        /// </summary>
        public static string ControlerOnlineConfig_Desc {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Desc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一键下载到本地.
        /// </summary>
        public static string ControlerOnlineConfig_Download_to_local_with_one_click {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Download_to_local_with_one_click", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体配置.
        /// </summary>
        public static string ControlerOnlineConfig_Line_body_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Line_body_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 载入配置.
        /// </summary>
        public static string ControlerOnlineConfig_Load_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Load_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机配置.
        /// </summary>
        public static string ControlerOnlineConfig_Motor_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Motor_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在线配置.
        /// </summary>
        public static string ControlerOnlineConfig_Online_conf {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Online_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 参数名称.
        /// </summary>
        public static string ControlerOnlineConfig_Param_name {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Param_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PID配置.
        /// </summary>
        public static string ControlerOnlineConfig_Pid_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Pid_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 读取值.
        /// </summary>
        public static string ControlerOnlineConfig_Read_val {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Read_val", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置另存.
        /// </summary>
        public static string ControlerOnlineConfig_Save_configuration_as {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Save_configuration_as", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择配置:.
        /// </summary>
        public static string ControlerOnlineConfig_Sel_conf {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Sel_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 序列配置.
        /// </summary>
        public static string ControlerOnlineConfig_Sequence_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Sequence_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设定类型.
        /// </summary>
        public static string ControlerOnlineConfig_Set_type {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Set_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设定值.
        /// </summary>
        public static string ControlerOnlineConfig_Set_val {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Set_val", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站节点.
        /// </summary>
        public static string ControlerOnlineConfig_Slave_node {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Slave_node", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位配置.
        /// </summary>
        public static string ControlerOnlineConfig_Station_conf {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Station_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统配置.
        /// </summary>
        public static string ControlerOnlineConfig_Sys_conf {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Sys_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 一键上传到控制器.
        /// </summary>
        public static string ControlerOnlineConfig_Upload_to_controller_with_one_click {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Upload_to_controller_with_one_click", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 视图配置.
        /// </summary>
        public static string ControlerOnlineConfig_View_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_View_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位补偿配置.
        /// </summary>
        public static string ControlerOnlineConfig_Workstation_compensation_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Workstation_compensation_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位运行配置.
        /// </summary>
        public static string ControlerOnlineConfig_Workstation_operation_configuration {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Workstation_operation_configuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 写入.
        /// </summary>
        public static string ControlerOnlineConfig_Write {
            get {
                return ResourceManager.GetString("ControlerOnlineConfig_Write", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 发送成功.
        /// </summary>
        public static string ControlerOnlineConfigViewModel_Send_success {
            get {
                return ResourceManager.GetString("ControlerOnlineConfigViewModel_Send_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位配置下发成功.
        /// </summary>
        public static string ControlerOnlineConfigViewModel_Workstation_config_distributed {
            get {
                return ResourceManager.GetString("ControlerOnlineConfigViewModel_Workstation_config_distributed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 异步.
        /// </summary>
        public static string ControlerSys_Async {
            get {
                return ResourceManager.GetString("ControlerSys_Async", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自动运行.
        /// </summary>
        public static string ControlerSys_Auto_op {
            get {
                return ResourceManager.GetString("ControlerSys_Auto_op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自动运行模式：.
        /// </summary>
        public static string ControlerSys_Auto_op_mode {
            get {
                return ResourceManager.GetString("ControlerSys_Auto_op_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴示教.
        /// </summary>
        public static string ControlerSys_Axis_teach {
            get {
                return ResourceManager.GetString("ControlerSys_Axis_teach", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接驳示教.
        /// </summary>
        public static string ControlerSys_Conn_teach {
            get {
                return ResourceManager.GetString("ControlerSys_Conn_teach", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制模式：.
        /// </summary>
        public static string ControlerSys_Ctrl_mode {
            get {
                return ResourceManager.GetString("ControlerSys_Ctrl_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制对象：.
        /// </summary>
        public static string ControlerSys_Ctrl_obj {
            get {
                return ResourceManager.GetString("ControlerSys_Ctrl_obj", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 执行.
        /// </summary>
        public static string ControlerSys_Exec {
            get {
                return ResourceManager.GetString("ControlerSys_Exec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 直线电机.
        /// </summary>
        public static string ControlerSys_Linear_motor {
            get {
                return ResourceManager.GetString("ControlerSys_Linear_motor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 动子.
        /// </summary>
        public static string ControlerSys_Mover {
            get {
                return ResourceManager.GetString("ControlerSys_Mover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 读取.
        /// </summary>
        public static string ControlerSys_Read {
            get {
                return ResourceManager.GetString("ControlerSys_Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转电机.
        /// </summary>
        public static string ControlerSys_Rotary_motor {
            get {
                return ResourceManager.GetString("ControlerSys_Rotary_motor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择操作：.
        /// </summary>
        public static string ControlerSys_Sel_op {
            get {
                return ResourceManager.GetString("ControlerSys_Sel_op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站节点ID：.
        /// </summary>
        public static string ControlerSys_Slave_node_id {
            get {
                return ResourceManager.GetString("ControlerSys_Slave_node_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度百分比：.
        /// </summary>
        public static string ControlerSys_Speed_perc {
            get {
                return ResourceManager.GetString("ControlerSys_Speed_perc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 同步.
        /// </summary>
        public static string ControlerSys_Sync {
            get {
                return ResourceManager.GetString("ControlerSys_Sync", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统控制.
        /// </summary>
        public static string ControlerSys_Sys_ctrl {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统错误轴ID.
        /// </summary>
        public static string ControlerSys_Sys_err_axis_id {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_err_axis_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统错误代码.
        /// </summary>
        public static string ControlerSys_Sys_err_code {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_err_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统错误驱动.
        /// </summary>
        public static string ControlerSys_Sys_err_driver {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_err_driver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统错误码.
        /// </summary>
        public static string ControlerSys_Sys_err_num {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_err_num", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统运行模式：.
        /// </summary>
        public static string ControlerSys_Sys_op_mode {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_op_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统状态.
        /// </summary>
        public static string ControlerSys_Sys_stat {
            get {
                return ResourceManager.GetString("ControlerSys_Sys_stat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统驱动错误.
        /// </summary>
        public static string ControlerSys_System_drive_error {
            get {
                return ResourceManager.GetString("ControlerSys_System_drive_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 适配器解析数据失败！.
        /// </summary>
        public static string ControlerTcpClient_Adapter_parsing_failed {
            get {
                return ResourceManager.GetString("ControlerTcpClient_Adapter_parsing_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器心跳发送失败.
        /// </summary>
        public static string ControlerTcpClient_Controller_heartbeat_failed {
            get {
                return ResourceManager.GetString("ControlerTcpClient_Controller_heartbeat_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器未连接！.
        /// </summary>
        public static string ControlerTcpClient_Controller_not_connected {
            get {
                return ResourceManager.GetString("ControlerTcpClient_Controller_not_connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 发送数据：.
        /// </summary>
        public static string ControlerTcpClient_Send_data {
            get {
                return ResourceManager.GetString("ControlerTcpClient_Send_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 加速度：.
        /// </summary>
        public static string ControlerTranStatus_Accel {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Accel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 实际位置.
        /// </summary>
        public static string ControlerTranStatus_Act_pos {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Act_pos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 实际速度.
        /// </summary>
        public static string ControlerTranStatus_Act_speed {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Act_speed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接驳配置：.
        /// </summary>
        public static string ControlerTranStatus_Conn_conf {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Conn_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接驳控制.
        /// </summary>
        public static string ControlerTranStatus_Conn_ctrl {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Conn_ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接驳ID:.
        /// </summary>
        public static string ControlerTranStatus_Conn_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Conn_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接状态：.
        /// </summary>
        public static string ControlerTranStatus_Conn_stat {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Conn_stat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制命令：.
        /// </summary>
        public static string ControlerTranStatus_Ctrl_cmd {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Ctrl_cmd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 当前对象ID：.
        /// </summary>
        public static string ControlerTranStatus_Curr_obj_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Curr_obj_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 减速度：.
        /// </summary>
        public static string ControlerTranStatus_Decel {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Decel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 断开连接.
        /// </summary>
        public static string ControlerTranStatus_Disconnect {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 使能状态.
        /// </summary>
        public static string ControlerTranStatus_Enable_stat {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Enable_stat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 错误码.
        /// </summary>
        public static string ControlerTranStatus_Err_code {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Err_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 建立连接.
        /// </summary>
        public static string ControlerTranStatus_Est_conn {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Est_conn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 执行.
        /// </summary>
        public static string ControlerTranStatus_Exec {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Exec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 回零完成.
        /// </summary>
        public static string ControlerTranStatus_Homing_done {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Homing_done", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 左侧对象ID：.
        /// </summary>
        public static string ControlerTranStatus_Left_obj_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Left_obj_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体ID.
        /// </summary>
        public static string ControlerTranStatus_Line_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Line_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体左侧连接对象ID.
        /// </summary>
        public static string ControlerTranStatus_Line_left_conn_obj_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Line_left_conn_obj_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体右侧连接对象ID.
        /// </summary>
        public static string ControlerTranStatus_Line_right_conn_obj_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Line_right_conn_obj_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 读取.
        /// </summary>
        public static string ControlerTranStatus_Read {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 右侧对象ID：.
        /// </summary>
        public static string ControlerTranStatus_Right_obj_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Right_obj_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 运行状态.
        /// </summary>
        public static string ControlerTranStatus_Run_stat {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Run_stat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择操作：.
        /// </summary>
        public static string ControlerTranStatus_Sel_op {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Sel_op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度：.
        /// </summary>
        public static string ControlerTranStatus_Speed {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Speed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标位置：.
        /// </summary>
        public static string ControlerTranStatus_Target_pos {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Target_pos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 目标工位ID:.
        /// </summary>
        public static string ControlerTranStatus_Target_station_id {
            get {
                return ResourceManager.GetString("ControlerTranStatus_Target_station_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 不做处理.
        /// </summary>
        public static string ControlerTranStatusViewModel_Do_nothing {
            get {
                return ResourceManager.GetString("ControlerTranStatusViewModel_Do_nothing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 绝对运动.
        /// </summary>
        public static string ControllerConst_Absolute_movement {
            get {
                return ResourceManager.GetString("ControllerConst_Absolute_movement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 向后点动.
        /// </summary>
        public static string ControllerConst_Backward_jog {
            get {
                return ResourceManager.GetString("ControllerConst_Backward_jog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 向前点动.
        /// </summary>
        public static string ControllerConst_Forward_jog {
            get {
                return ResourceManager.GetString("ControllerConst_Forward_jog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 下使能.
        /// </summary>
        public static string ControllerConst_Lower_enable {
            get {
                return ResourceManager.GetString("ControllerConst_Lower_enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 相对运动.
        /// </summary>
        public static string ControllerConst_Relative_movement {
            get {
                return ResourceManager.GetString("ControllerConst_Relative_movement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 重置.
        /// </summary>
        public static string ControllerConst_Reset {
            get {
                return ResourceManager.GetString("ControllerConst_Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设置零点.
        /// </summary>
        public static string ControllerConst_Set_zero_point {
            get {
                return ResourceManager.GetString("ControllerConst_Set_zero_point", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 停止.
        /// </summary>
        public static string ControllerConst_Stop {
            get {
                return ResourceManager.GetString("ControllerConst_Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 上使能.
        /// </summary>
        public static string ControllerConst_Upper_enable {
            get {
                return ResourceManager.GetString("ControllerConst_Upper_enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位运动.
        /// </summary>
        public static string ControllerConst_Workstation_movement {
            get {
                return ResourceManager.GetString("ControllerConst_Workstation_movement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴报警.
        /// </summary>
        public static string ControllerHelper_Axis_alarm {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_alarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴驱动器错误.
        /// </summary>
        public static string ControllerHelper_Axis_driver_error {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_driver_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴错误状态.
        /// </summary>
        public static string ControllerHelper_Axis_error_status {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_error_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴已到达目标位置.
        /// </summary>
        public static string ControllerHelper_Axis_has_reached_the_target_position {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_has_reached_the_target_position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴左到位.
        /// </summary>
        public static string ControllerHelper_Axis_in_left_position {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_in_left_position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴右到位.
        /// </summary>
        public static string ControllerHelper_Axis_in_right_position {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_in_right_position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴在工位上.
        /// </summary>
        public static string ControllerHelper_Axis_is_at_the_workstation {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_is_at_the_workstation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴已使能.
        /// </summary>
        public static string ControllerHelper_Axis_is_enabled {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_is_enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴正在运行.
        /// </summary>
        public static string ControllerHelper_Axis_is_running {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_is_running", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴运动错误.
        /// </summary>
        public static string ControllerHelper_Axis_movement_error {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_movement_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴通知.
        /// </summary>
        public static string ControllerHelper_Axis_notification {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_notification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴状态.
        /// </summary>
        public static string ControllerHelper_Axis_status {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴警告.
        /// </summary>
        public static string ControllerHelper_Axis_warning {
            get {
                return ResourceManager.GetString("ControllerHelper_Axis_warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴负限位.
        /// </summary>
        public static string ControllerHelper_Negative_limit_of_axis {
            get {
                return ResourceManager.GetString("ControllerHelper_Negative_limit_of_axis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴正限位.
        /// </summary>
        public static string ControllerHelper_Positive_limit_of_axis {
            get {
                return ResourceManager.GetString("ControllerHelper_Positive_limit_of_axis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转轴.
        /// </summary>
        public static string ControllerHelper_Rotary_axis {
            get {
                return ResourceManager.GetString("ControllerHelper_Rotary_axis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转轴回零完成.
        /// </summary>
        public static string ControllerHelper_Rotary_axis_homing_completed {
            get {
                return ResourceManager.GetString("ControllerHelper_Rotary_axis_homing_completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转轴已使能.
        /// </summary>
        public static string ControllerHelper_Rotary_axis_is_enabled {
            get {
                return ResourceManager.GetString("ControllerHelper_Rotary_axis_is_enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 旋转轴正在运行.
        /// </summary>
        public static string ControllerHelper_Rotary_axis_is_running {
            get {
                return ResourceManager.GetString("ControllerHelper_Rotary_axis_is_running", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统总线已连接.
        /// </summary>
        public static string ControllerHelper_System_bus_is_connected {
            get {
                return ResourceManager.GetString("ControllerHelper_System_bus_is_connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统已启用.
        /// </summary>
        public static string ControllerHelper_System_is_enabled {
            get {
                return ResourceManager.GetString("ControllerHelper_System_is_enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统处于错误状态.
        /// </summary>
        public static string ControllerHelper_System_is_in_error_state {
            get {
                return ResourceManager.GetString("ControllerHelper_System_is_in_error_state", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统已准备好.
        /// </summary>
        public static string ControllerHelper_System_is_ready {
            get {
                return ResourceManager.GetString("ControllerHelper_System_is_ready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统正在运行.
        /// </summary>
        public static string ControllerHelper_System_is_running {
            get {
                return ResourceManager.GetString("ControllerHelper_System_is_running", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器连接成功！.
        /// </summary>
        public static string DataViewModel_Controller_connected {
            get {
                return ResourceManager.GetString("DataViewModel_Controller_connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器连接断开！.
        /// </summary>
        public static string DataViewModel_Controller_disconnected {
            get {
                return ResourceManager.GetString("DataViewModel_Controller_disconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 主线程.
        /// </summary>
        public static string DesignerHelper_Main_thread {
            get {
                return ResourceManager.GetString("DesignerHelper_Main_thread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 第三个指令不存在.
        /// </summary>
        public static string ElectricParaPackage_Third_instruction_not_exist {
            get {
                return ResourceManager.GetString("ElectricParaPackage_Third_instruction_not_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 不存在相应的目录.
        /// </summary>
        public static string FileHelper_Directory_not_exist {
            get {
                return ResourceManager.GetString("FileHelper_Directory_not_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新追加内容.
        /// </summary>
        public static string FileHelper_Newly_appended_content {
            get {
                return ResourceManager.GetString("FileHelper_Newly_appended_content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 这是我写的内容啊.
        /// </summary>
        public static string FileHelper_What_i_wrote {
            get {
                return ResourceManager.GetString("FileHelper_What_i_wrote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 后退.
        /// </summary>
        public static string FtpClient_Back {
            get {
                return ResourceManager.GetString("FtpClient_Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接.
        /// </summary>
        public static string FtpClient_Connect {
            get {
                return ResourceManager.GetString("FtpClient_Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建文件夹.
        /// </summary>
        public static string FtpClient_Create_folder {
            get {
                return ResourceManager.GetString("FtpClient_Create_folder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除.
        /// </summary>
        public static string FtpClient_Delete {
            get {
                return ResourceManager.GetString("FtpClient_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 断开.
        /// </summary>
        public static string FtpClient_Disconnect {
            get {
                return ResourceManager.GetString("FtpClient_Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 下载到本地.
        /// </summary>
        public static string FtpClient_Download_to_local {
            get {
                return ResourceManager.GetString("FtpClient_Download_to_local", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 前进.
        /// </summary>
        public static string FtpClient_Forward {
            get {
                return ResourceManager.GetString("FtpClient_Forward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 主机:.
        /// </summary>
        public static string FtpClient_Host {
            get {
                return ResourceManager.GetString("FtpClient_Host", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 本地目录: .
        /// </summary>
        public static string FtpClient_Local_directory {
            get {
                return ResourceManager.GetString("FtpClient_Local_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 密码:.
        /// </summary>
        public static string FtpClient_Password {
            get {
                return ResourceManager.GetString("FtpClient_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 端口:.
        /// </summary>
        public static string FtpClient_Port {
            get {
                return ResourceManager.GetString("FtpClient_Port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 刷新.
        /// </summary>
        public static string FtpClient_Refresh {
            get {
                return ResourceManager.GetString("FtpClient_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 远程目录: .
        /// </summary>
        public static string FtpClient_Remote_directory {
            get {
                return ResourceManager.GetString("FtpClient_Remote_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 传输日志:.
        /// </summary>
        public static string FtpClient_Transmission_log {
            get {
                return ResourceManager.GetString("FtpClient_Transmission_log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 向上.
        /// </summary>
        public static string FtpClient_Up {
            get {
                return ResourceManager.GetString("FtpClient_Up", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 上传到服务器.
        /// </summary>
        public static string FtpClient_Upload_to_server {
            get {
                return ResourceManager.GetString("FtpClient_Upload_to_server", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 用户名:.
        /// </summary>
        public static string FtpClient_Username {
            get {
                return ResourceManager.GetString("FtpClient_Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 浏览.
        /// </summary>
        public static string FtpClientViewModel_Browse {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Browse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接.
        /// </summary>
        public static string FtpClientViewModel_Connect {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已连接到FTP服务器.
        /// </summary>
        public static string FtpClientViewModel_Connected_to_ftp_server {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Connected_to_ftp_server", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正在连接到FTP服务器....
        /// </summary>
        public static string FtpClientViewModel_Connecting_to_ftp_server {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Connecting_to_ftp_server", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建目录.
        /// </summary>
        public static string FtpClientViewModel_Create_directory {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Create_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 删除.
        /// </summary>
        public static string FtpClientViewModel_Delete {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已创建目录: .
        /// </summary>
        public static string FtpClientViewModel_Directory_created {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Directory_created", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已删除目录: .
        /// </summary>
        public static string FtpClientViewModel_Directory_deleted {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Directory_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 断开连接.
        /// </summary>
        public static string FtpClientViewModel_Disconnect {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已断开连接.
        /// </summary>
        public static string FtpClientViewModel_Disconnected {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Disconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 下载.
        /// </summary>
        public static string FtpClientViewModel_Download {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Download", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 下载完成: .
        /// </summary>
        public static string FtpClientViewModel_Download_completed {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Download_completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 下载失败: .
        /// </summary>
        public static string FtpClientViewModel_Download_failed {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Download_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正在下载: .
        /// </summary>
        public static string FtpClientViewModel_Downloading {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Downloading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 创建目录失败: .
        /// </summary>
        public static string FtpClientViewModel_Failed_to_create_directory {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Failed_to_create_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 加载本地目录失败: .
        /// </summary>
        public static string FtpClientViewModel_Failed_to_load_local_directory {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Failed_to_load_local_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 加载远程目录失败: .
        /// </summary>
        public static string FtpClientViewModel_Failed_to_load_remote_directory {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Failed_to_load_remote_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已删除文件: .
        /// </summary>
        public static string FtpClientViewModel_File_deleted {
            get {
                return ResourceManager.GetString("FtpClientViewModel_File_deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正在加载本地目录: .
        /// </summary>
        public static string FtpClientViewModel_Loading_local_directory {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Loading_local_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正在加载远程目录: .
        /// </summary>
        public static string FtpClientViewModel_Loading_remote_directory {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Loading_remote_directory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已加载本地目录: .
        /// </summary>
        public static string FtpClientViewModel_Local_directory_loaded {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Local_directory_loaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 打开.
        /// </summary>
        public static string FtpClientViewModel_Open {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 已加载远程目录: .
        /// </summary>
        public static string FtpClientViewModel_Remote_directory_loaded {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Remote_directory_loaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 上传.
        /// </summary>
        public static string FtpClientViewModel_Upload {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 上传完成: .
        /// </summary>
        public static string FtpClientViewModel_Upload_completed {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Upload_completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 上传失败: .
        /// </summary>
        public static string FtpClientViewModel_Upload_failed {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Upload_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 正在上传: .
        /// </summary>
        public static string FtpClientViewModel_Uploading {
            get {
                return ResourceManager.GetString("FtpClientViewModel_Uploading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 开关.
        /// </summary>
        public static string ImageAttached_Switch {
            get {
                return ResourceManager.GetString("ImageAttached_Switch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 输入值必须在指定范围内.
        /// </summary>
        public static string InputConverter_Input_value_range {
            get {
                return ResourceManager.GetString("InputConverter_Input_value_range", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 语言.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴.
        /// </summary>
        public static string LineConfigEnum_Axis {
            get {
                return ResourceManager.GetString("LineConfigEnum_Axis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴偏移.
        /// </summary>
        public static string LineConfigEnum_Axis_offset {
            get {
                return ResourceManager.GetString("LineConfigEnum_Axis_offset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴PID.
        /// </summary>
        public static string LineConfigEnum_Axis_pid {
            get {
                return ResourceManager.GetString("LineConfigEnum_Axis_pid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴运行PID配置参数.
        /// </summary>
        public static string LineConfigEnum_Axis_running_pid_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Axis_running_pid_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴序列.
        /// </summary>
        public static string LineConfigEnum_Axis_sequence {
            get {
                return ResourceManager.GetString("LineConfigEnum_Axis_sequence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴序列配置参数.
        /// </summary>
        public static string LineConfigEnum_Axis_sequence_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Axis_sequence_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置参数.
        /// </summary>
        public static string LineConfigEnum_Configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设备接线方向.
        /// </summary>
        public static string LineConfigEnum_Device_wiring_direction {
            get {
                return ResourceManager.GetString("LineConfigEnum_Device_wiring_direction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体.
        /// </summary>
        public static string LineConfigEnum_Line {
            get {
                return ResourceManager.GetString("LineConfigEnum_Line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体段配置参数.
        /// </summary>
        public static string LineConfigEnum_Line_segment_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Line_segment_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体视图配置参数.
        /// </summary>
        public static string LineConfigEnum_Line_view_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Line_view_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 线体接线方向配置参数.
        /// </summary>
        public static string LineConfigEnum_Line_wiring_direction_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Line_wiring_direction_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机.
        /// </summary>
        public static string LineConfigEnum_Motor {
            get {
                return ResourceManager.GetString("LineConfigEnum_Motor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电机配置参数.
        /// </summary>
        public static string LineConfigEnum_Motor_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Motor_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 动子补偿配置参数.
        /// </summary>
        public static string LineConfigEnum_Rotor_compensation_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Rotor_compensation_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 动子配置参数.
        /// </summary>
        public static string LineConfigEnum_Rotor_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Rotor_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站节点.
        /// </summary>
        public static string LineConfigEnum_Slave_node {
            get {
                return ResourceManager.GetString("LineConfigEnum_Slave_node", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 从站节点配置参数.
        /// </summary>
        public static string LineConfigEnum_Slave_node_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Slave_node_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统.
        /// </summary>
        public static string LineConfigEnum_System {
            get {
                return ResourceManager.GetString("LineConfigEnum_System", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统配置参数.
        /// </summary>
        public static string LineConfigEnum_System_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_System_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UI视图.
        /// </summary>
        public static string LineConfigEnum_Ui_view {
            get {
                return ResourceManager.GetString("LineConfigEnum_Ui_view", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位.
        /// </summary>
        public static string LineConfigEnum_Workstation {
            get {
                return ResourceManager.GetString("LineConfigEnum_Workstation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位补偿配置参数.
        /// </summary>
        public static string LineConfigEnum_Workstation_compensation_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Workstation_compensation_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位偏移.
        /// </summary>
        public static string LineConfigEnum_Workstation_offset {
            get {
                return ResourceManager.GetString("LineConfigEnum_Workstation_offset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位运行配置参数.
        /// </summary>
        public static string LineConfigEnum_Workstation_running_configuration_parameter {
            get {
                return ResourceManager.GetString("LineConfigEnum_Workstation_running_configuration_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 日本語.
        /// </summary>
        public static string LocalizationManager_Japanese {
            get {
                return ResourceManager.GetString("LocalizationManager_Japanese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 中文简体.
        /// </summary>
        public static string LocalizationManager_Simplified_chinese {
            get {
                return ResourceManager.GetString("LocalizationManager_Simplified_chinese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 中文繁體.
        /// </summary>
        public static string LocalizationManager_Traditional_chinese {
            get {
                return ResourceManager.GetString("LocalizationManager_Traditional_chinese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 登录.
        /// </summary>
        public static string Login_Login {
            get {
                return ResourceManager.GetString("Login_Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 密码.
        /// </summary>
        public static string Login_Passwd {
            get {
                return ResourceManager.GetString("Login_Passwd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 记住密码.
        /// </summary>
        public static string Login_Rem_passwd {
            get {
                return ResourceManager.GetString("Login_Rem_passwd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 用户名.
        /// </summary>
        public static string Login_User_name {
            get {
                return ResourceManager.GetString("Login_User_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 报警.
        /// </summary>
        public static string Main_Alarm {
            get {
                return ResourceManager.GetString("Main_Alarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 自动.
        /// </summary>
        public static string Main_Auto {
            get {
                return ResourceManager.GetString("Main_Auto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴控制.
        /// </summary>
        public static string Main_Axis_ctrl {
            get {
                return ResourceManager.GetString("Main_Axis_ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴错误复位.
        /// </summary>
        public static string Main_Axis_err_reset {
            get {
                return ResourceManager.GetString("Main_Axis_err_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 基本设置.
        /// </summary>
        public static string Main_Basic_sett {
            get {
                return ResourceManager.GetString("Main_Basic_sett", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置生成.
        /// </summary>
        public static string Main_Conf_gen {
            get {
                return ResourceManager.GetString("Main_Conf_gen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接断开.
        /// </summary>
        public static string Main_Conn_disconnected {
            get {
                return ResourceManager.GetString("Main_Conn_disconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 接驳状态.
        /// </summary>
        public static string Main_Conn_stat {
            get {
                return ResourceManager.GetString("Main_Conn_stat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接成功.
        /// </summary>
        public static string Main_Conn_successful {
            get {
                return ResourceManager.GetString("Main_Conn_successful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器.
        /// </summary>
        public static string Main_Ctrl {
            get {
                return ResourceManager.GetString("Main_Ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器连接:.
        /// </summary>
        public static string Main_Ctrl_conn {
            get {
                return ResourceManager.GetString("Main_Ctrl_conn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 曲强往复.
        /// </summary>
        public static string Main_Curve_recip {
            get {
                return ResourceManager.GetString("Main_Curve_recip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数据追溯.
        /// </summary>
        public static string Main_Data_trace {
            get {
                return ResourceManager.GetString("Main_Data_trace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 倔差补偿.
        /// </summary>
        public static string Main_Dev_comp {
            get {
                return ResourceManager.GetString("Main_Dev_comp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数字IO.
        /// </summary>
        public static string Main_Digital_io {
            get {
                return ResourceManager.GetString("Main_Digital_io", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器.
        /// </summary>
        public static string Main_Driver {
            get {
                return ResourceManager.GetString("Main_Driver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器连接:.
        /// </summary>
        public static string Main_Driver_conn {
            get {
                return ResourceManager.GetString("Main_Driver_conn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 急停.
        /// </summary>
        public static string Main_Emergency_stop {
            get {
                return ResourceManager.GetString("Main_Emergency_stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 使能.
        /// </summary>
        public static string Main_Enable {
            get {
                return ResourceManager.GetString("Main_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设备连接.
        /// </summary>
        public static string Main_Equip_conn {
            get {
                return ResourceManager.GetString("Main_Equip_conn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 错误故障.
        /// </summary>
        public static string Main_Err_fault {
            get {
                return ResourceManager.GetString("Main_Err_fault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 故障.
        /// </summary>
        public static string Main_Fault {
            get {
                return ResourceManager.GetString("Main_Fault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 反馈信息.
        /// </summary>
        public static string Main_Feedback_info {
            get {
                return ResourceManager.GetString("Main_Feedback_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 功能列表.
        /// </summary>
        public static string Main_Func_list {
            get {
                return ResourceManager.GetString("Main_Func_list", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 固件升级.
        /// </summary>
        public static string Main_Fw_upgrade {
            get {
                return ResourceManager.GetString("Main_Fw_upgrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 初始化.
        /// </summary>
        public static string Main_Init {
            get {
                return ResourceManager.GetString("Main_Init", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 维护.
        /// </summary>
        public static string Main_Maint {
            get {
                return ResourceManager.GetString("Main_Maint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 手动.
        /// </summary>
        public static string Main_Manual {
            get {
                return ResourceManager.GetString("Main_Manual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 离线配置.
        /// </summary>
        public static string Main_Offline_conf {
            get {
                return ResourceManager.GetString("Main_Offline_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在线配置.
        /// </summary>
        public static string Main_Online_conf {
            get {
                return ResourceManager.GetString("Main_Online_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在线演示.
        /// </summary>
        public static string Main_Online_demonstration {
            get {
                return ResourceManager.GetString("Main_Online_demonstration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作日志.
        /// </summary>
        public static string Main_Op_log {
            get {
                return ResourceManager.GetString("Main_Op_log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 示波器.
        /// </summary>
        public static string Main_Oscillo {
            get {
                return ResourceManager.GetString("Main_Oscillo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 权限分配.
        /// </summary>
        public static string Main_Perm_assign {
            get {
                return ResourceManager.GetString("Main_Perm_assign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 明码报文.
        /// </summary>
        public static string Main_Plaintext_msg {
            get {
                return ResourceManager.GetString("Main_Plaintext_msg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 复位.
        /// </summary>
        public static string Main_Reset {
            get {
                return ResourceManager.GetString("Main_Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色管理.
        /// </summary>
        public static string Main_Role_mgmt {
            get {
                return ResourceManager.GetString("Main_Role_mgmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 运行.
        /// </summary>
        public static string Main_Running {
            get {
                return ResourceManager.GetString("Main_Running", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 保存.
        /// </summary>
        public static string Main_Save {
            get {
                return ResourceManager.GetString("Main_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择轴序号:.
        /// </summary>
        public static string Main_Sel_axis_sn {
            get {
                return ResourceManager.GetString("Main_Sel_axis_sn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 伺服配置.
        /// </summary>
        public static string Main_Servo_conf {
            get {
                return ResourceManager.GetString("Main_Servo_conf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 启动.
        /// </summary>
        public static string Main_Start {
            get {
                return ResourceManager.GetString("Main_Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位控制.
        /// </summary>
        public static string Main_Station_ctrl {
            get {
                return ResourceManager.GetString("Main_Station_ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 启用工位.
        /// </summary>
        public static string Main_Station_enable {
            get {
                return ResourceManager.GetString("Main_Station_enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 工位初始化.
        /// </summary>
        public static string Main_Station_init {
            get {
                return ResourceManager.GetString("Main_Station_init", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 屏蔽工位.
        /// </summary>
        public static string Main_Station_mask {
            get {
                return ResourceManager.GetString("Main_Station_mask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 停止.
        /// </summary>
        public static string Main_Stop {
            get {
                return ResourceManager.GetString("Main_Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统总成.
        /// </summary>
        public static string Main_Sys_assembly {
            get {
                return ResourceManager.GetString("Main_Sys_assembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统控制.
        /// </summary>
        public static string Main_Sys_ctrl {
            get {
                return ResourceManager.GetString("Main_Sys_ctrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 重启系统.
        /// </summary>
        public static string Main_Sys_restart {
            get {
                return ResourceManager.GetString("Main_Sys_restart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 用户管理.
        /// </summary>
        public static string Main_User_mgmt {
            get {
                return ResourceManager.GetString("Main_User_mgmt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制器反馈轴数为0，无法进行此操作！.
        /// </summary>
        public static string MainViewModel_Controller_feedback_zero {
            get {
                return ResourceManager.GetString("MainViewModel_Controller_feedback_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 清除所有通知.
        /// </summary>
        public static string NoticeListControl_Clear_all_notifications {
            get {
                return ResourceManager.GetString("NoticeListControl_Clear_all_notifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 反馈信息.
        /// </summary>
        public static string NoticeListControl_Feedback_information {
            get {
                return ResourceManager.GetString("NoticeListControl_Feedback_information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 消息内容.
        /// </summary>
        public static string NoticeListControl_Message_content {
            get {
                return ResourceManager.GetString("NoticeListControl_Message_content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 来源.
        /// </summary>
        public static string NoticeListControl_Source {
            get {
                return ResourceManager.GetString("NoticeListControl_Source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 类型.
        /// </summary>
        public static string NoticeListControl_Type {
            get {
                return ResourceManager.GetString("NoticeListControl_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 传入对象不能为空！.
        /// </summary>
        public static string ObjectUtil_Object_not_empty {
            get {
                return ResourceManager.GetString("ObjectUtil_Object_not_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 无描述.
        /// </summary>
        public static string OnlineConfigService_No_description {
            get {
                return ResourceManager.GetString("OnlineConfigService_No_description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 无值.
        /// </summary>
        public static string OnlineConfigService_No_value {
            get {
                return ResourceManager.GetString("OnlineConfigService_No_value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 未知.
        /// </summary>
        public static string OnlineConfigService_Unknown {
            get {
                return ResourceManager.GetString("OnlineConfigService_Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 行为.
        /// </summary>
        public static string OperateLog_Behav {
            get {
                return ResourceManager.GetString("OperateLog_Behav", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 取消.
        /// </summary>
        public static string OperateLog_Cancel {
            get {
                return ResourceManager.GetString("OperateLog_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 描述.
        /// </summary>
        public static string OperateLog_Desc {
            get {
                return ResourceManager.GetString("OperateLog_Desc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 详细描述:.
        /// </summary>
        public static string OperateLog_Detailed_desc {
            get {
                return ResourceManager.GetString("OperateLog_Detailed_desc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 详细.
        /// </summary>
        public static string OperateLog_Details {
            get {
                return ResourceManager.GetString("OperateLog_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请输入关键字.
        /// </summary>
        public static string OperateLog_Enter_keywords {
            get {
                return ResourceManager.GetString("OperateLog_Enter_keywords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 模块.
        /// </summary>
        public static string OperateLog_Module {
            get {
                return ResourceManager.GetString("OperateLog_Module", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作.
        /// </summary>
        public static string OperateLog_Op {
            get {
                return ResourceManager.GetString("OperateLog_Op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 操作人.
        /// </summary>
        public static string OperateLog_Operator {
            get {
                return ResourceManager.GetString("OperateLog_Operator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 刷新.
        /// </summary>
        public static string OperateLog_Refresh {
            get {
                return ResourceManager.GetString("OperateLog_Refresh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 起始时间: .
        /// </summary>
        public static string OperateLog_Start_time {
            get {
                return ResourceManager.GetString("OperateLog_Start_time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 时间.
        /// </summary>
        public static string OperateLog_Time {
            get {
                return ResourceManager.GetString("OperateLog_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 查看.
        /// </summary>
        public static string OperateLog_View {
            get {
                return ResourceManager.GetString("OperateLog_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 输入值超出限制！.
        /// </summary>
        public static string ParameterModel_Input_value_exceed_limit {
            get {
                return ResourceManager.GetString("ParameterModel_Input_value_exceed_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 输入值错误！.
        /// </summary>
        public static string ParameterModel_Input_value_incorrect {
            get {
                return ResourceManager.GetString("ParameterModel_Input_value_incorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ParameterModelExtension异常.
        /// </summary>
        public static string ParameterModelExtension_Parameter_model_extension_exception {
            get {
                return ResourceManager.GetString("ParameterModelExtension_Parameter_model_extension_exception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 6-控制状态.
        /// </summary>
        public static string ParamTableEnum_Control_status {
            get {
                return ResourceManager.GetString("ParamTableEnum_Control_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3-编码器参数.
        /// </summary>
        public static string ParamTableEnum_Encoder_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_Encoder_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5-故障记录.
        /// </summary>
        public static string ParamTableEnum_Fault_record {
            get {
                return ResourceManager.GetString("ParamTableEnum_Fault_record", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1-电机参数.
        /// </summary>
        public static string ParamTableEnum_Motor_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_Motor_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 7-位置参数.
        /// </summary>
        public static string ParamTableEnum_Position_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_Position_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4-保护参数.
        /// </summary>
        public static string ParamTableEnum_Protection_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_Protection_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8-速度参数.
        /// </summary>
        public static string ParamTableEnum_Speed_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_Speed_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2-系统参数.
        /// </summary>
        public static string ParamTableEnum_System_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_System_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 9-转矩参数.
        /// </summary>
        public static string ParamTableEnum_Torque_parameter {
            get {
                return ResourceManager.GetString("ParamTableEnum_Torque_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 您没有该操作权限.
        /// </summary>
        public static string PermissionHelper_No_permission_operation {
            get {
                return ResourceManager.GetString("PermissionHelper_No_permission_operation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 您没有该菜单的权限.
        /// </summary>
        public static string PromptUserControl_No_menu_perm {
            get {
                return ResourceManager.GetString("PromptUserControl_No_menu_perm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 按钮.
        /// </summary>
        public static string RecursionHelper_Button {
            get {
                return ResourceManager.GetString("RecursionHelper_Button", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色名不能为空.
        /// </summary>
        public static string RoleDto_Role_name_cannot_be_empty {
            get {
                return ResourceManager.GetString("RoleDto_Role_name_cannot_be_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 角色名不能为空.
        /// </summary>
        public static string RoleDto_Role_name_not_empty {
            get {
                return ResourceManager.GetString("RoleDto_Role_name_not_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 通道.
        /// </summary>
        public static string Scope_Channel {
            get {
                return ResourceManager.GetString("Scope_Channel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 查错.
        /// </summary>
        public static string Scope_Check_err {
            get {
                return ResourceManager.GetString("Scope_Check_err", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 采集.
        /// </summary>
        public static string Scope_Collect {
            get {
                return ResourceManager.GetString("Scope_Collect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 颜色.
        /// </summary>
        public static string Scope_Color {
            get {
                return ResourceManager.GetString("Scope_Color", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 十字星.
        /// </summary>
        public static string Scope_Cross_star {
            get {
                return ResourceManager.GetString("Scope_Cross_star", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 调试.
        /// </summary>
        public static string Scope_Debug {
            get {
                return ResourceManager.GetString("Scope_Debug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 导出.
        /// </summary>
        public static string Scope_Export {
            get {
                return ResourceManager.GetString("Scope_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 导入.
        /// </summary>
        public static string Scope_Import {
            get {
                return ResourceManager.GetString("Scope_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 是否可见.
        /// </summary>
        public static string Scope_Is_visible {
            get {
                return ResourceManager.GetString("Scope_Is_visible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 放大倍数.
        /// </summary>
        public static string Scope_Magni {
            get {
                return ResourceManager.GetString("Scope_Magni", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 偏移量.
        /// </summary>
        public static string Scope_Offset {
            get {
                return ResourceManager.GetString("Scope_Offset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请选择.
        /// </summary>
        public static string Scope_Please_select {
            get {
                return ResourceManager.GetString("Scope_Please_select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 重置.
        /// </summary>
        public static string Scope_Reset {
            get {
                return ResourceManager.GetString("Scope_Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 采样频率(1-300，单位ms)：.
        /// </summary>
        public static string Scope_Sample_freq_1_300_ms {
            get {
                return ResourceManager.GetString("Scope_Sample_freq_1_300_ms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择对象.
        /// </summary>
        public static string Scope_Sel_obj {
            get {
                return ResourceManager.GetString("Scope_Sel_obj", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 停止.
        /// </summary>
        public static string Scope_Stop {
            get {
                return ResourceManager.GetString("Scope_Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数值.
        /// </summary>
        public static string Scope_Value {
            get {
                return ResourceManager.GetString("Scope_Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to X轴标尺.
        /// </summary>
        public static string Scope_X_axis_scale {
            get {
                return ResourceManager.GetString("Scope_X_axis_scale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Y轴标尺.
        /// </summary>
        public static string Scope_Y_axis_scale {
            get {
                return ResourceManager.GetString("Scope_Y_axis_scale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 缩放.
        /// </summary>
        public static string Scope_Zoom {
            get {
                return ResourceManager.GetString("Scope_Zoom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0母线电压.
        /// </summary>
        public static string ScopeConst_Axis0_bus_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_bus_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0_控制电压.
        /// </summary>
        public static string ScopeConst_Axis0_control_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_control_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0电流反馈.
        /// </summary>
        public static string ScopeConst_Axis0_current_feedback {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_current_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0电流指令.
        /// </summary>
        public static string ScopeConst_Axis0_current_instruction {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_current_instruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0 D轴参考电压.
        /// </summary>
        public static string ScopeConst_Axis0_d_axis_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_d_axis_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0位置反馈.
        /// </summary>
        public static string ScopeConst_Axis0_position_feedback {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_position_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0 Q轴参考电压.
        /// </summary>
        public static string ScopeConst_Axis0_q_axis_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_q_axis_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0速度反馈.
        /// </summary>
        public static string ScopeConst_Axis0_speed_feedback {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_speed_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0速度指令.
        /// </summary>
        public static string ScopeConst_Axis0_speed_instruction {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_speed_instruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0_U相电流.
        /// </summary>
        public static string ScopeConst_Axis0_u_phase_current {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_u_phase_current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0_V相电流.
        /// </summary>
        public static string ScopeConst_Axis0_v_phase_current {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_v_phase_current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0_W相电流.
        /// </summary>
        public static string ScopeConst_Axis0_w_phase_current {
            get {
                return ResourceManager.GetString("ScopeConst_Axis0_w_phase_current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1母线电压.
        /// </summary>
        public static string ScopeConst_Axis1_bus_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_bus_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1_控制电压.
        /// </summary>
        public static string ScopeConst_Axis1_control_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_control_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1电流反馈.
        /// </summary>
        public static string ScopeConst_Axis1_current_feedback {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_current_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1电流指令.
        /// </summary>
        public static string ScopeConst_Axis1_current_instruction {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_current_instruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1 D轴参考电压.
        /// </summary>
        public static string ScopeConst_Axis1_d_axis_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_d_axis_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1位置反馈.
        /// </summary>
        public static string ScopeConst_Axis1_position_feedback {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_position_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1 Q轴参考电压.
        /// </summary>
        public static string ScopeConst_Axis1_q_axis_voltage {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_q_axis_voltage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1速度反馈.
        /// </summary>
        public static string ScopeConst_Axis1_speed_feedback {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_speed_feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1速度指令.
        /// </summary>
        public static string ScopeConst_Axis1_speed_instruction {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_speed_instruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1_U相电流.
        /// </summary>
        public static string ScopeConst_Axis1_u_phase_current {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_u_phase_current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1_V相电流.
        /// </summary>
        public static string ScopeConst_Axis1_v_phase_current {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_v_phase_current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴1_W相电流.
        /// </summary>
        public static string ScopeConst_Axis1_w_phase_current {
            get {
                return ResourceManager.GetString("ScopeConst_Axis1_w_phase_current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电流参数.
        /// </summary>
        public static string ScopeConst_Current_parameter {
            get {
                return ResourceManager.GetString("ScopeConst_Current_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 位置参数.
        /// </summary>
        public static string ScopeConst_Position_parameter {
            get {
                return ResourceManager.GetString("ScopeConst_Position_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 速度参数.
        /// </summary>
        public static string ScopeConst_Speed_parameter {
            get {
                return ResourceManager.GetString("ScopeConst_Speed_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 电压参数.
        /// </summary>
        public static string ScopeConst_Voltage_parameter {
            get {
                return ResourceManager.GetString("ScopeConst_Voltage_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV 文件 (*.csv)|*.csv|所有文件 (*.*)|*.*.
        /// </summary>
        public static string ScopeView_xaml_Csv_file_filter {
            get {
                return ResourceManager.GetString("ScopeView_xaml_Csv_file_filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数据导出成功.
        /// </summary>
        public static string ScopeView_xaml_Data_export_success {
            get {
                return ResourceManager.GetString("ScopeView_xaml_Data_export_success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请选择一个CSV文件.
        /// </summary>
        public static string ScopeView_xaml_Select_csv_file {
            get {
                return ResourceManager.GetString("ScopeView_xaml_Select_csv_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 请选择保存路径.
        /// </summary>
        public static string ScopeView_xaml_Select_save_path {
            get {
                return ResourceManager.GetString("ScopeView_xaml_Select_save_path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 新的SerialPort必须在连接状态。.
        /// </summary>
        public static string SerialCore_New_serial_port_connected {
            get {
                return ResourceManager.GetString("SerialCore_New_serial_port_connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 远程终端已关闭.
        /// </summary>
        public static string SerialCore_Remote_terminal_closed {
            get {
                return ResourceManager.GetString("SerialCore_Remote_terminal_closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 当前适配器不支持对象发送。.
        /// </summary>
        public static string SerialPortClient_Adapter_not_support_send {
            get {
                return ResourceManager.GetString("SerialPortClient_Adapter_not_support_send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 配置文件不能为空。.
        /// </summary>
        public static string SerialPortClient_Config_file_not_empty {
            get {
                return ResourceManager.GetString("SerialPortClient_Config_file_not_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在处理数据时发生错误.
        /// </summary>
        public static string SerialPortClient_Data_processing_error {
            get {
                return ResourceManager.GetString("SerialPortClient_Data_processing_error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 串口配置不能为空。.
        /// </summary>
        public static string SerialPortClient_Serial_port_config_not_empty {
            get {
                return ResourceManager.GetString("SerialPortClient_Serial_port_config_not_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 6-控制状态.
        /// </summary>
        public static string ServoContext_Control_status {
            get {
                return ResourceManager.GetString("ServoContext_Control_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3-编码器参数.
        /// </summary>
        public static string ServoContext_Encoder_parameter {
            get {
                return ResourceManager.GetString("ServoContext_Encoder_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5-故障记录.
        /// </summary>
        public static string ServoContext_Fault_record {
            get {
                return ResourceManager.GetString("ServoContext_Fault_record", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GetFromDriveContext异常.
        /// </summary>
        public static string ServoContext_Get_from_drive_context_exception {
            get {
                return ResourceManager.GetString("ServoContext_Get_from_drive_context_exception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1-电机参数.
        /// </summary>
        public static string ServoContext_Motor_parameter {
            get {
                return ResourceManager.GetString("ServoContext_Motor_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 7-位置参数.
        /// </summary>
        public static string ServoContext_Position_parameter {
            get {
                return ResourceManager.GetString("ServoContext_Position_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4-保护参数.
        /// </summary>
        public static string ServoContext_Protection_parameter {
            get {
                return ResourceManager.GetString("ServoContext_Protection_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8-速度参数.
        /// </summary>
        public static string ServoContext_Speed_parameter {
            get {
                return ResourceManager.GetString("ServoContext_Speed_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2-系统参数.
        /// </summary>
        public static string ServoContext_System_parameter {
            get {
                return ResourceManager.GetString("ServoContext_System_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 9-转矩参数.
        /// </summary>
        public static string ServoContext_Torque_parameter {
            get {
                return ResourceManager.GetString("ServoContext_Torque_parameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 波特率.
        /// </summary>
        public static string ServoSerialPort_Baud_rate {
            get {
                return ResourceManager.GetString("ServoSerialPort_Baud_rate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 连接.
        /// </summary>
        public static string ServoSerialPort_Connect {
            get {
                return ResourceManager.GetString("ServoSerialPort_Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 数据位.
        /// </summary>
        public static string ServoSerialPort_Data_bits {
            get {
                return ResourceManager.GetString("ServoSerialPort_Data_bits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 断开.
        /// </summary>
        public static string ServoSerialPort_Disconnect {
            get {
                return ResourceManager.GetString("ServoSerialPort_Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器连接.
        /// </summary>
        public static string ServoSerialPort_Driver_conn {
            get {
                return ResourceManager.GetString("ServoSerialPort_Driver_conn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 校验位.
        /// </summary>
        public static string ServoSerialPort_Parity_bit {
            get {
                return ResourceManager.GetString("ServoSerialPort_Parity_bit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 串口.
        /// </summary>
        public static string ServoSerialPort_Serial_port {
            get {
                return ResourceManager.GetString("ServoSerialPort_Serial_port", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 停止位.
        /// </summary>
        public static string ServoSerialPort_Stop_bits {
            get {
                return ResourceManager.GetString("ServoSerialPort_Stop_bits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器连接成功！.
        /// </summary>
        public static string ServoSerialPortClient_Driver_connected_successfully {
            get {
                return ResourceManager.GetString("ServoSerialPortClient_Driver_connected_successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器断开连接！.
        /// </summary>
        public static string ServoSerialPortClient_Driver_disconnected {
            get {
                return ResourceManager.GetString("ServoSerialPortClient_Driver_disconnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器参数恢复成功！.
        /// </summary>
        public static string ServoSerialPortClient_Driver_parameter_recovery_successful {
            get {
                return ResourceManager.GetString("ServoSerialPortClient_Driver_parameter_recovery_successful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 伺服心跳发送失败.
        /// </summary>
        public static string ServoSerialPortClient_Servo_heartbeat_failed {
            get {
                return ResourceManager.GetString("ServoSerialPortClient_Servo_heartbeat_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系数.
        /// </summary>
        public static string ServoSetting_Coeff {
            get {
                return ResourceManager.GetString("ServoSetting_Coeff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制权:.
        /// </summary>
        public static string ServoSetting_Ctrl_right {
            get {
                return ResourceManager.GetString("ServoSetting_Ctrl_right", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 描述.
        /// </summary>
        public static string ServoSetting_Desc {
            get {
                return ResourceManager.GetString("ServoSetting_Desc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动模式设置:.
        /// </summary>
        public static string ServoSetting_Drive_mode_set {
            get {
                return ResourceManager.GetString("ServoSetting_Drive_mode_set", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 驱动器参数.
        /// </summary>
        public static string ServoSetting_Driver_params {
            get {
                return ResourceManager.GetString("ServoSetting_Driver_params", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 错误复位.
        /// </summary>
        public static string ServoSetting_Err_reset {
            get {
                return ResourceManager.GetString("ServoSetting_Err_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 故障记录清除.
        /// </summary>
        public static string ServoSetting_Fault_rec_clear {
            get {
                return ResourceManager.GetString("ServoSetting_Fault_rec_clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 本地控制模式:.
        /// </summary>
        public static string ServoSetting_Local_ctrl_mode {
            get {
                return ResourceManager.GetString("ServoSetting_Local_ctrl_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最大值.
        /// </summary>
        public static string ServoSetting_Max_val {
            get {
                return ResourceManager.GetString("ServoSetting_Max_val", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 最小值.
        /// </summary>
        public static string ServoSetting_Min_val {
            get {
                return ResourceManager.GetString("ServoSetting_Min_val", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 监控.
        /// </summary>
        public static string ServoSetting_Monitor {
            get {
                return ResourceManager.GetString("ServoSetting_Monitor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 参数名称.
        /// </summary>
        public static string ServoSetting_Param_name {
            get {
                return ResourceManager.GetString("ServoSetting_Param_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 权限.
        /// </summary>
        public static string ServoSetting_Perm {
            get {
                return ResourceManager.GetString("ServoSetting_Perm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 读取值.
        /// </summary>
        public static string ServoSetting_Read_val {
            get {
                return ResourceManager.GetString("ServoSetting_Read_val", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 恢复默认参数.
        /// </summary>
        public static string ServoSetting_Restore_def_params {
            get {
                return ResourceManager.GetString("ServoSetting_Restore_def_params", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择操作:.
        /// </summary>
        public static string ServoSetting_Sel_op {
            get {
                return ResourceManager.GetString("ServoSetting_Sel_op", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择写入.
        /// </summary>
        public static string ServoSetting_Sel_write {
            get {
                return ResourceManager.GetString("ServoSetting_Sel_write", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 选择.
        /// </summary>
        public static string ServoSetting_Select {
            get {
                return ResourceManager.GetString("ServoSetting_Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设定类型.
        /// </summary>
        public static string ServoSetting_Set_type {
            get {
                return ResourceManager.GetString("ServoSetting_Set_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 设定值.
        /// </summary>
        public static string ServoSetting_Set_val {
            get {
                return ResourceManager.GetString("ServoSetting_Set_val", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 子模式:.
        /// </summary>
        public static string ServoSetting_Sub_mode {
            get {
                return ResourceManager.GetString("ServoSetting_Sub_mode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统软复位.
        /// </summary>
        public static string ServoSetting_System_soft_reset {
            get {
                return ResourceManager.GetString("ServoSetting_System_soft_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 全部写入.
        /// </summary>
        public static string ServoSetting_Write_all {
            get {
                return ResourceManager.GetString("ServoSetting_Write_all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 交流采样测试.
        /// </summary>
        public static string ServoSettingViewModel_Ac_sampling_test {
            get {
                return ResourceManager.GetString("ServoSettingViewModel_Ac_sampling_test", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴0电角度辨识.
        /// </summary>
        public static string ServoSettingViewModel_Axis0_electrical_angle {
            get {
                return ResourceManager.GetString("ServoSettingViewModel_Axis0_electrical_angle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 直流采样测试.
        /// </summary>
        public static string ServoSettingViewModel_Dc_sampling_test {
            get {
                return ResourceManager.GetString("ServoSettingViewModel_Dc_sampling_test", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 双轴位置控制.
        /// </summary>
        public static string ServoSettingViewModel_Dual_axis_position_control {
            get {
                return ResourceManager.GetString("ServoSettingViewModel_Dual_axis_position_control", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 无控制.
        /// </summary>
        public static string ServoSettingViewModel_No_control {
            get {
                return ResourceManager.GetString("ServoSettingViewModel_No_control", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 【SQL参数】：.
        /// </summary>
        public static string SqlsugarSetup_Sql_parameters {
            get {
                return ResourceManager.GetString("SqlsugarSetup_Sql_parameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 【SQL语句】：.
        /// </summary>
        public static string SqlsugarSetup_Sql_statement {
            get {
                return ResourceManager.GetString("SqlsugarSetup_Sql_statement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 紧急停止.
        /// </summary>
        public static string SysCtrlCmdEnum_Emergency_stop {
            get {
                return ResourceManager.GetString("SysCtrlCmdEnum_Emergency_stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 错误复位.
        /// </summary>
        public static string SysCtrlCmdEnum_Error_reset {
            get {
                return ResourceManager.GetString("SysCtrlCmdEnum_Error_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 下使能.
        /// </summary>
        public static string SysCtrlCmdEnum_Lower_enable {
            get {
                return ResourceManager.GetString("SysCtrlCmdEnum_Lower_enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 暂停.
        /// </summary>
        public static string SysCtrlCmdEnum_Pause {
            get {
                return ResourceManager.GetString("SysCtrlCmdEnum_Pause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 运行.
        /// </summary>
        public static string SysCtrlCmdEnum_Run {
            get {
                return ResourceManager.GetString("SysCtrlCmdEnum_Run", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 上使能.
        /// </summary>
        public static string SysCtrlCmdEnum_Upper_enable {
            get {
                return ResourceManager.GetString("SysCtrlCmdEnum_Upper_enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 控制对象已经从协议中移除，请勿使用此属性.
        /// </summary>
        public static string SysCtrlCmdPackage_Control_object_removed {
            get {
                return ResourceManager.GetString("SysCtrlCmdPackage_Control_object_removed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 轴配置完成，可进行轴序列初始化.
        /// </summary>
        public static string SysFeedBackMapping_Axis_config_completed {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_Axis_config_completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to bit8-31: 预留\n.
        /// </summary>
        public static string SysFeedBackMapping_Bit8_31_reserved {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_Bit8_31_reserved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 运动参数配置完成，可进行系统旧状态恢复.
        /// </summary>
        public static string SysFeedBackMapping_Motion_param_config_completed {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_Motion_param_config_completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统总线状态.
        /// </summary>
        public static string SysFeedBackMapping_System_bus_status {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_bus_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统使能状态.
        /// </summary>
        public static string SysFeedBackMapping_System_enable_status {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_enable_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统错误状态.
        /// </summary>
        public static string SysFeedBackMapping_System_error_status {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_error_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统平台校验状态.
        /// </summary>
        public static string SysFeedBackMapping_System_platform_verification {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_platform_verification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统已准备好.
        /// </summary>
        public static string SysFeedBackMapping_System_ready {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_ready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统运行状态.
        /// </summary>
        public static string SysFeedBackMapping_System_running_status {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_running_status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 系统恢复旧状态完成.
        /// </summary>
        public static string SysFeedBackMapping_System_state_restored {
            get {
                return ResourceManager.GetString("SysFeedBackMapping_System_state_restored", resourceCulture);
            }
        }
    }
}
