<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>语言</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>连接断开</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>连接成功</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>自动</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>手动</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>初始化</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>启动</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>急停</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>复位</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>使能</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>轴错误复位</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>重启系统</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>工位初始化</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>启用工位</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>屏蔽工位</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>故障</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>维护</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>运行</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>设备连接</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>驱动器</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>控制器</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>明码报文</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>固件升级</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>离线配置</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>系统总成</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>轴控制</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>接驳状态</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>工位控制</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>系统控制</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>反馈信息</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>错误故障</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>在线配置</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>倔差补偿</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>曲强往复</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>配置生成</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>数字IO</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>伺服配置</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>示波器</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>基本设置</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>角色管理</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>用户管理</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>功能列表</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>权限分配</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>数据追溯</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>操作日志</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>选择轴序号:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>驱动器连接:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>控制器连接:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>动子轴控制</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>轴运动模式：</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Jog运动</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>绝对运动</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>相对运动</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>工位运动</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>轴ID:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>轴类型：</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>动子</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>旋转电机</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>直线电机</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>速度模式：</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>轴控制模式：</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>目标线体ID:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>目标工位ID:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>速度：</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>加速度：</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>减速度：</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>加加速度：</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>定位精度：</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>防碰撞精度：</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>目标位置：</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>选择操作：</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>执行</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>读取</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>轴所处对象</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>轴所处线体</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>驱动错误</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>轴错误</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>轴当前位置（mm）</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>轴当前速度</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>轴当前状态</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>控制器连接</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>端口</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>连接</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>断开</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>发送：</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>日志：</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>清空</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>配置生成</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>系统配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>电机配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>从站节点配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>线体段配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>工位配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>动子配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>旋转轴配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>IO配置个数：</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>生成配置文件</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>在线配置</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>选择配置:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>系统配置</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>工位配置</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>写入</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>参数名称</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>设定类型</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>读取值</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>设定值</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>系统控制</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>控制对象：</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>动子</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>旋转电机</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>直线电机</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>系统运行模式：</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>轴示教</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>接驳示教</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>自动运行</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>自动运行模式：</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>同步</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>异步</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>速度百分比：</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>从站节点ID：</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>控制模式：</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>选择操作：</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>执行</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>读取</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>系统错误轴ID</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>系统错误驱动</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>系统错误代码</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>系统错误码</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>系统状态</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>接驳控制</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>接驳配置：</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>当前对象ID：</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>左侧对象ID：</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>连接状态：</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>断开连接</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>建立连接</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>右侧对象ID：</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>选择操作：</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>执行</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>读取</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>接驳ID:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>目标工位ID:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>速度：</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>加速度：</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>减速度：</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>目标位置：</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>控制命令：</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>线体ID</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>线体左侧连接对象ID</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>线体右侧连接对象ID</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>使能状态</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>运行状态</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>回零完成</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>错误码</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>实际速度</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>实际位置</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>记住密码</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>请输入关键字</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>起始时间: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>时间</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>模块</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>行为</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>操作人</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>查看</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>详细</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>详细描述:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>采集</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>十字星</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>X轴标尺</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Y轴标尺</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>导入</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>导出</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>查错</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>缩放</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>采样频率(1-300，单位ms)：</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>通道</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>选择对象</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>请选择</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>数值</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>是否可见</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>偏移量</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>放大倍数</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>颜色</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>调试</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>驱动器连接</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>串口</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>波特率</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>数据位</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>校验位</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>停止位</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>连接</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>断开</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>驱动器参数</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>选择操作:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>选择写入</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>全部写入</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>恢复默认参数</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>错误复位</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>故障记录清除</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>驱动模式设置:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>控制权:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>本地控制模式:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>子模式:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>选择</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>参数名称</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>设定类型</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>最小值</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>最大值</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>读取值</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>设定值</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>权限</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>系数</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>监控</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>角色:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>权限:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>请输入关键字</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>菜单</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>绑定代码</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>是否是按钮</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>是否隐藏</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>按钮事件</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>级别</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>启用</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>创建者</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>修改者</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>修改时间</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>菜单名称:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>父级菜单:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>请输入关键字</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>角色名</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>描述</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>级别</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>创建者</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>修改者</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>修改时间</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>是否启用</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>优先级越小，权限越大</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>是否启用当前角色:</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>请输入关键字</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>新建</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>真实姓名</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>角色</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>状态</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>备注</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>修改时间</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>最后登陆</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>操作</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>登陆名:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>密码:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>修改密码</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>待启用</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>您没有该菜单的权限</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI线程：</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>UI线程异常：</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>UI线程发生致命错误！</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>非UI线程发生致命错误</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>非UI线程异常：</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task线程：</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Task线程异常：</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>主线程</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>开关</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>您没有该操作权限</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>单轴使能状态</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>单轴运行状态</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>单轴报警状态</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>单轴错误状态</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>单轴左碰撞</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>单轴右碰撞</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>单轴正限位</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>单轴负限位</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>单轴在工位上</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>单轴已到达目标位置</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>系统已准备好</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>系统使能状态</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>系统错误状态</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>系统运行状态</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>系统总线状态</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>系统平台校验状态</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>轴配置完成，可进行轴序列初始化</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>运动参数配置完成，可进行系统旧状态恢复</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>系统恢复旧状态完成</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>bit8-31: 预留\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【SQL语句】：</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【SQL参数】：</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>输入值必须在指定范围内</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>根节点</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>获取成功</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>根节点</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>获取成功</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>添加成功</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>更新成功</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>删除成功</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>获取成功</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>添加成功</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>更新成功</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>删除成功</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Jog正向运动</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Jog反向运动</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>绝对运动</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>相对运动</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>工位运动</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>设置零点</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>轴复位</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>配置文件生成成功</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>工位配置下发成功</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>不做处理</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>控制器连接断开！</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>控制器连接成功！</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>控制器反馈轴数为0，无法进行此操作！</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>无控制</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>双轴位置控制</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>轴0电角度辨识</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>直流采样测试</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>交流采样测试</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>CSV 文件 (*.csv)|*.csv|所有文件 (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>请选择一个CSV文件</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>请选择保存路径</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>数据导出成功</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>传入对象不能为空！</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>新追加内容</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>这是我写的内容啊</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>不存在相应的目录</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>按钮</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>发送数据：</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>适配器解析数据失败！</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>控制器未连接！</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>控制器心跳发送失败</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>上使能</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>下使能</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>重置</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>设置零点</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>向前点动</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>向后点动</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>绝对运动</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>相对运动</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>工位运动</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>上使能</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>下使能</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>错误复位</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>运行</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>暂停</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>紧急停止</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>控制对象已经从协议中移除，请勿使用此属性</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>控制对象已经从协议中移除，请勿使用此属性</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>位置参数</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>轴0位置反馈</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>轴1位置反馈</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>速度参数</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>轴0速度指令</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>轴0速度反馈</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>轴1速度指令</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>轴1速度反馈</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>电流参数</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>轴0电流指令</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>轴0电流反馈</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>轴1电流指令</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>轴1电流反馈</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>电压参数</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>轴0 D轴参考电压</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>轴1 D轴参考电压</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>轴0 Q轴参考电压</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>轴1 Q轴参考电压</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>轴0母线电压</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>轴1母线电压</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>轴0_U相电流</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>轴1_U相电流</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>轴0_V相电流</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>轴1_V相电流</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>轴0_W相电流</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>轴1_W相电流</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>轴0_控制电压</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>轴1_控制电压</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1-电机参数</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2-系统参数</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3-编码器参数</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4-保护参数</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5-故障记录</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6-控制状态</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7-位置参数</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8-速度参数</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9-转矩参数</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext异常</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>伺服心跳发送失败</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>第三个指令不存在</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>角色名不能为空</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>输入值超出限制！</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>输入值错误！</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>系统</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>电机</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>从站节点</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>线体</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>工位</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>轴</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>轴序列</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>轴PID</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>轴偏移</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>设备接线方向</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>工位偏移</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI视图</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>配置参数</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>系统配置参数</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>电机配置参数</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>从站节点配置参数</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>线体段配置参数</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>工位运行配置参数</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>动子配置参数</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>轴序列配置参数</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>轴运行PID配置参数</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>动子补偿配置参数</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>线体接线方向配置参数</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>工位补偿配置参数</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>线体视图配置参数</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1-电机参数</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2-系统参数</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3-编码器参数</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4-保护参数</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5-故障记录</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6-控制状态</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7-位置参数</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8-速度参数</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9-转矩参数</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension异常</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>中文简体</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>中文繁體</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>日本語</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>未知</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>无描述</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>无值</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>远程终端已关闭</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>新的SerialPort必须在连接状态。</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>在处理数据时发生错误</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>配置文件不能为空。</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>串口配置不能为空。</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>当前适配器不支持对象发送。</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>视图配置</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>电机配置</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>从站节点</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>线体配置</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>工位运行配置</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>轴配置</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>序列配置</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>PID配置</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>轴补偿配置</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>工位补偿配置</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>一键上传到控制器</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>一键下载到本地</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>载入配置</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>配置另存</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>发送成功</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>角色名不能为空</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>在线演示</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>报警</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>反馈信息</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>清除所有通知</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>类型</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>来源</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>消息内容</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>全局数据复位</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>平台校验</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>系统参数配置初始化</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>从站信息获取</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>从站地址映射到控制地址</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>主从站状态校验</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>总线-系统等状态初始化完成</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>运动相关参数初始化</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>磁驱初始化成功</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>系统驱动错误</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>主机:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>端口:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>用户名:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>密码:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>连接</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>断开</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>远程目录: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>后退</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>前进</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>向上</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>刷新</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>创建文件夹</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>下载到本地</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>本地目录: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>上传到服务器</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>传输日志:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>系统软复位</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>正在连接到FTP服务器...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>已连接到FTP服务器</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>连接</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>已断开连接</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>断开连接</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>正在加载远程目录: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>已加载远程目录: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>加载远程目录失败: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>浏览</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>正在加载本地目录: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>已加载本地目录: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>加载本地目录失败: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>正在下载: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>下载完成: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>下载</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>下载失败: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>正在上传: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>上传完成: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>上传</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>上传失败: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>已创建目录: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>创建目录</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>创建目录失败: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>已删除目录: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>已删除文件: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>打开</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>系统正在运行</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>系统已准备好</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>系统已启用</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>系统总线已连接</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>系统处于错误状态</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>轴驱动器错误</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>轴运动错误</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>轴错误状态</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>轴报警</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>轴正限位</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>轴负限位</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>轴警告</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>轴左到位</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>轴右到位</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>轴已到达目标位置</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>轴在工位上</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>轴通知</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>轴正在运行</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>轴已使能</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>轴状态</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>旋转轴正在运行</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>旋转轴回零完成</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>旋转轴已使能</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>旋转轴</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>驱动器连接成功！</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>驱动器断开连接！</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>驱动器参数恢复成功！</value>
  </data>
</root>