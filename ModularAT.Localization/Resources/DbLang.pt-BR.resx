<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>Configuração online</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>Geração de configuração</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>Registro de operações</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>Rastreabilidade de dados</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>Coleta</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>Exclusão de registros de falhas</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>Redefinição de erro</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>Restaurar parâmetros padrão</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>Gravar tudo</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>Gravar selecionado</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>Executar</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>Executar</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>Parar</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>Executar</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>Atribuição de permissões</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>Menu</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>Usuário</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>Papel</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>Osciloscópio</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>Configuração do servo</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>Controle do sistema</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>Estado da conexão</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>Controle do eixo</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>Montagem do sistema</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>Conexão do controlador</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>Conexão do driver</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>Configurações básicas</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>Driver</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>Controlador</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>Conexão do dispositivo</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>Resistência do fio do motor (mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>Indutância do fio do motor (mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>Corrente nominal do motor (Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>Torque nominal do motor (N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>Corrente de pico do motor (Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>Constante de torque do motor (N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>Coeficiente de força eletromotriz inversa do motor (V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>Distância entre pares de pólos N - N do motor (mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>Número de pares de pólos do motor</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>Deslocamento do ângulo elétrico (PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>Corrente da fase U do motor (A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>Corrente da fase V do motor (A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>Corrente da fase W do motor (A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>Tensão da barra (V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>Versão do driver - Modelo do chip</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>Versão do driver - Iteração da versão principal</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>Versão do driver - Iteração de funcionalidade</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>Versão do driver - Iteração de correção de bugs</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>Versão do driver - Depuração/Lançamento (0 - Depuração, 1 - Lançamento)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>Controle do osciloscópio</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>Canal 0 do osciloscópio</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>Canal 1 do osciloscópio</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>Canal 2 do osciloscópio</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>Canal 3 do osciloscópio</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>Canal 4 do osciloscópio</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>Canal 5 do osciloscópio</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>Canal 6 do osciloscópio</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>Canal 7 do osciloscópio</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>Tipo de codificador</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>Resolução do codificador</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>Versão do codificador - Iteração da versão principal</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>Versão do codificador - Iteração de funcionalidade</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>Versão do codificador - Iteração de correção de bugs</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>Versão do codificador - Versão de depuração</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>Seleção de funções de depuração do codificador</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>Interface de depuração da posição do codificador 0</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>Interface de depuração da posição do codificador 1</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>Limite de detecção de sobrecorrente (A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>Tempo de julgamento de detecção de sobrecorrente (ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>Limite de corrente de julgamento de sobrecarga</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>Corrente de pico de sobrecarga</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>Tempo permitido de duração da corrente de pico (ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>Coeficiente de compensação de aumento de I2t</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>Coeficiente de compensação de diminuição de I2t</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>Limite de corrente de detecção de travamento do motor (A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>Tempo de julgamento de travamento do motor (ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>Limite de velocidade de julgamento de travamento do motor (mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>Limite de temperatura MOS para alarme (℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>Limite do número de erros de comunicação do codificador para alarme</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>Limite de detecção de perda de velocidade do motor (mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>Limite de proteção contra sobretensão (V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>Limite de proteção contra sub-tensão (V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>Última localização do erro</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>Índice de erro no início</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>Erro histórico 0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>Erro histórico 1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>Erro histórico 2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>Erro histórico 3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>Erro histórico 4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>Erro histórico 5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>Erro histórico 6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>Erro histórico 7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>Erro histórico 8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>Erro histórico 9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>Erro histórico 10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>Erro histórico 11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>Erro histórico 12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>Erro histórico 13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>Erro histórico 14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>Erro histórico 15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>Erro histórico 16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>Erro histórico 17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>Erro histórico 18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>Erro histórico 19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>Palavra de controle</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>Palavra de status</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>Estado de operação</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>Estado real</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>Posição-alvo (PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>Posição real (PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>Coeficiente de proporcionalidade do loop de posição ((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>Coeficiente de integração do loop de posição</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>Coeficiente de diferenciação do loop de posição</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa da指令 de posição (Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>Limite superior de saída de controle de posição (mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>Limite inferior de saída de controle de posição (mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>Frequência de corte do filtro de média da指令 de posição (Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>Velocidade-alvo (mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>Velocidade real (mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>Coeficiente de proporcionalidade do loop de velocidade (A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>Coeficiente de integração do loop de velocidade (A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>Coeficiente de diferenciação do loop de velocidade (A/(mm/s²))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>Coeficiente anti-saturação de integração do loop de velocidade</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>Coeficiente de ganho de feedforward de velocidade</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa de feedforward de velocidade (Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa de feedback de velocidade (Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa da指令 de velocidade (Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>Limite superior de saída de controle de velocidade (A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>Limite inferior de saída de controle de velocidade (A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Valor-alvo da corrente do eixo Q (A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>Valor-alvo da corrente do eixo D (A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Valor de feedback da corrente do eixo Q (A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>Valor de feedback da corrente do eixo D (A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>Coeficiente de proporcionalidade do loop de corrente</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>Coeficiente de integração do loop de corrente</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>Coeficiente de diferenciação do loop de corrente</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>Coeficiente de compensação de força eletromotriz inversa do eixo D</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Coeficiente de compensação de força eletromotriz inversa do eixo Q</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>Coeficiente de compensação de força eletromotriz inversa do ímã permanente</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa de feedback de corrente (Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa da指令 de corrente (Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>Coeficiente de ganho de feedforward de corrente</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>Frequência de corte do filtro passa-baixa de feedforward de corrente (Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>Frequência central do filtro notch da指令 de corrente (Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>Largura de banda do filtro notch da指令 de corrente (Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>Profundidade do filtro notch da指令 de corrente (dB)</value>
    </data>
</root>