<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Language" xml:space="preserve">
    <value>Sprache</value>
  </data>
  <data name="Main_Conn_disconnected" xml:space="preserve">
    <value>Verbindung getrennt</value>
  </data>
  <data name="Main_Conn_successful" xml:space="preserve">
    <value>Verbindung erfolgreich</value>
  </data>
  <data name="Main_Auto" xml:space="preserve">
    <value>Automatisch</value>
  </data>
  <data name="Main_Manual" xml:space="preserve">
    <value>Manuell</value>
  </data>
  <data name="Main_Init" xml:space="preserve">
    <value>Initialisieren</value>
  </data>
  <data name="Main_Start" xml:space="preserve">
    <value>Starten</value>
  </data>
  <data name="Main_Stop" xml:space="preserve">
    <value>Stoppen</value>
  </data>
  <data name="Main_Emergency_stop" xml:space="preserve">
    <value>Notstoß</value>
  </data>
  <data name="Main_Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="Main_Enable" xml:space="preserve">
    <value>Aktivieren</value>
  </data>
  <data name="Main_Axis_err_reset" xml:space="preserve">
    <value>Achserror zurücksetzen</value>
  </data>
  <data name="Main_Sys_restart" xml:space="preserve">
    <value>System neu starten</value>
  </data>
  <data name="Main_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="Main_Station_init" xml:space="preserve">
    <value>Arbeitsplatz - Initialisierung</value>
  </data>
  <data name="Main_Station_enable" xml:space="preserve">
    <value>Arbeitsplatz aktivieren</value>
  </data>
  <data name="Main_Station_mask" xml:space="preserve">
    <value>Arbeitsplatz maskieren</value>
  </data>
  <data name="Main_Fault" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="Main_Maint" xml:space="preserve">
    <value>Wartung</value>
  </data>
  <data name="Main_Running" xml:space="preserve">
    <value>Laufen</value>
  </data>
  <data name="Main_Equip_conn" xml:space="preserve">
    <value>Geräteverbindung</value>
  </data>
  <data name="Main_Driver" xml:space="preserve">
    <value>Treiber</value>
  </data>
  <data name="Main_Ctrl" xml:space="preserve">
    <value>Controller</value>
  </data>
  <data name="Main_Plaintext_msg" xml:space="preserve">
    <value>Klartext - Nachricht</value>
  </data>
  <data name="Main_Fw_upgrade" xml:space="preserve">
    <value>Firmware - Update</value>
  </data>
  <data name="Main_Offline_conf" xml:space="preserve">
    <value>Offline - Konfiguration</value>
  </data>
  <data name="Main_Sys_assembly" xml:space="preserve">
    <value>System - Zusammenbau</value>
  </data>
  <data name="Main_Axis_ctrl" xml:space="preserve">
    <value>Achskontrolle</value>
  </data>
  <data name="Main_Conn_stat" xml:space="preserve">
    <value>Verbindungsstatus</value>
  </data>
  <data name="Main_Station_ctrl" xml:space="preserve">
    <value>Arbeitsplatz - Steuerung</value>
  </data>
  <data name="Main_Sys_ctrl" xml:space="preserve">
    <value>Systemsteuerung</value>
  </data>
  <data name="Main_Feedback_info" xml:space="preserve">
    <value>Feedback - Information</value>
  </data>
  <data name="Main_Err_fault" xml:space="preserve">
    <value>Fehler und Störungen</value>
  </data>
  <data name="Main_Online_conf" xml:space="preserve">
    <value>Online - Konfiguration</value>
  </data>
  <data name="Main_Dev_comp" xml:space="preserve">
    <value>Abweichungsausgleich</value>
  </data>
  <data name="Main_Curve_recip" xml:space="preserve">
    <value>Kurve - Stärke - Hin - und - Her - Bewegung</value>
  </data>
  <data name="Main_Conf_gen" xml:space="preserve">
    <value>Konfiguration generieren</value>
  </data>
  <data name="Main_Digital_io" xml:space="preserve">
    <value>Digital - IO</value>
  </data>
  <data name="Main_Servo_conf" xml:space="preserve">
    <value>Servo - Konfiguration</value>
  </data>
  <data name="Main_Oscillo" xml:space="preserve">
    <value>Oszilloskop</value>
  </data>
  <data name="Main_Basic_sett" xml:space="preserve">
    <value>Grundeinstellungen</value>
  </data>
  <data name="Main_Role_mgmt" xml:space="preserve">
    <value>Rollenverwaltung</value>
  </data>
  <data name="Main_User_mgmt" xml:space="preserve">
    <value>Benutzerverwaltung</value>
  </data>
  <data name="Main_Func_list" xml:space="preserve">
    <value>Funktionsliste</value>
  </data>
  <data name="Main_Perm_assign" xml:space="preserve">
    <value>Berechtigungszuweisung</value>
  </data>
  <data name="Main_Data_trace" xml:space="preserve">
    <value>Datenverfolgung</value>
  </data>
  <data name="Main_Op_log" xml:space="preserve">
    <value>Operationsprotokoll</value>
  </data>
  <data name="Main_Sel_axis_sn" xml:space="preserve">
    <value>Wählen Sie die Achsennummer:</value>
  </data>
  <data name="Main_Driver_conn" xml:space="preserve">
    <value>Treiberverbindung:</value>
  </data>
  <data name="Main_Ctrl_conn" xml:space="preserve">
    <value>Controller - Verbindung:</value>
  </data>
  <data name="ControlerAxis_Mover_axis_ctrl" xml:space="preserve">
    <value>Mover - Achskontrolle</value>
  </data>
  <data name="ControlerAxis_Axis_mot_mode" xml:space="preserve">
    <value>Achsmotionsmodus:</value>
  </data>
  <data name="ControlerAxis_Jog_mot" xml:space="preserve">
    <value>Jog - Bewegung</value>
  </data>
  <data name="ControlerAxis_Abs_mot" xml:space="preserve">
    <value>Absolutbewegung</value>
  </data>
  <data name="ControlerAxis_Rel_mot" xml:space="preserve">
    <value>Relativbewegung</value>
  </data>
  <data name="ControlerAxis_Station_mot" xml:space="preserve">
    <value>Arbeitsplatz - Bewegung</value>
  </data>
  <data name="ControlerAxis_Axis_id" xml:space="preserve">
    <value>Achsen - ID:</value>
  </data>
  <data name="ControlerAxis_Axis_type" xml:space="preserve">
    <value>Achstyp:</value>
  </data>
  <data name="ControlerAxis_Mover" xml:space="preserve">
    <value>Mover</value>
  </data>
  <data name="ControlerAxis_Rotary_motor" xml:space="preserve">
    <value>Rotationsmotor</value>
  </data>
  <data name="ControlerAxis_Linear_motor" xml:space="preserve">
    <value>Linearmotor</value>
  </data>
  <data name="ControlerAxis_Speed_mode" xml:space="preserve">
    <value>Geschwindigkeitsmodus:</value>
  </data>
  <data name="ControlerAxis_Axis_ctrl_mode" xml:space="preserve">
    <value>Achskontrollmodus:</value>
  </data>
  <data name="ControlerAxis_Target_line_id" xml:space="preserve">
    <value>Ziel - Lineen - ID:</value>
  </data>
  <data name="ControlerAxis_Target_station_id" xml:space="preserve">
    <value>Ziel - Arbeitsplatz - ID:</value>
  </data>
  <data name="ControlerAxis_Speed" xml:space="preserve">
    <value>Geschwindigkeit:</value>
  </data>
  <data name="ControlerAxis_Accel" xml:space="preserve">
    <value>Beschleunigung:</value>
  </data>
  <data name="ControlerAxis_Decel" xml:space="preserve">
    <value>Verzögerung:</value>
  </data>
  <data name="ControlerAxis_Jerk" xml:space="preserve">
    <value>Jerk:</value>
  </data>
  <data name="ControlerAxis_Pos_accu" xml:space="preserve">
    <value>Positionsgenauigkeit:</value>
  </data>
  <data name="ControlerAxis_Anti_coll_accu" xml:space="preserve">
    <value>Kollisionsverhütungsgenauigkeit:</value>
  </data>
  <data name="ControlerAxis_Target_pos" xml:space="preserve">
    <value>Zielposition:</value>
  </data>
  <data name="ControlerAxis_Sel_op" xml:space="preserve">
    <value>Wählen Sie die Aktion:</value>
  </data>
  <data name="ControlerAxis_Exec" xml:space="preserve">
    <value>Ausführen</value>
  </data>
  <data name="ControlerAxis_Read" xml:space="preserve">
    <value>Lesen</value>
  </data>
  <data name="ControlerAxis_Stop" xml:space="preserve">
    <value>Stoppen</value>
  </data>
  <data name="ControlerAxis_Axis_obj" xml:space="preserve">
    <value>Objekt, zu dem die Achse gehört</value>
  </data>
  <data name="ControlerAxis_Axis_line" xml:space="preserve">
    <value>Linie, zu der die Achse gehört</value>
  </data>
  <data name="ControlerAxis_Driver_err" xml:space="preserve">
    <value>Treiberfehler</value>
  </data>
  <data name="ControlerAxis_Axis_err" xml:space="preserve">
    <value>Achserror</value>
  </data>
  <data name="ControlerAxis_Axis_curr_pos_mm" xml:space="preserve">
    <value>Aktuelle Achsposition (mm)</value>
  </data>
  <data name="ControlerAxis_Axis_curr_speed" xml:space="preserve">
    <value>Aktuelle Achsgeschwindigkeit</value>
  </data>
  <data name="ControlerAxis_Axis_curr_stat" xml:space="preserve">
    <value>Aktueller Achszustand</value>
  </data>
  <data name="ControlerClient_Ctrl_conn" xml:space="preserve">
    <value>Controller - Verbindung</value>
  </data>
  <data name="ControlerClient_Port" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="ControlerClient_Connect" xml:space="preserve">
    <value>Verbinden</value>
  </data>
  <data name="ControlerClient_Disconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="ControlerClient_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="ControlerDebug_Send" xml:space="preserve">
    <value>Senden:</value>
  </data>
  <data name="ControlerDebug_Log" xml:space="preserve">
    <value>Protokoll:</value>
  </data>
  <data name="ControlerDebug_Clear" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="ControlerGenerateConfig_Conf_gen" xml:space="preserve">
    <value>Konfiguration generieren</value>
  </data>
  <data name="ControlerGenerateConfig_Sys_conf_num" xml:space="preserve">
    <value>Anzahl der Systemkonfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Motor_conf_num" xml:space="preserve">
    <value>Anzahl der Motorkonfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Slave_node_conf_num" xml:space="preserve">
    <value>Anzahl der Slave - Knoten - Konfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Line_seg_conf_num" xml:space="preserve">
    <value>Anzahl der Liniensegment - Konfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Station_conf_num" xml:space="preserve">
    <value>Anzahl der Arbeitsplatz - Konfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Mover_conf_num" xml:space="preserve">
    <value>Anzahl der Mover - Konfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Rot_axis_conf_num" xml:space="preserve">
    <value>Anzahl der Drehachsen - Konfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Io_conf_num" xml:space="preserve">
    <value>Anzahl der IO - Konfigurationen:</value>
  </data>
  <data name="ControlerGenerateConfig_Gen_conf_file" xml:space="preserve">
    <value>Konfigurationsdatei generieren</value>
  </data>
  <data name="ControlerOnlineConfig_Online_conf" xml:space="preserve">
    <value>Online - Konfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Sel_conf" xml:space="preserve">
    <value>Wählen Sie die Konfiguration:</value>
  </data>
  <data name="ControlerOnlineConfig_Sys_conf" xml:space="preserve">
    <value>Systemkonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Station_conf" xml:space="preserve">
    <value>Arbeitsplatz - Konfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Write" xml:space="preserve">
    <value>Schreiben</value>
  </data>
  <data name="ControlerOnlineConfig_Param_name" xml:space="preserve">
    <value>Parametername</value>
  </data>
  <data name="ControlerOnlineConfig_Set_type" xml:space="preserve">
    <value>Einstellungstyp</value>
  </data>
  <data name="ControlerOnlineConfig_Read_val" xml:space="preserve">
    <value>Gelesener Wert</value>
  </data>
  <data name="ControlerOnlineConfig_Set_val" xml:space="preserve">
    <value>Einstellen Wert</value>
  </data>
  <data name="ControlerOnlineConfig_Desc" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="ControlerSys_Sys_ctrl" xml:space="preserve">
    <value>Systemsteuerung</value>
  </data>
  <data name="ControlerSys_Ctrl_obj" xml:space="preserve">
    <value>Steuerobjekt:</value>
  </data>
  <data name="ControlerSys_Mover" xml:space="preserve">
    <value>Mover</value>
  </data>
  <data name="ControlerSys_Rotary_motor" xml:space="preserve">
    <value>Rotationsmotor</value>
  </data>
  <data name="ControlerSys_Linear_motor" xml:space="preserve">
    <value>Linearmotor</value>
  </data>
  <data name="ControlerSys_Sys_op_mode" xml:space="preserve">
    <value>Systembetriebsmodus:</value>
  </data>
  <data name="ControlerSys_Axis_teach" xml:space="preserve">
    <value>Achstrainings</value>
  </data>
  <data name="ControlerSys_Conn_teach" xml:space="preserve">
    <value>Verbindungs - Training</value>
  </data>
  <data name="ControlerSys_Auto_op" xml:space="preserve">
    <value>Automatischer Betrieb</value>
  </data>
  <data name="ControlerSys_Auto_op_mode" xml:space="preserve">
    <value>Automatischer Betriebsmodus:</value>
  </data>
  <data name="ControlerSys_Sync" xml:space="preserve">
    <value>Synchron</value>
  </data>
  <data name="ControlerSys_Async" xml:space="preserve">
    <value>Asynchron</value>
  </data>
  <data name="ControlerSys_Speed_perc" xml:space="preserve">
    <value>Geschwindigkeitsprozentsatz:</value>
  </data>
  <data name="ControlerSys_Slave_node_id" xml:space="preserve">
    <value>Slave - Knoten - ID:</value>
  </data>
  <data name="ControlerSys_Ctrl_mode" xml:space="preserve">
    <value>Steuerungsmodus:</value>
  </data>
  <data name="ControlerSys_Sel_op" xml:space="preserve">
    <value>Wählen Sie die Aktion:</value>
  </data>
  <data name="ControlerSys_Exec" xml:space="preserve">
    <value>Ausführen</value>
  </data>
  <data name="ControlerSys_Read" xml:space="preserve">
    <value>Lesen</value>
  </data>
  <data name="ControlerSys_Sys_err_axis_id" xml:space="preserve">
    <value>System - Error - Achsen - ID</value>
  </data>
  <data name="ControlerSys_Sys_err_driver" xml:space="preserve">
    <value>System - Error - Treiber</value>
  </data>
  <data name="ControlerSys_Sys_err_code" xml:space="preserve">
    <value>Systemfehlercode</value>
  </data>
  <data name="ControlerSys_Sys_err_num" xml:space="preserve">
    <value>Systemfehlercode</value>
  </data>
  <data name="ControlerSys_Sys_stat" xml:space="preserve">
    <value>Systemzustand</value>
  </data>
  <data name="ControlerTranStatus_Conn_ctrl" xml:space="preserve">
    <value>Verbindungssteuerung</value>
  </data>
  <data name="ControlerTranStatus_Conn_conf" xml:space="preserve">
    <value>Verbindungs - Konfiguration:</value>
  </data>
  <data name="ControlerTranStatus_Curr_obj_id" xml:space="preserve">
    <value>Aktuelles Objekt - ID:</value>
  </data>
  <data name="ControlerTranStatus_Left_obj_id" xml:space="preserve">
    <value>Linkes Objekt - ID:</value>
  </data>
  <data name="ControlerTranStatus_Conn_stat" xml:space="preserve">
    <value>Verbindungsstatus:</value>
  </data>
  <data name="ControlerTranStatus_Disconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="ControlerTranStatus_Est_conn" xml:space="preserve">
    <value>Verbindung herstellen</value>
  </data>
  <data name="ControlerTranStatus_Right_obj_id" xml:space="preserve">
    <value>Rechtes Objekt - ID:</value>
  </data>
  <data name="ControlerTranStatus_Sel_op" xml:space="preserve">
    <value>Wählen Sie die Aktion:</value>
  </data>
  <data name="ControlerTranStatus_Exec" xml:space="preserve">
    <value>Ausführen</value>
  </data>
  <data name="ControlerTranStatus_Read" xml:space="preserve">
    <value>Lesen</value>
  </data>
  <data name="ControlerTranStatus_Conn_id" xml:space="preserve">
    <value>Verbindungs - ID:</value>
  </data>
  <data name="ControlerTranStatus_Target_station_id" xml:space="preserve">
    <value>Ziel - Arbeitsplatz - ID:</value>
  </data>
  <data name="ControlerTranStatus_Speed" xml:space="preserve">
    <value>Geschwindigkeit:</value>
  </data>
  <data name="ControlerTranStatus_Accel" xml:space="preserve">
    <value>Beschleunigung:</value>
  </data>
  <data name="ControlerTranStatus_Decel" xml:space="preserve">
    <value>Verzögerung:</value>
  </data>
  <data name="ControlerTranStatus_Target_pos" xml:space="preserve">
    <value>Zielposition:</value>
  </data>
  <data name="ControlerTranStatus_Ctrl_cmd" xml:space="preserve">
    <value>Steuerbefehl:</value>
  </data>
  <data name="ControlerTranStatus_Line_id" xml:space="preserve">
    <value>Lineen - ID</value>
  </data>
  <data name="ControlerTranStatus_Line_left_conn_obj_id" xml:space="preserve">
    <value>Linkes Verbindungsobjekt - ID der Linie</value>
  </data>
  <data name="ControlerTranStatus_Line_right_conn_obj_id" xml:space="preserve">
    <value>Rechtes Verbindungsobjekt - ID der Linie</value>
  </data>
  <data name="ControlerTranStatus_Enable_stat" xml:space="preserve">
    <value>Aktivierungsstatus</value>
  </data>
  <data name="ControlerTranStatus_Run_stat" xml:space="preserve">
    <value>Betriebsstatus</value>
  </data>
  <data name="ControlerTranStatus_Homing_done" xml:space="preserve">
    <value>Homing abgeschlossen</value>
  </data>
  <data name="ControlerTranStatus_Err_code" xml:space="preserve">
    <value>Fehlercode</value>
  </data>
  <data name="ControlerTranStatus_Act_speed" xml:space="preserve">
    <value>Tatsächliche Geschwindigkeit</value>
  </data>
  <data name="ControlerTranStatus_Act_pos" xml:space="preserve">
    <value>Tatsächliche Position</value>
  </data>
  <data name="Login_User_name" xml:space="preserve">
    <value>Benutzername</value>
  </data>
  <data name="Login_Passwd" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="Login_Rem_passwd" xml:space="preserve">
    <value>Passwort merken</value>
  </data>
  <data name="Login_Login" xml:space="preserve">
    <value>Anmelden</value>
  </data>
  <data name="OperateLog_Enter_keywords" xml:space="preserve">
    <value>Geben Sie die Schlüsselwörter ein</value>
  </data>
  <data name="OperateLog_Refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="OperateLog_Start_time" xml:space="preserve">
    <value>Startzeit: </value>
  </data>
  <data name="OperateLog_Time" xml:space="preserve">
    <value>Zeit</value>
  </data>
  <data name="OperateLog_Module" xml:space="preserve">
    <value>Modul</value>
  </data>
  <data name="OperateLog_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="OperateLog_Behav" xml:space="preserve">
    <value>Verhalten</value>
  </data>
  <data name="OperateLog_Desc" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="OperateLog_Operator" xml:space="preserve">
    <value>Betreiber</value>
  </data>
  <data name="OperateLog_View" xml:space="preserve">
    <value>Anzeigen</value>
  </data>
  <data name="OperateLog_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="OperateLog_Detailed_desc" xml:space="preserve">
    <value>Ausführliche Beschreibung:</value>
  </data>
  <data name="OperateLog_Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="Scope_Stop" xml:space="preserve">
    <value>Stoppen</value>
  </data>
  <data name="Scope_Collect" xml:space="preserve">
    <value>Sammlung</value>
  </data>
  <data name="Scope_Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="Scope_Cross_star" xml:space="preserve">
    <value>Kreuz</value>
  </data>
  <data name="Scope_X_axis_scale" xml:space="preserve">
    <value>X - Achskalierung</value>
  </data>
  <data name="Scope_Y_axis_scale" xml:space="preserve">
    <value>Y - Achskalierung</value>
  </data>
  <data name="Scope_Import" xml:space="preserve">
    <value>Importieren</value>
  </data>
  <data name="Scope_Export" xml:space="preserve">
    <value>Exportieren</value>
  </data>
  <data name="Scope_Check_err" xml:space="preserve">
    <value>Fehlerprüfung</value>
  </data>
  <data name="Scope_Zoom" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="Scope_Sample_freq_1_300_ms" xml:space="preserve">
    <value>Abtastfrequenz (1 - 300, Einheit ms):</value>
  </data>
  <data name="Scope_Channel" xml:space="preserve">
    <value>Kanal</value>
  </data>
  <data name="Scope_Sel_obj" xml:space="preserve">
    <value>Wählen Sie das Objekt</value>
  </data>
  <data name="Scope_Please_select" xml:space="preserve">
    <value>Bitte wählen</value>
  </data>
  <data name="Scope_Value" xml:space="preserve">
    <value>Wert</value>
  </data>
  <data name="Scope_Is_visible" xml:space="preserve">
    <value>Sichtbar?</value>
  </data>
  <data name="Scope_Offset" xml:space="preserve">
    <value>Offset</value>
  </data>
  <data name="Scope_Magni" xml:space="preserve">
    <value>Vergrößerungsfaktor</value>
  </data>
  <data name="Scope_Color" xml:space="preserve">
    <value>Farbe</value>
  </data>
  <data name="Scope_Debug" xml:space="preserve">
    <value>Debuggen</value>
  </data>
  <data name="ServoSerialPort_Driver_conn" xml:space="preserve">
    <value>Treiberverbindung</value>
  </data>
  <data name="ServoSerialPort_Serial_port" xml:space="preserve">
    <value>Serielle Schnittstelle</value>
  </data>
  <data name="ServoSerialPort_Baud_rate" xml:space="preserve">
    <value>Baudrate</value>
  </data>
  <data name="ServoSerialPort_Data_bits" xml:space="preserve">
    <value>Datenbits</value>
  </data>
  <data name="ServoSerialPort_Parity_bit" xml:space="preserve">
    <value>Paritätsbit</value>
  </data>
  <data name="ServoSerialPort_Stop_bits" xml:space="preserve">
    <value>Stoppbits</value>
  </data>
  <data name="ServoSerialPort_Connect" xml:space="preserve">
    <value>Verbinden</value>
  </data>
  <data name="ServoSerialPort_Disconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="ServoSetting_Driver_params" xml:space="preserve">
    <value>Treiberparameter</value>
  </data>
  <data name="ServoSetting_Sel_op" xml:space="preserve">
    <value>Wählen Sie die Aktion:</value>
  </data>
  <data name="ServoSetting_Sel_write" xml:space="preserve">
    <value>Schreiben auswählen</value>
  </data>
  <data name="ServoSetting_Write_all" xml:space="preserve">
    <value>Alles schreiben</value>
  </data>
  <data name="ServoSetting_Restore_def_params" xml:space="preserve">
    <value>Standardparameter wiederherstellen</value>
  </data>
  <data name="ServoSetting_Err_reset" xml:space="preserve">
    <value>Fehler zurücksetzen</value>
  </data>
  <data name="ServoSetting_Fault_rec_clear" xml:space="preserve">
    <value>Fehlerprotokoll löschen</value>
  </data>
  <data name="ServoSetting_Drive_mode_set" xml:space="preserve">
    <value>Antriebsmodus - Einstellung:</value>
  </data>
  <data name="ServoSetting_Ctrl_right" xml:space="preserve">
    <value>Steuerungsrecht:</value>
  </data>
  <data name="ServoSetting_Local_ctrl_mode" xml:space="preserve">
    <value>Lokaler Steuerungsmodus:</value>
  </data>
  <data name="ServoSetting_Sub_mode" xml:space="preserve">
    <value>Untermodus:</value>
  </data>
  <data name="ServoSetting_Select" xml:space="preserve">
    <value>Wählen</value>
  </data>
  <data name="ServoSetting_Param_name" xml:space="preserve">
    <value>Parametername</value>
  </data>
  <data name="ServoSetting_Set_type" xml:space="preserve">
    <value>Einstellungstyp</value>
  </data>
  <data name="ServoSetting_Min_val" xml:space="preserve">
    <value>Mindestwert</value>
  </data>
  <data name="ServoSetting_Max_val" xml:space="preserve">
    <value>Maximalwert</value>
  </data>
  <data name="ServoSetting_Read_val" xml:space="preserve">
    <value>Gelesener Wert</value>
  </data>
  <data name="ServoSetting_Set_val" xml:space="preserve">
    <value>Einstellen Wert</value>
  </data>
  <data name="ServoSetting_Perm" xml:space="preserve">
    <value>Berechtigungen</value>
  </data>
  <data name="ServoSetting_Coeff" xml:space="preserve">
    <value>Koeffizient</value>
  </data>
  <data name="ServoSetting_Monitor" xml:space="preserve">
    <value>Überwachen</value>
  </data>
  <data name="ServoSetting_Desc" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="BasePermAssign_Role" xml:space="preserve">
    <value>Rolle:</value>
  </data>
  <data name="BasePermAssign_Refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="BasePermAssign_Perm" xml:space="preserve">
    <value>Berechtigungen:</value>
  </data>
  <data name="BasePermAssign_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="BasePermission_Enter_keywords" xml:space="preserve">
    <value>Geben Sie die Schlüsselwörter ein</value>
  </data>
  <data name="BasePermission_New" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="BasePermission_Refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="BasePermission_Menu" xml:space="preserve">
    <value>Menü</value>
  </data>
  <data name="BasePermission_Bind_code" xml:space="preserve">
    <value>Bindekode</value>
  </data>
  <data name="BasePermission_Is_button" xml:space="preserve">
    <value>Ist eine Schaltfläche</value>
  </data>
  <data name="BasePermission_Is_hidden" xml:space="preserve">
    <value>Versteckt?</value>
  </data>
  <data name="BasePermission_Btn_event" xml:space="preserve">
    <value>Schaltflächenevent</value>
  </data>
  <data name="BasePermission_Desc" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="BasePermission_Level" xml:space="preserve">
    <value>Ebene</value>
  </data>
  <data name="BasePermission_Enable" xml:space="preserve">
    <value>Aktivieren</value>
  </data>
  <data name="BasePermission_Creator" xml:space="preserve">
    <value>Ersteller</value>
  </data>
  <data name="BasePermission_Create_time" xml:space="preserve">
    <value>Erstellungszeit</value>
  </data>
  <data name="BasePermission_Modifier" xml:space="preserve">
    <value>Änderer</value>
  </data>
  <data name="BasePermission_Mod_time" xml:space="preserve">
    <value>Änderungszeit</value>
  </data>
  <data name="BasePermission_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="BasePermission_Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="BasePermission_Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="BasePermission_Menu_name" xml:space="preserve">
    <value>Menüname:</value>
  </data>
  <data name="BasePermission_Parent_menu" xml:space="preserve">
    <value>Übergeordnetes Menü:</value>
  </data>
  <data name="BasePermission_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="BasePermission_Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="BaseRole_Enter_keywords" xml:space="preserve">
    <value>Geben Sie die Schlüsselwörter ein</value>
  </data>
  <data name="BaseRole_New" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="BaseRole_Refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="BaseRole_Role_name" xml:space="preserve">
    <value>Rollenname</value>
  </data>
  <data name="BaseRole_Desc" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="BaseRole_Level" xml:space="preserve">
    <value>Ebene</value>
  </data>
  <data name="BaseRole_Creator" xml:space="preserve">
    <value>Ersteller</value>
  </data>
  <data name="BaseRole_Create_time" xml:space="preserve">
    <value>Erstellungszeit</value>
  </data>
  <data name="BaseRole_Modifier" xml:space="preserve">
    <value>Änderer</value>
  </data>
  <data name="BaseRole_Mod_time" xml:space="preserve">
    <value>Änderungszeit</value>
  </data>
  <data name="BaseRole_Is_enabled" xml:space="preserve">
    <value>Aktiviert?</value>
  </data>
  <data name="BaseRole_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="BaseRole_Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="BaseRole_Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="BaseRole_Pri_smaller_perm_bigger" xml:space="preserve">
    <value>Je kleiner die Priorität, desto größer die Berechtigungen</value>
  </data>
  <data name="BaseRole_Enable_curr_role" xml:space="preserve">
    <value>Aktual Rolle aktivieren?</value>
  </data>
  <data name="BaseRole_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="BaseRole_Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="BaseUser_Enter_keywords" xml:space="preserve">
    <value>Geben Sie die Schlüsselwörter ein</value>
  </data>
  <data name="BaseUser_New" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="BaseUser_Refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="BaseUser_User_name" xml:space="preserve">
    <value>Benutzername</value>
  </data>
  <data name="BaseUser_Real_name" xml:space="preserve">
    <value>Echtname</value>
  </data>
  <data name="BaseUser_Role" xml:space="preserve">
    <value>Rolle</value>
  </data>
  <data name="BaseUser_Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="BaseUser_Remark" xml:space="preserve">
    <value>Bemerkung</value>
  </data>
  <data name="BaseUser_Create_time" xml:space="preserve">
    <value>Erstellungszeit</value>
  </data>
  <data name="BaseUser_Mod_time" xml:space="preserve">
    <value>Änderungszeit</value>
  </data>
  <data name="BaseUser_Last_login" xml:space="preserve">
    <value>Letzte Anmeldung</value>
  </data>
  <data name="BaseUser_Op" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="BaseUser_Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="BaseUser_Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="BaseUser_Login_name" xml:space="preserve">
    <value>Anmeldename:</value>
  </data>
  <data name="BaseUser_Passwd" xml:space="preserve">
    <value>Passwort:</value>
  </data>
  <data name="BaseUser_Change_passwd" xml:space="preserve">
    <value>Passwort ändern</value>
  </data>
  <data name="BaseUser_Pending_enable" xml:space="preserve">
    <value>Wartet auf Aktivierung</value>
  </data>
  <data name="BaseUser_Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="BaseUser_Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="PromptUserControl_No_menu_perm" xml:space="preserve">
    <value>Sie haben keine Berechtigung für dieses Menü</value>
  </data>
  <data name="App_xaml_Ui_thread" xml:space="preserve">
    <value>UI-Thread:</value>
  </data>
  <data name="App_xaml_Ui_thread_exception" xml:space="preserve">
    <value>UI-Thread-Ausnahme:</value>
  </data>
  <data name="App_xaml_Ui_thread_fatal_error" xml:space="preserve">
    <value>Ein fataler Fehler ist im UI-Thread aufgetreten!</value>
  </data>
  <data name="App_xaml_Non_ui_thread_fatal_error" xml:space="preserve">
    <value>Ein fataler Fehler ist in einem Nicht-UI-Thread aufgetreten</value>
  </data>
  <data name="App_xaml_Non_ui_thread_exception" xml:space="preserve">
    <value>Nicht-UI-Thread-Ausnahme:</value>
  </data>
  <data name="App_xaml_Task_thread" xml:space="preserve">
    <value>Task-Thread:</value>
  </data>
  <data name="App_xaml_Task_thread_exception" xml:space="preserve">
    <value>Task-Thread-Ausnahme:</value>
  </data>
  <data name="DesignerHelper_Main_thread" xml:space="preserve">
    <value>Hauptthread</value>
  </data>
  <data name="ImageAttached_Switch" xml:space="preserve">
    <value>Schalter</value>
  </data>
  <data name="PermissionHelper_No_permission_operation" xml:space="preserve">
    <value>Sie haben keine Berechtigung für diese Aktion.</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_enable_status" xml:space="preserve">
    <value>Einzelachse Aktivierungszustand</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_running_status" xml:space="preserve">
    <value>Einzelachse Laufzustand</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_alarm_status" xml:space="preserve">
    <value>Einzelachse Alarmzustand</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_error_status" xml:space="preserve">
    <value>Einzelachse Fehlerzustand</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_left_collision" xml:space="preserve">
    <value>Einzelachse Linkskollision</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_right_collision" xml:space="preserve">
    <value>Einzelachse Rechtskollision</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_positive_limit" xml:space="preserve">
    <value>Einzelachse Positiv-Endschalter</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_negative_limit" xml:space="preserve">
    <value>Einzelachse Negativ-Endschalter</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_on_workstation" xml:space="preserve">
    <value>Einzelachse an der Arbeitsstation</value>
  </data>
  <data name="AxisFeedBackMapping_Single_axis_reached_target" xml:space="preserve">
    <value>Einzelachse hat das Ziel erreicht</value>
  </data>
  <data name="SysFeedBackMapping_System_ready" xml:space="preserve">
    <value>Das System ist bereit.</value>
  </data>
  <data name="SysFeedBackMapping_System_enable_status" xml:space="preserve">
    <value>System Aktivierungszustand</value>
  </data>
  <data name="SysFeedBackMapping_System_error_status" xml:space="preserve">
    <value>System Fehlerzustand</value>
  </data>
  <data name="SysFeedBackMapping_System_running_status" xml:space="preserve">
    <value>System Laufzustand</value>
  </data>
  <data name="SysFeedBackMapping_System_bus_status" xml:space="preserve">
    <value>System Buszustand</value>
  </data>
  <data name="SysFeedBackMapping_System_platform_verification" xml:space="preserve">
    <value>System Plattformprüfzustand</value>
  </data>
  <data name="SysFeedBackMapping_Axis_config_completed" xml:space="preserve">
    <value>Die Achsenkonfiguration ist abgeschlossen. Die Achsensequenz kann initialisiert werden.</value>
  </data>
  <data name="SysFeedBackMapping_Motion_param_config_completed" xml:space="preserve">
    <value>Die Bewegungsparameterkonfiguration ist abgeschlossen. Der alte Systemzustand kann wiederhergestellt werden.</value>
  </data>
  <data name="SysFeedBackMapping_System_state_restored" xml:space="preserve">
    <value>Die Wiederherstellung des alten Systemzustands ist abgeschlossen.</value>
  </data>
  <data name="SysFeedBackMapping_Bit8_31_reserved" xml:space="preserve">
    <value>Bit 8 - 31: Reserviert\n</value>
  </data>
  <data name="SqlsugarSetup_Sql_statement" xml:space="preserve">
    <value>【SQL-Anweisung】:</value>
  </data>
  <data name="SqlsugarSetup_Sql_parameters" xml:space="preserve">
    <value>【SQL-Parameter】:</value>
  </data>
  <data name="InputConverter_Input_value_range" xml:space="preserve">
    <value>Der Eingabewert muss im angegebenen Bereich liegen.</value>
  </data>
  <data name="BasePermAssignViewModel_Root_node" xml:space="preserve">
    <value>Stammknoten</value>
  </data>
  <data name="BasePermAssignViewModel_Get_success" xml:space="preserve">
    <value>Abruf erfolgreich</value>
  </data>
  <data name="BasePermissionViewModel_Root_node" xml:space="preserve">
    <value>Stammknoten</value>
  </data>
  <data name="BasePermissionViewModel_Get_success" xml:space="preserve">
    <value>Abruf erfolgreich</value>
  </data>
  <data name="BasePermissionViewModel_Add_success" xml:space="preserve">
    <value>Hinzufügen erfolgreich</value>
  </data>
  <data name="BasePermissionViewModel_Update_success" xml:space="preserve">
    <value>Aktualisierung erfolgreich</value>
  </data>
  <data name="BasePermissionViewModel_Delete_success" xml:space="preserve">
    <value>Löschung erfolgreich</value>
  </data>
  <data name="BaseUserViewModel_Get_success" xml:space="preserve">
    <value>Abruf erfolgreich</value>
  </data>
  <data name="BaseUserViewModel_Add_success" xml:space="preserve">
    <value>Hinzufügen erfolgreich</value>
  </data>
  <data name="BaseUserViewModel_Update_success" xml:space="preserve">
    <value>Aktualisierung erfolgreich</value>
  </data>
  <data name="BaseUserViewModel_Delete_success" xml:space="preserve">
    <value>Löschung erfolgreich</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_forward" xml:space="preserve">
    <value>Jog Vorwärtsbewegung</value>
  </data>
  <data name="ControlerAxisViewModel_Jog_reverse" xml:space="preserve">
    <value>Jog Rückwärtsbewegung</value>
  </data>
  <data name="ControlerAxisViewModel_Absolute_movement" xml:space="preserve">
    <value>Absolute Bewegung</value>
  </data>
  <data name="ControlerAxisViewModel_Relative_movement" xml:space="preserve">
    <value>Relative Bewegung</value>
  </data>
  <data name="ControlerAxisViewModel_Workstation_movement" xml:space="preserve">
    <value>Bewegung zur Arbeitsstation</value>
  </data>
  <data name="ControlerAxisViewModel_Set_zero_point" xml:space="preserve">
    <value>Nullpunkt setzen</value>
  </data>
  <data name="ControlerAxisViewModel_Axis_reset" xml:space="preserve">
    <value>Achse zurücksetzen</value>
  </data>
  <data name="ControlerGenerateConfigViewModel_Config_file_generated" xml:space="preserve">
    <value>Die Erstellung der Konfigurationsdatei war erfolgreich.</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Workstation_config_distributed" xml:space="preserve">
    <value>Die Übertragung der Arbeitsstationskonfiguration war erfolgreich.</value>
  </data>
  <data name="ControlerTranStatusViewModel_Do_nothing" xml:space="preserve">
    <value>Keine Verarbeitung</value>
  </data>
  <data name="DataViewModel_Controller_disconnected" xml:space="preserve">
    <value>Die Verbindung zum Controller ist getrennt!</value>
  </data>
  <data name="DataViewModel_Controller_connected" xml:space="preserve">
    <value>Die Verbindung zum Controller war erfolgreich!</value>
  </data>
  <data name="MainViewModel_Controller_feedback_zero" xml:space="preserve">
    <value>Der Controller meldet 0 Achsen. Diese Aktion kann nicht ausgeführt werden!</value>
  </data>
  <data name="ServoSettingViewModel_No_control" xml:space="preserve">
    <value>Keine Steuerung</value>
  </data>
  <data name="ServoSettingViewModel_Dual_axis_position_control" xml:space="preserve">
    <value>Zwei-Achsen-Positionssteuerung</value>
  </data>
  <data name="ServoSettingViewModel_Axis0_electrical_angle" xml:space="preserve">
    <value>Elektrische Winkelidentifikation für Achse 0</value>
  </data>
  <data name="ServoSettingViewModel_Dc_sampling_test" xml:space="preserve">
    <value>Gleichstrom-Sampling-Test</value>
  </data>
  <data name="ServoSettingViewModel_Ac_sampling_test" xml:space="preserve">
    <value>Wechselstrom-Sampling-Test</value>
  </data>
  <data name="ScopeView_xaml_Csv_file_filter" xml:space="preserve">
    <value>CSV-Dateien (*.csv)|*.csv|Alle Dateien (*.*)|*.*</value>
  </data>
  <data name="ScopeView_xaml_Select_csv_file" xml:space="preserve">
    <value>Bitte wählen Sie eine CSV-Datei aus.</value>
  </data>
  <data name="ScopeView_xaml_Select_save_path" xml:space="preserve">
    <value>Bitte wählen Sie einen Speicherort aus.</value>
  </data>
  <data name="ScopeView_xaml_Data_export_success" xml:space="preserve">
    <value>Der Datenexport war erfolgreich.</value>
  </data>
  <data name="ObjectUtil_Object_not_empty" xml:space="preserve">
    <value>Das übergebene Objekt darf nicht null sein!</value>
  </data>
  <data name="FileHelper_Newly_appended_content" xml:space="preserve">
    <value>Neu hinzugefügter Inhalt</value>
  </data>
  <data name="FileHelper_What_i_wrote" xml:space="preserve">
    <value>Das ist der Inhalt, den ich geschrieben habe.</value>
  </data>
  <data name="FileHelper_Directory_not_exist" xml:space="preserve">
    <value>Das entsprechende Verzeichnis existiert nicht.</value>
  </data>
  <data name="RecursionHelper_Button" xml:space="preserve">
    <value>Schaltfläche</value>
  </data>
  <data name="ControlerTcpClient_Send_data" xml:space="preserve">
    <value>Daten senden:</value>
  </data>
  <data name="ControlerTcpClient_Adapter_parsing_failed" xml:space="preserve">
    <value>Die Adapter-Data-Parsein fehlgeschlagen!</value>
  </data>
  <data name="ControlerTcpClient_Controller_not_connected" xml:space="preserve">
    <value>Der Controller ist nicht verbunden!</value>
  </data>
  <data name="ControlerTcpClient_Controller_heartbeat_failed" xml:space="preserve">
    <value>Das Senden des Controller-Herzschlags ist fehlgeschlagen.</value>
  </data>
  <data name="ControllerConst_Upper_enable" xml:space="preserve">
    <value>Obere Aktivierung</value>
  </data>
  <data name="ControllerConst_Lower_enable" xml:space="preserve">
    <value>Untere Aktivierung</value>
  </data>
  <data name="ControllerConst_Stop" xml:space="preserve">
    <value>Stopp</value>
  </data>
  <data name="ControllerConst_Reset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="ControllerConst_Set_zero_point" xml:space="preserve">
    <value>Nullpunkt setzen</value>
  </data>
  <data name="ControllerConst_Forward_jog" xml:space="preserve">
    <value>Vorwärts-Jog</value>
  </data>
  <data name="ControllerConst_Backward_jog" xml:space="preserve">
    <value>Rückwärts-Jog</value>
  </data>
  <data name="ControllerConst_Absolute_movement" xml:space="preserve">
    <value>Absolute Bewegung</value>
  </data>
  <data name="ControllerConst_Relative_movement" xml:space="preserve">
    <value>Relative Bewegung</value>
  </data>
  <data name="ControllerConst_Workstation_movement" xml:space="preserve">
    <value>Bewegung zur Arbeitsstation</value>
  </data>
  <data name="SysCtrlCmdEnum_Upper_enable" xml:space="preserve">
    <value>Obere Aktivierung</value>
  </data>
  <data name="SysCtrlCmdEnum_Lower_enable" xml:space="preserve">
    <value>Untere Aktivierung</value>
  </data>
  <data name="SysCtrlCmdEnum_Error_reset" xml:space="preserve">
    <value>Fehler zurücksetzen</value>
  </data>
  <data name="SysCtrlCmdEnum_Run" xml:space="preserve">
    <value>Lauf</value>
  </data>
  <data name="SysCtrlCmdEnum_Pause" xml:space="preserve">
    <value>Pause</value>
  </data>
  <data name="SysCtrlCmdEnum_Emergency_stop" xml:space="preserve">
    <value>Notaus</value>
  </data>
  <data name="AxisCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>Das Steuerobjekt wurde aus dem Protokoll entfernt. Bitte verwenden Sie diese Eigenschaft nicht.</value>
  </data>
  <data name="SysCtrlCmdPackage_Control_object_removed" xml:space="preserve">
    <value>Das Steuerobjekt wurde aus dem Protokoll entfernt. Bitte verwenden Sie diese Eigenschaft nicht.</value>
  </data>
  <data name="ScopeConst_Position_parameter" xml:space="preserve">
    <value>Positionsparameter</value>
  </data>
  <data name="ScopeConst_Axis0_position_feedback" xml:space="preserve">
    <value>Positionsrückmeldung für Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_position_feedback" xml:space="preserve">
    <value>Positionsrückmeldung für Achse 1</value>
  </data>
  <data name="ScopeConst_Speed_parameter" xml:space="preserve">
    <value>Geschwindigkeitsparameter</value>
  </data>
  <data name="ScopeConst_Axis0_speed_instruction" xml:space="preserve">
    <value>Geschwindigkeitsbefehl für Achse 0</value>
  </data>
  <data name="ScopeConst_Axis0_speed_feedback" xml:space="preserve">
    <value>Geschwindigkeitsrückmeldung für Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_speed_instruction" xml:space="preserve">
    <value>Geschwindigkeitsbefehl für Achse 1</value>
  </data>
  <data name="ScopeConst_Axis1_speed_feedback" xml:space="preserve">
    <value>Geschwindigkeitsrückmeldung für Achse 1</value>
  </data>
  <data name="ScopeConst_Current_parameter" xml:space="preserve">
    <value>Stromparameter</value>
  </data>
  <data name="ScopeConst_Axis0_current_instruction" xml:space="preserve">
    <value>Strombefehl für Achse 0</value>
  </data>
  <data name="ScopeConst_Axis0_current_feedback" xml:space="preserve">
    <value>Stromrückmeldung für Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_current_instruction" xml:space="preserve">
    <value>Strombefehl für Achse 1</value>
  </data>
  <data name="ScopeConst_Axis1_current_feedback" xml:space="preserve">
    <value>Stromrückmeldung für Achse 1</value>
  </data>
  <data name="ScopeConst_Voltage_parameter" xml:space="preserve">
    <value>Spannungsparameter</value>
  </data>
  <data name="ScopeConst_Axis0_d_axis_voltage" xml:space="preserve">
    <value>Referenzspannung für die D-Achse von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_d_axis_voltage" xml:space="preserve">
    <value>Referenzspannung für die D-Achse von Achse 1</value>
  </data>
  <data name="ScopeConst_Axis0_q_axis_voltage" xml:space="preserve">
    <value>Referenzspannung für die Q-Achse von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_q_axis_voltage" xml:space="preserve">
    <value>Referenzspannung für die Q-Achse von Achse 1</value>
  </data>
  <data name="ScopeConst_Axis0_bus_voltage" xml:space="preserve">
    <value>Bussspannung von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_bus_voltage" xml:space="preserve">
    <value>Bussspannung von Achse 1</value>
  </data>
  <data name="ScopeConst_Axis0_u_phase_current" xml:space="preserve">
    <value>Strom in der U-Phase von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_u_phase_current" xml:space="preserve">
    <value>Strom in der U-Phase von Achse 1</value>
  </data>
  <data name="ScopeConst_Axis0_v_phase_current" xml:space="preserve">
    <value>Strom in der V-Phase von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_v_phase_current" xml:space="preserve">
    <value>Strom in der V-Phase von Achse 1</value>
  </data>
  <data name="ScopeConst_Axis0_w_phase_current" xml:space="preserve">
    <value>Strom in der W-Phase von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_w_phase_current" xml:space="preserve">
    <value>Strom in der W-Phase von Achse 1</value>
  </data>
  <data name="ScopeConst_Axis0_control_voltage" xml:space="preserve">
    <value>Steuerspannung von Achse 0</value>
  </data>
  <data name="ScopeConst_Axis1_control_voltage" xml:space="preserve">
    <value>Steuerspannung von Achse 1</value>
  </data>
  <data name="ServoContext_Motor_parameter" xml:space="preserve">
    <value>1 - Motorparameter</value>
  </data>
  <data name="ServoContext_System_parameter" xml:space="preserve">
    <value>2 - Systemparameter</value>
  </data>
  <data name="ServoContext_Encoder_parameter" xml:space="preserve">
    <value>3 - Encoderparameter</value>
  </data>
  <data name="ServoContext_Protection_parameter" xml:space="preserve">
    <value>4 - Schutzparameter</value>
  </data>
  <data name="ServoContext_Fault_record" xml:space="preserve">
    <value>5 - Fehlereinträge</value>
  </data>
  <data name="ServoContext_Control_status" xml:space="preserve">
    <value>6 - Steuerungszustand</value>
  </data>
  <data name="ServoContext_Position_parameter" xml:space="preserve">
    <value>7 - Positionsparameter</value>
  </data>
  <data name="ServoContext_Speed_parameter" xml:space="preserve">
    <value>8 - Geschwindigkeitsparameter</value>
  </data>
  <data name="ServoContext_Torque_parameter" xml:space="preserve">
    <value>9 - Drehmomentparameter</value>
  </data>
  <data name="ServoContext_Get_from_drive_context_exception" xml:space="preserve">
    <value>GetFromDriveContext-Ausnahme</value>
  </data>
  <data name="ServoSerialPortClient_Servo_heartbeat_failed" xml:space="preserve">
    <value>Das Senden des Servo-Herzschlags ist fehlgeschlagen.</value>
  </data>
  <data name="ElectricParaPackage_Third_instruction_not_exist" xml:space="preserve">
    <value>Der dritte Befehl existiert nicht.</value>
  </data>
  <data name="RoleDto_Role_name_not_empty" xml:space="preserve">
    <value>Der Rollenname darf nicht leer sein.</value>
  </data>
  <data name="ParameterModel_Input_value_exceed_limit" xml:space="preserve">
    <value>Der Eingabewert überschreitet die Grenze!</value>
  </data>
  <data name="ParameterModel_Input_value_incorrect" xml:space="preserve">
    <value>Falscher Eingabewert!</value>
  </data>
  <data name="LineConfigEnum_System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="LineConfigEnum_Motor" xml:space="preserve">
    <value>Motor</value>
  </data>
  <data name="LineConfigEnum_Slave_node" xml:space="preserve">
    <value>Slave-Knoten</value>
  </data>
  <data name="LineConfigEnum_Line" xml:space="preserve">
    <value>Produktionslinie</value>
  </data>
  <data name="LineConfigEnum_Workstation" xml:space="preserve">
    <value>Arbeitsstation</value>
  </data>
  <data name="LineConfigEnum_Axis" xml:space="preserve">
    <value>Achse</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence" xml:space="preserve">
    <value>Achsensequenz</value>
  </data>
  <data name="LineConfigEnum_Axis_pid" xml:space="preserve">
    <value>Achse PID</value>
  </data>
  <data name="LineConfigEnum_Axis_offset" xml:space="preserve">
    <value>Achse Offset</value>
  </data>
  <data name="LineConfigEnum_Device_wiring_direction" xml:space="preserve">
    <value>Geräteanschlussrichtung</value>
  </data>
  <data name="LineConfigEnum_Workstation_offset" xml:space="preserve">
    <value>Arbeitsstation Offset</value>
  </data>
  <data name="LineConfigEnum_Ui_view" xml:space="preserve">
    <value>UI-Ansicht</value>
  </data>
  <data name="LineConfigEnum_Configuration_parameter" xml:space="preserve">
    <value>Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_System_configuration_parameter" xml:space="preserve">
    <value>Systemkonfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Motor_configuration_parameter" xml:space="preserve">
    <value>Motorkonfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Slave_node_configuration_parameter" xml:space="preserve">
    <value>Slave-Knoten-Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Line_segment_configuration_parameter" xml:space="preserve">
    <value>Produktionslinienabschnitt-Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Workstation_running_configuration_parameter" xml:space="preserve">
    <value>Arbeitsstationslaufkonfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Rotor_configuration_parameter" xml:space="preserve">
    <value>Röhre-Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Axis_sequence_configuration_parameter" xml:space="preserve">
    <value>Achsensequenz-Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Axis_running_pid_configuration_parameter" xml:space="preserve">
    <value>Achsenlauf-PID-Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Rotor_compensation_configuration_parameter" xml:space="preserve">
    <value>Röhre-Kompensationskonfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Line_wiring_direction_configuration_parameter" xml:space="preserve">
    <value>Produktionslinienanschlussrichtung-Konfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Workstation_compensation_configuration_parameter" xml:space="preserve">
    <value>Arbeitsstationskompensationskonfigurationsparameter</value>
  </data>
  <data name="LineConfigEnum_Line_view_configuration_parameter" xml:space="preserve">
    <value>Produktionslinienansicht-Konfigurationsparameter</value>
  </data>
  <data name="ParamTableEnum_Motor_parameter" xml:space="preserve">
    <value>1 - Motorparameter</value>
  </data>
  <data name="ParamTableEnum_System_parameter" xml:space="preserve">
    <value>2 - Systemparameter</value>
  </data>
  <data name="ParamTableEnum_Encoder_parameter" xml:space="preserve">
    <value>3 - Encoderparameter</value>
  </data>
  <data name="ParamTableEnum_Protection_parameter" xml:space="preserve">
    <value>4 - Schutzparameter</value>
  </data>
  <data name="ParamTableEnum_Fault_record" xml:space="preserve">
    <value>5 - Fehlereinträge</value>
  </data>
  <data name="ParamTableEnum_Control_status" xml:space="preserve">
    <value>6 - Steuerungszustand</value>
  </data>
  <data name="ParamTableEnum_Position_parameter" xml:space="preserve">
    <value>7 - Positionsparameter</value>
  </data>
  <data name="ParamTableEnum_Speed_parameter" xml:space="preserve">
    <value>8 - Geschwindigkeitsparameter</value>
  </data>
  <data name="ParamTableEnum_Torque_parameter" xml:space="preserve">
    <value>9 - Drehmomentparameter</value>
  </data>
  <data name="ParameterModelExtension_Parameter_model_extension_exception" xml:space="preserve">
    <value>ParameterModelExtension-Ausnahme</value>
  </data>
  <data name="LocalizationManager_Simplified_chinese" xml:space="preserve">
    <value>Vereinfachtes Chinesisch</value>
  </data>
  <data name="LocalizationManager_Traditional_chinese" xml:space="preserve">
    <value>Traditionelles Chinesisch</value>
  </data>
  <data name="LocalizationManager_Japanese" xml:space="preserve">
    <value>Japanisch</value>
  </data>
  <data name="OnlineConfigService_Unknown" xml:space="preserve">
    <value>Unbekannt</value>
  </data>
  <data name="OnlineConfigService_No_description" xml:space="preserve">
    <value>Keine Beschreibung</value>
  </data>
  <data name="OnlineConfigService_No_value" xml:space="preserve">
    <value>Kein Wert</value>
  </data>
  <data name="SerialCore_Remote_terminal_closed" xml:space="preserve">
    <value>Der Fernterminal ist geschlossen.</value>
  </data>
  <data name="SerialCore_New_serial_port_connected" xml:space="preserve">
    <value>Ein neues SerialPort muss im Verbunden-Zustand sein.</value>
  </data>
  <data name="SerialPortClient_Data_processing_error" xml:space="preserve">
    <value>Beim Verarbeiten der Daten ist ein Fehler aufgetreten.</value>
  </data>
  <data name="SerialPortClient_Config_file_not_empty" xml:space="preserve">
    <value>Die Konfigurationsdatei darf nicht leer sein.</value>
  </data>
  <data name="SerialPortClient_Serial_port_config_not_empty" xml:space="preserve">
    <value>Die Serienportkonfiguration darf nicht leer sein.</value>
  </data>
  <data name="SerialPortClient_Adapter_not_support_send" xml:space="preserve">
    <value>Der aktuelle Adapter unterstützt keine Objektsendung.</value>
  </data>
  <data name="ControlerOnlineConfig_View_configuration" xml:space="preserve">
    <value>Ansichtskonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Motor_configuration" xml:space="preserve">
    <value>Motorkonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Slave_node" xml:space="preserve">
    <value>Slavenknoten</value>
  </data>
  <data name="ControlerOnlineConfig_Line_body_configuration" xml:space="preserve">
    <value>Linienkonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_operation_configuration" xml:space="preserve">
    <value>Arbeitsplatzlaufkonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_configuration" xml:space="preserve">
    <value>Achsenkonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Sequence_configuration" xml:space="preserve">
    <value>Sequenzkonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Pid_configuration" xml:space="preserve">
    <value>PID-Konfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Axis_compensation_configuration" xml:space="preserve">
    <value>Achsenkompensationskonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Workstation_compensation_configuration" xml:space="preserve">
    <value>Arbeitsplatzkompensationskonfiguration</value>
  </data>
  <data name="ControlerOnlineConfig_Upload_to_controller_with_one_click" xml:space="preserve">
    <value>Einfach mit einem Klick auf den Controller hochladen</value>
  </data>
  <data name="ControlerOnlineConfig_Download_to_local_with_one_click" xml:space="preserve">
    <value>Einfach mit einem Klick auf den lokalen Computer herunterladen</value>
  </data>
  <data name="ControlerOnlineConfig_Load_configuration" xml:space="preserve">
    <value>Konfiguration laden</value>
  </data>
  <data name="ControlerOnlineConfig_Save_configuration_as" xml:space="preserve">
    <value>Konfiguration unter einem anderen Namen speichern</value>
  </data>
  <data name="ControlerOnlineConfigViewModel_Send_success" xml:space="preserve">
    <value>Erfolgreich gesendet</value>
  </data>
  <data name="RoleDto_Role_name_cannot_be_empty" xml:space="preserve">
    <value>Der Rollenname darf nicht leer sein</value>
  </data>
  <data name="Main_Online_demonstration" xml:space="preserve">
    <value>Online-Demonstration</value>
  </data>
  <data name="Main_Alarm" xml:space="preserve">
    <value>Alarm</value>
  </data>
  <data name="NoticeListControl_Feedback_information" xml:space="preserve">
    <value>Rückmeldung</value>
  </data>
  <data name="NoticeListControl_Clear_all_notifications" xml:space="preserve">
    <value>Alle Benachrichtigungen löschen</value>
  </data>
  <data name="NoticeListControl_Type" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="NoticeListControl_Source" xml:space="preserve">
    <value>Quelle</value>
  </data>
  <data name="NoticeListControl_Message_content" xml:space="preserve">
    <value>Nachricht Inhalt</value>
  </data>
  <data name="ControlerClient_Global_data_reset" xml:space="preserve">
    <value>Globale Daten zurücksetzen</value>
  </data>
  <data name="ControlerClient_Platform_verification" xml:space="preserve">
    <value>Plattform überprüfen</value>
  </data>
  <data name="ControlerClient_System_parameter_configuration_initialization" xml:space="preserve">
    <value>Initialisierung der Systemparameterkonfiguration</value>
  </data>
  <data name="ControlerClient_Slave_station_information_acquisition" xml:space="preserve">
    <value>Slave-Station-Informationsabrufen</value>
  </data>
  <data name="ControlerClient_Mapping_of_slave_station_address_to_control_address" xml:space="preserve">
    <value>Slave-Stationsadresse auf Steueradresse abbilden</value>
  </data>
  <data name="ControlerClient_Master_slave_station_status_verification" xml:space="preserve">
    <value>Prüfung des Master-Slave-Stationsstatus</value>
  </data>
  <data name="ControlerClient_Completion_of_status_initialization_of_bus_system_etc" xml:space="preserve">
    <value>Initialisierung des Bus-Systems usw. abgeschlossen</value>
  </data>
  <data name="ControlerClient_Initialization_of_movement_related_parameters" xml:space="preserve">
    <value>Initialisierung der bewegungsbezogenen Parameter</value>
  </data>
  <data name="ControlerClient_Successful_initialization_of_magnetic_drive" xml:space="preserve">
    <value>Erfolgreiche Initialisierung des Magnetantriebs</value>
  </data>
  <data name="ControlerSys_System_drive_error" xml:space="preserve">
    <value>Systemtreiberfehler</value>
  </data>
  <data name="FtpClient_Host" xml:space="preserve">
    <value>Host:</value>
  </data>
  <data name="FtpClient_Port" xml:space="preserve">
    <value>Port:</value>
  </data>
  <data name="FtpClient_Username" xml:space="preserve">
    <value>Benutzername:</value>
  </data>
  <data name="FtpClient_Password" xml:space="preserve">
    <value>Passwort:</value>
  </data>
  <data name="FtpClient_Connect" xml:space="preserve">
    <value>Verbinden</value>
  </data>
  <data name="FtpClient_Disconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="FtpClient_Remote_directory" xml:space="preserve">
    <value>Remoteverzeichnis: </value>
  </data>
  <data name="FtpClient_Back" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="FtpClient_Forward" xml:space="preserve">
    <value>Vorwärts</value>
  </data>
  <data name="FtpClient_Up" xml:space="preserve">
    <value>Nach oben</value>
  </data>
  <data name="FtpClient_Refresh" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="FtpClient_Create_folder" xml:space="preserve">
    <value>Ordner erstellen</value>
  </data>
  <data name="FtpClient_Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="FtpClient_Download_to_local" xml:space="preserve">
    <value>Lokal herunterladen</value>
  </data>
  <data name="FtpClient_Local_directory" xml:space="preserve">
    <value>Lokales Verzeichnis: </value>
  </data>
  <data name="FtpClient_Upload_to_server" xml:space="preserve">
    <value>Auf Server hochladen</value>
  </data>
  <data name="FtpClient_Transmission_log" xml:space="preserve">
    <value>Übertragungslog:</value>
  </data>
  <data name="ServoSetting_System_soft_reset" xml:space="preserve">
    <value>System Soft Reset</value>
  </data>
  <data name="FtpClientViewModel_Connecting_to_ftp_server" xml:space="preserve">
    <value>Verbinde mit FTP-Server...</value>
  </data>
  <data name="FtpClientViewModel_Connected_to_ftp_server" xml:space="preserve">
    <value>Verbunden mit FTP-Server</value>
  </data>
  <data name="FtpClientViewModel_Connect" xml:space="preserve">
    <value>Verbinden</value>
  </data>
  <data name="FtpClientViewModel_Disconnected" xml:space="preserve">
    <value>Verbindung getrennt</value>
  </data>
  <data name="FtpClientViewModel_Disconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="FtpClientViewModel_Loading_remote_directory" xml:space="preserve">
    <value>Lade Remoteverzeichnis: </value>
  </data>
  <data name="FtpClientViewModel_Remote_directory_loaded" xml:space="preserve">
    <value>Remoteverzeichnis geladen: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_remote_directory" xml:space="preserve">
    <value>Fehler beim Laden des Remoteverzeichnisses: </value>
  </data>
  <data name="FtpClientViewModel_Browse" xml:space="preserve">
    <value>Durchsuchen</value>
  </data>
  <data name="FtpClientViewModel_Loading_local_directory" xml:space="preserve">
    <value>Lade lokales Verzeichnis: </value>
  </data>
  <data name="FtpClientViewModel_Local_directory_loaded" xml:space="preserve">
    <value>Lokales Verzeichnis geladen: </value>
  </data>
  <data name="FtpClientViewModel_Failed_to_load_local_directory" xml:space="preserve">
    <value>Fehler beim Laden des lokalen Verzeichnisses: </value>
  </data>
  <data name="FtpClientViewModel_Downloading" xml:space="preserve">
    <value>Lade herunter: </value>
  </data>
  <data name="FtpClientViewModel_Download_completed" xml:space="preserve">
    <value>Herunterladen abgeschlossen: </value>
  </data>
  <data name="FtpClientViewModel_Download" xml:space="preserve">
    <value>Herunterladen</value>
  </data>
  <data name="FtpClientViewModel_Download_failed" xml:space="preserve">
    <value>Fehler beim Herunterladen: </value>
  </data>
  <data name="FtpClientViewModel_Uploading" xml:space="preserve">
    <value>Lade hoch: </value>
  </data>
  <data name="FtpClientViewModel_Upload_completed" xml:space="preserve">
    <value>Hochladen abgeschlossen: </value>
  </data>
  <data name="FtpClientViewModel_Upload" xml:space="preserve">
    <value>Hochladen</value>
  </data>
  <data name="FtpClientViewModel_Upload_failed" xml:space="preserve">
    <value>Fehler beim Hochladen: </value>
  </data>
  <data name="FtpClientViewModel_Directory_created" xml:space="preserve">
    <value>Verzeichnis erstellt: </value>
  </data>
  <data name="FtpClientViewModel_Create_directory" xml:space="preserve">
    <value>Verzeichnis erstellen</value>
  </data>
  <data name="FtpClientViewModel_Failed_to_create_directory" xml:space="preserve">
    <value>Fehler beim Erstellen des Verzeichnisses: </value>
  </data>
  <data name="FtpClientViewModel_Directory_deleted" xml:space="preserve">
    <value>Verzeichnis gelöscht: </value>
  </data>
  <data name="FtpClientViewModel_Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="FtpClientViewModel_File_deleted" xml:space="preserve">
    <value>Datei gelöscht: </value>
  </data>
  <data name="FtpClientViewModel_Open" xml:space="preserve">
    <value>Öffnen</value>
  </data>
  <data name="ControllerHelper_System_is_running" xml:space="preserve">
    <value>System läuft</value>
  </data>
  <data name="ControllerHelper_System_is_ready" xml:space="preserve">
    <value>System ist bereit</value>
  </data>
  <data name="ControllerHelper_System_is_enabled" xml:space="preserve">
    <value>System ist aktiviert</value>
  </data>
  <data name="ControllerHelper_System_bus_is_connected" xml:space="preserve">
    <value>Systembus ist verbunden</value>
  </data>
  <data name="ControllerHelper_System_is_in_error_state" xml:space="preserve">
    <value>System ist im Fehlerzustand</value>
  </data>
  <data name="ControllerHelper_Axis_driver_error" xml:space="preserve">
    <value>Achsdriverfehler</value>
  </data>
  <data name="ControllerHelper_Axis_movement_error" xml:space="preserve">
    <value>Achsmovementfehler</value>
  </data>
  <data name="ControllerHelper_Axis_error_status" xml:space="preserve">
    <value>Achserrorzustand</value>
  </data>
  <data name="ControllerHelper_Axis_alarm" xml:space="preserve">
    <value>Achsalarm</value>
  </data>
  <data name="ControllerHelper_Positive_limit_of_axis" xml:space="preserve">
    <value>Positive Achsenbegrenzung</value>
  </data>
  <data name="ControllerHelper_Negative_limit_of_axis" xml:space="preserve">
    <value>Negative Achsenbegrenzung</value>
  </data>
  <data name="ControllerHelper_Axis_warning" xml:space="preserve">
    <value>Achswarnung</value>
  </data>
  <data name="ControllerHelper_Axis_in_left_position" xml:space="preserve">
    <value>Achse links erreicht</value>
  </data>
  <data name="ControllerHelper_Axis_in_right_position" xml:space="preserve">
    <value>Achse rechts erreicht</value>
  </data>
  <data name="ControllerHelper_Axis_has_reached_the_target_position" xml:space="preserve">
    <value>Achse hat das Zielposition erreicht</value>
  </data>
  <data name="ControllerHelper_Axis_is_at_the_workstation" xml:space="preserve">
    <value>Achse ist auf der Arbeitsstation</value>
  </data>
  <data name="ControllerHelper_Axis_notification" xml:space="preserve">
    <value>Achsenbenachrichtigung</value>
  </data>
  <data name="ControllerHelper_Axis_is_running" xml:space="preserve">
    <value>Achse läuft</value>
  </data>
  <data name="ControllerHelper_Axis_is_enabled" xml:space="preserve">
    <value>Achse ist aktiviert</value>
  </data>
  <data name="ControllerHelper_Axis_status" xml:space="preserve">
    <value>Achsenzustand</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_running" xml:space="preserve">
    <value>Rotierende Achse läuft</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_homing_completed" xml:space="preserve">
    <value>Rotierende Achse homing abgeschlossen</value>
  </data>
  <data name="ControllerHelper_Rotary_axis_is_enabled" xml:space="preserve">
    <value>Rotierende Achse ist aktiviert</value>
  </data>
  <data name="ControllerHelper_Rotary_axis" xml:space="preserve">
    <value>Rotierende Achse</value>
  </data>
  <data name="ServoSerialPortClient_Driver_connected_successfully" xml:space="preserve">
    <value>Treiber erfolgreich verbunden!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_disconnected" xml:space="preserve">
    <value>Treiber getrennt!</value>
  </data>
  <data name="ServoSerialPortClient_Driver_parameter_recovery_successful" xml:space="preserve">
    <value>Treiberparameter erfolgreich wiederhergestellt!</value>
  </data>
</root>