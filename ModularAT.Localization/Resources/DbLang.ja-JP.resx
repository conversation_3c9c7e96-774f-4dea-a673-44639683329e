<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="/ControlerOnlineConfig" xml:space="preserve">
        <value>オンライン設定</value>
    </data>
    <data name="/ControlerGenerateConfig" xml:space="preserve">
        <value>設定生成</value>
    </data>
    <data name="/DataTrace/OperateLog" xml:space="preserve">
        <value>操作ログ</value>
    </data>
    <data name="/DataTrace" xml:space="preserve">
        <value>データトレース</value>
    </data>
    <data name="/Scope/StartRun" xml:space="preserve">
        <value>収集</value>
    </data>
    <data name="/ServoSetting/ErrorRecordClear" xml:space="preserve">
        <value>障害記録の削除</value>
    </data>
    <data name="/ServoSetting/ErrorReset" xml:space="preserve">
        <value>エラーリセット</value>
    </data>
    <data name="/ServoSetting/ParaClear" xml:space="preserve">
        <value>デフォルトパラメータの復元</value>
    </data>
    <data name="/ServoSetting/SetParamsAll" xml:space="preserve">
        <value>全て書き込み</value>
    </data>
    <data name="/ServoSetting/SetPara" xml:space="preserve">
        <value>選択書き込み</value>
    </data>
    <data name="/ControlerTranStatus/Execute" xml:space="preserve">
        <value>実行</value>
    </data>
    <data name="/ControlerSys/Execute" xml:space="preserve">
        <value>実行</value>
    </data>
    <data name="/ControlerAxis/Stop" xml:space="preserve">
        <value>停止</value>
    </data>
    <data name="/ControlerAxis/Execute" xml:space="preserve">
        <value>実行</value>
    </data>
    <data name="/BasePermAssign" xml:space="preserve">
        <value>権限割り当て</value>
    </data>
    <data name="/BasePermission" xml:space="preserve">
        <value>メニュー</value>
    </data>
    <data name="/BaseUser" xml:space="preserve">
        <value>ユーザー</value>
    </data>
    <data name="/BaseRole" xml:space="preserve">
        <value>役割</value>
    </data>
    <data name="/Scope" xml:space="preserve">
        <value>オシロスコープ</value>
    </data>
    <data name="/ServoSetting" xml:space="preserve">
        <value>サーボ設定</value>
    </data>
    <data name="/ControlerSys" xml:space="preserve">
        <value>システム制御</value>
    </data>
    <data name="/ControlerTranStatus" xml:space="preserve">
        <value>接続状態</value>
    </data>
    <data name="/ControlerAxis" xml:space="preserve">
        <value>軸制御</value>
    </data>
    <data name="/Simulation" xml:space="preserve">
        <value>システム全体</value>
    </data>
    <data name="/ControlerClient" xml:space="preserve">
        <value>コントローラ接続</value>
    </data>
    <data name="/ServoSerialPort" xml:space="preserve">
        <value>ドライバ接続</value>
    </data>
    <data name="/Base" xml:space="preserve">
        <value>基本設定</value>
    </data>
    <data name="/Servo" xml:space="preserve">
        <value>ドライバ</value>
    </data>
    <data name="/Controller" xml:space="preserve">
        <value>コントローラ</value>
    </data>
    <data name="/Devices" xml:space="preserve">
        <value>機器接続</value>
    </data>
    <data name="LL_Resistance" xml:space="preserve">
        <value>モータ線抵抗(mΩ)</value>
    </data>
    <data name="LL_Inductance" xml:space="preserve">
        <value>モータ線インダクタンス(mH)</value>
    </data>
    <data name="Rate_Current" xml:space="preserve">
        <value>モータ定格電流(Arms)</value>
    </data>
    <data name="Rate_Torque" xml:space="preserve">
        <value>モータ定格トルク(N)</value>
    </data>
    <data name="Peak_Current" xml:space="preserve">
        <value>モータピーク電流(Arms)</value>
    </data>
    <data name="Torque_Constant" xml:space="preserve">
        <value>モータトルク定数(N/Arms)</value>
    </data>
    <data name="Back_Emf_Coeff" xml:space="preserve">
        <value>モータ逆起電力係数(V(pk)/m/s)</value>
    </data>
    <data name="Electrode_Distance" xml:space="preserve">
        <value>モータ極対N-N距離(mm)</value>
    </data>
    <data name="Number_Of_Poles" xml:space="preserve">
        <value>モータ極対数</value>
    </data>
    <data name="Elec_Offset" xml:space="preserve">
        <value>電気角オフセット(PosUnit)</value>
    </data>
    <data name="U_Current" xml:space="preserve">
        <value>モータU相電流(A)</value>
    </data>
    <data name="V_Current" xml:space="preserve">
        <value>モータV相電流(A)</value>
    </data>
    <data name="W_Current" xml:space="preserve">
        <value>モータW相電流(A)</value>
    </data>
    <data name="Bus_Voltage" xml:space="preserve">
        <value>バス電圧(V)</value>
    </data>
    <data name="DRIVER_VERSION_0" xml:space="preserve">
        <value>ドライババージョン-チップ型番</value>
    </data>
    <data name="DRIVER_VERSION_1" xml:space="preserve">
        <value>ドライババージョン-メジャーバージョンアップ</value>
    </data>
    <data name="DRIVER_VERSION_2" xml:space="preserve">
        <value>ドライババージョン-機能アップデート</value>
    </data>
    <data name="DRIVER_VERSION_3" xml:space="preserve">
        <value>ドライババージョン-バグ修正</value>
    </data>
    <data name="DRIVER_VERSION_4" xml:space="preserve">
        <value>ドライババージョン-デバッグ/リリース(0-デバッグ 1-リリース)</value>
    </data>
    <data name="ScopeCtl" xml:space="preserve">
        <value>オシロスコープ制御</value>
    </data>
    <data name="ScopeMapList0" xml:space="preserve">
        <value>オシロスコープチャンネル0</value>
    </data>
    <data name="ScopeMapList1" xml:space="preserve">
        <value>オシロスコープチャンネル1</value>
    </data>
    <data name="ScopeMapList2" xml:space="preserve">
        <value>オシロスコープチャンネル2</value>
    </data>
    <data name="ScopeMapList3" xml:space="preserve">
        <value>オシロスコープチャンネル3</value>
    </data>
    <data name="ScopeMapList4" xml:space="preserve">
        <value>オシロスコープチャンネル4</value>
    </data>
    <data name="ScopeMapList5" xml:space="preserve">
        <value>オシロスコープチャンネル5</value>
    </data>
    <data name="ScopeMapList6" xml:space="preserve">
        <value>オシロスコープチャンネル6</value>
    </data>
    <data name="ScopeMapList7" xml:space="preserve">
        <value>オシロスコープチャンネル7</value>
    </data>
    <data name="EncoderType" xml:space="preserve">
        <value>エンコーダタイプ</value>
    </data>
    <data name="EncoderResolution" xml:space="preserve">
        <value>エンコーダ分解能</value>
    </data>
    <data name="EncVersion_Master" xml:space="preserve">
        <value>エンコーダバージョン-メジャーバージョンアップ</value>
    </data>
    <data name="EncVersion_Func" xml:space="preserve">
        <value>エンコーダバージョン-機能アップデート</value>
    </data>
    <data name="EncVersion_Bug" xml:space="preserve">
        <value>エンコーダバージョン-バグ修正</value>
    </data>
    <data name="EncVersion_Debug" xml:space="preserve">
        <value>エンコーダバージョン-デバッグバージョン</value>
    </data>
    <data name="EncDebugFunc" xml:space="preserve">
        <value>エンコーダデバッグ機能選択</value>
    </data>
    <data name="EncoderPos0" xml:space="preserve">
        <value>エンコーダ0位置デバッグインターフェイス</value>
    </data>
    <data name="EncoderPos1" xml:space="preserve">
        <value>エンコーダ1位置デバッグインターフェイス</value>
    </data>
    <data name="OCD_Threshold" xml:space="preserve">
        <value>過電流検出閾値(A)</value>
    </data>
    <data name="OCD_Time" xml:space="preserve">
        <value>過電流検出判定時間(ms)</value>
    </data>
    <data name="OLD_RateCur" xml:space="preserve">
        <value>過負荷判定電流閾値</value>
    </data>
    <data name="OLD_PeakCur" xml:space="preserve">
        <value>過負荷ピーク電流</value>
    </data>
    <data name="Dur_Of_PeakCur" xml:space="preserve">
        <value>ピーク電流許容持続時間(ms)</value>
    </data>
    <data name="Heat_Coeff" xml:space="preserve">
        <value>I2t増加補償係数</value>
    </data>
    <data name="Cool_Coeff" xml:space="preserve">
        <value>I2t減少補償係数</value>
    </data>
    <data name="Locked_rotor_Current" xml:space="preserve">
        <value>モータロックアップ検出電流閾値(A)</value>
    </data>
    <data name="Locked_rotor_Time" xml:space="preserve">
        <value>モータロックアップ判定時間(ms)</value>
    </data>
    <data name="Locked_rotor_Vel" xml:space="preserve">
        <value>モータロックアップ速度判定閾値(mm/s)</value>
    </data>
    <data name="MOS_Temp" xml:space="preserve">
        <value>MOS温度警報閾値(℃)</value>
    </data>
    <data name="Encoder_Commu_Err" xml:space="preserve">
        <value>エンコーダ通信エラー回数警報閾値</value>
    </data>
    <data name="Stall_Dect" xml:space="preserve">
        <value>モータスピンアウト検出閾値(mm/s)</value>
    </data>
    <data name="Over_Voltage" xml:space="preserve">
        <value>過電圧保護閾値(V)</value>
    </data>
    <data name="Under_Voltage" xml:space="preserve">
        <value>低電圧保護閾値(V)</value>
    </data>
    <data name="New_ErrIndex" xml:space="preserve">
        <value>最新のエラー位置</value>
    </data>
    <data name="Pre_ErrIndex" xml:space="preserve">
        <value>起動時のエラーインデックス</value>
    </data>
    <data name="His_Err_Code0" xml:space="preserve">
        <value>過去のエラー0</value>
    </data>
    <data name="His_Err_Code1" xml:space="preserve">
        <value>過去のエラー1</value>
    </data>
    <data name="His_Err_Code2" xml:space="preserve">
        <value>過去のエラー2</value>
    </data>
    <data name="His_Err_Code3" xml:space="preserve">
        <value>過去のエラー3</value>
    </data>
    <data name="His_Err_Code4" xml:space="preserve">
        <value>過去のエラー4</value>
    </data>
    <data name="His_Err_Code5" xml:space="preserve">
        <value>過去のエラー5</value>
    </data>
    <data name="His_Err_Code6" xml:space="preserve">
        <value>過去のエラー6</value>
    </data>
    <data name="His_Err_Code7" xml:space="preserve">
        <value>過去のエラー7</value>
    </data>
    <data name="His_Err_Code8" xml:space="preserve">
        <value>過去のエラー8</value>
    </data>
    <data name="His_Err_Code9" xml:space="preserve">
        <value>過去のエラー9</value>
    </data>
    <data name="His_Err_Code10" xml:space="preserve">
        <value>過去のエラー10</value>
    </data>
    <data name="His_Err_Code11" xml:space="preserve">
        <value>過去のエラー11</value>
    </data>
    <data name="His_Err_Code12" xml:space="preserve">
        <value>過去のエラー12</value>
    </data>
    <data name="His_Err_Code13" xml:space="preserve">
        <value>過去のエラー13</value>
    </data>
    <data name="His_Err_Code14" xml:space="preserve">
        <value>過去のエラー14</value>
    </data>
    <data name="His_Err_Code15" xml:space="preserve">
        <value>過去のエラー15</value>
    </data>
    <data name="His_Err_Code16" xml:space="preserve">
        <value>過去のエラー16</value>
    </data>
    <data name="His_Err_Code17" xml:space="preserve">
        <value>過去のエラー17</value>
    </data>
    <data name="His_Err_Code18" xml:space="preserve">
        <value>過去のエラー18</value>
    </data>
    <data name="His_Err_Code19" xml:space="preserve">
        <value>過去のエラー19</value>
    </data>
    <data name="ControlWord" xml:space="preserve">
        <value>制御ワード</value>
    </data>
    <data name="StatusWord" xml:space="preserve">
        <value>状態ワード</value>
    </data>
    <data name="ModeOfOperation" xml:space="preserve">
        <value>動作状態</value>
    </data>
    <data name="ModesOfOperationDisplay" xml:space="preserve">
        <value>実際の状態</value>
    </data>
    <data name="Target_Position" xml:space="preserve">
        <value>目標位置(PosUnit)</value>
    </data>
    <data name="Actual_Position" xml:space="preserve">
        <value>実際の位置(PosUnit)</value>
    </data>
    <data name="Position_Kp" xml:space="preserve">
        <value>位置ループ比例係数((mm/s)/PosUnit)</value>
    </data>
    <data name="Position_Ki" xml:space="preserve">
        <value>位置ループ積分係数</value>
    </data>
    <data name="Position_Kd" xml:space="preserve">
        <value>位置ループ微分係数</value>
    </data>
    <data name="PILF_Cutoff_Freq" xml:space="preserve">
        <value>位置指令ローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="PosCtrl_ClamUp" xml:space="preserve">
        <value>位置制御出力クランプUP(mm/s)</value>
    </data>
    <data name="PosCtrl_ClamLow" xml:space="preserve">
        <value>位置制御出力クランプLOW(mm/s)</value>
    </data>
    <data name="PISA_Cutoff" xml:space="preserve">
        <value>位置指令平均化フィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="Target_Velocity" xml:space="preserve">
        <value>目標速度(mm/s)</value>
    </data>
    <data name="Actual_Velocity" xml:space="preserve">
        <value>実際の速度(mm/s)</value>
    </data>
    <data name="Velocity_Kp" xml:space="preserve">
        <value>速度ループ比例係数(A/(mm/s))</value>
    </data>
    <data name="Velocity_Ki" xml:space="preserve">
        <value>速度ループ積分係数(A/mm)</value>
    </data>
    <data name="Velocity_Kd" xml:space="preserve">
        <value>速度ループ微分係数(A/(mm/s^2))</value>
    </data>
    <data name="Velocity_Kc" xml:space="preserve">
        <value>速度ループ積分飽和防止係数</value>
    </data>
    <data name="Vel_FF_Gain" xml:space="preserve">
        <value>速度フィードフォワードゲイン係数</value>
    </data>
    <data name="Vel_FFLPF_CutFreq" xml:space="preserve">
        <value>速度フィードフォワードローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="Vel_FBLPF_CutFreq" xml:space="preserve">
        <value>速度フィードバックローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="VILP_Cutoff_Freq" xml:space="preserve">
        <value>速度指令ローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="VelCtrl_ClamUp" xml:space="preserve">
        <value>速度制御出力クランプUP(A)</value>
    </data>
    <data name="VelCtrl_ClamLow" xml:space="preserve">
        <value>速度制御出力クランプLOW(A)</value>
    </data>
    <data name="Iq_CMD" xml:space="preserve">
        <value>Q軸電流目標値(A)</value>
    </data>
    <data name="Id_CMD" xml:space="preserve">
        <value>D軸電流目標値(A)</value>
    </data>
    <data name="Iq_FB" xml:space="preserve">
        <value>Q軸電流フィードバック値(A)</value>
    </data>
    <data name="Id_FB" xml:space="preserve">
        <value>D軸電流フィードバック値(A)</value>
    </data>
    <data name="Current_Kp" xml:space="preserve">
        <value>電流ループ比例係数</value>
    </data>
    <data name="Current_Ki" xml:space="preserve">
        <value>電流ループ積分係数</value>
    </data>
    <data name="Current_Kd" xml:space="preserve">
        <value>電流ループ微分係数</value>
    </data>
    <data name="Current_Ke_D" xml:space="preserve">
        <value>D軸逆起電力補償係数</value>
    </data>
    <data name="Current_Ke_Q" xml:space="preserve">
        <value>Q軸逆起電力補償係数</value>
    </data>
    <data name="Current_Kf" xml:space="preserve">
        <value>永久磁石逆起電力補償係数</value>
    </data>
    <data name="Cur_FB_CutFreq" xml:space="preserve">
        <value>電流フィードバックローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="CILP_CutFreq" xml:space="preserve">
        <value>電流指令ローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="Cur_FF_Gain" xml:space="preserve">
        <value>電流フィードフォワードゲイン係数</value>
    </data>
    <data name="Cur_FFLPF_CutFreq" xml:space="preserve">
        <value>電流フィードフォワードローパスフィルタカットオフ周波数(Hz)</value>
    </data>
    <data name="CINF_NotchFreq" xml:space="preserve">
        <value>電流指令ノッチフィルタ中心周波数(Hz)</value>
    </data>
    <data name="CINF_CutFreq" xml:space="preserve">
        <value>電流指令ノッチフィルタ帯域幅(Hz)</value>
    </data>
    <data name="CINF_Depth" xml:space="preserve">
        <value>電流指令ノッチフィルタ深さ(dB)</value>
    </data>
</root>