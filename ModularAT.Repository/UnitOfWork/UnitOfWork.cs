using System;
using SqlSugar;

namespace ModularAT.Repository.UnitOfWork;

public class UnitOfWork : IUnitOfWork
{
    private readonly ISqlSugarClient _sqlSugarClient;
    //private readonly ILogger<UnitOfWork> _logger;

    public UnitOfWork(ISqlSugarClient sqlSugarClient)
    {
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    ///     获取DB，保证唯一性
    /// </summary>
    /// <returns></returns>
    public SqlSugarClient GetDbClient()
    {
        // 必须要as，后边会用到切换数据库操作
        return _sqlSugarClient as SqlSugarClient;
    }

    public void BeginTran()
    {
        GetDbClient().BeginTran();
    }

    public void CommitTran()
    {
        try
        {
            GetDbClient().CommitTran(); //
        }
        catch (Exception ex)
        {
            GetDbClient().RollbackTran();
            //_logger.LogError($"{ex.Message}\r\n{ex.InnerException}");
        }
    }

    public void RollbackTran()
    {
        GetDbClient().RollbackTran();
    }
}