using System;
using System.Linq;
using System.Text.Json;
using CommunityToolkit.Mvvm.Messaging;
using CommunityToolkit.Mvvm.Messaging.Messages;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ModularAT.Common.Helper;
using ModularAT.Common.Log;
using Rougamo;
using Rougamo.Context;
using Rougamo.Metadatas;

namespace ModularAT.Common.Attributes;

[AttributeUsage(AttributeTargets.Method)]
[Lifetime(Lifetime.Singleton)]
public class LogAttribute : MoAttribute
{
    private readonly JsonSerializerOptions _option =
        ServiceLocator.Services.GetRequiredService<IOptions<JsonSerializerOptions>>().Value;

    public override void OnEntry(MethodContext context)
    {
        var method = context.Method;
        var className = method.DeclaringType.Name;
        var methodName = method.Name;
        var parameters = method.GetParameters();
        var argumentValues = context.Arguments;

        var parameterDetails = parameters.Select((p, i) =>
        {
            return !p.ParameterType.IsValueType
                ? $"{p.Name}={JsonSerializer.Serialize(argumentValues[i], _option)}"
                : $"{p.Name}={argumentValues[i]}";
        }).ToArray();

        LogHelper.GetInstance()
            .Info($"{className}.{methodName} :Entering, Params:( {string.Join(", ", parameterDetails)})");
        WeakReferenceMessenger.Default.Send(new ValueChangedMessage<LogRecord>(new LogRecord(className, methodName,
            "Entering", $"Params:( {string.Join(", ", parameterDetails)})", DateTime.Now)));
    }

    public override void OnSuccess(MethodContext context)
    {
        var method = context.Method;
        var className = method.DeclaringType.Name;
        var methodName = method.Name;
        var returnValue = context.ReturnValue;

        var returnDetail = context.ReturnType == typeof(void)
            ? "void"
            : !context.ReturnType.IsValueType
                ? JsonSerializer.Serialize(returnValue, _option)
                : returnValue != null
                    ? returnValue.ToString()
                    : "null";

        LogHelper.GetInstance().Info($"{className}.{methodName} :Succeed, Return Value: {returnDetail}");
        WeakReferenceMessenger.Default.Send(new ValueChangedMessage<LogRecord>(new LogRecord(className, methodName,
            "Succeed", $"Return Value: {returnDetail}", DateTime.Now)));
    }

    public override void OnException(MethodContext context)
    {
        var method = context.Method;
        var className = method.DeclaringType.Name;
        var methodName = method.Name;
        var exception = context.Exception;
        LogHelper.GetInstance().Info($"{className}.{methodName} :Exception, {exception.Message}", exception);
        WeakReferenceMessenger.Default.Send(new ValueChangedMessage<LogRecord>(new LogRecord(className, methodName,
            "Exception", $"{exception.Message}", DateTime.Now)));
    }

    public record LogRecord(string Area, string Controller, string Action, string Description, DateTime LogTime);

    //public override void OnExit(MethodContext context)
    //{
    //    var method = context.Method;
    //    var className = method.DeclaringType.Name;
    //    var methodName = method.Name;
    //    LogHelper.GetInstance().Info($"Exiting: {className}.{methodName}");
    //}
}