using System;

namespace ModularAT.Common.Extensions;

/// <summary>
///     提供各种对象转换为基本数据类型的方法
/// </summary>
public static class UtilConvert
{
    /// <summary>
    ///     将对象转换为整数，如果转换失败则返回0
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <returns>转换后的整数</returns>
    public static int ObjToInt(this object thisValue)
    {
        var reval = 0;
        if (thisValue == null) return 0;
        if (thisValue != null && thisValue != DBNull.Value && int.TryParse(thisValue.ToString(), out reval))
            return reval;
        return reval;
    }

    /// <summary>
    ///     将对象转换为整数，如果转换失败则返回指定的错误值
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <param name="errorValue">转换失败时返回的错误值</param>
    /// <returns>转换后的整数或错误值</returns>
    public static int ObjToInt(this object thisValue, int errorValue)
    {
        var reval = 0;
        if (thisValue != null && thisValue != DBNull.Value && int.TryParse(thisValue.ToString(), out reval))
            return reval;
        return errorValue;
    }

    /// <summary>
    ///     将对象转换为货币值（双精度浮点数），如果转换失败则返回0
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <returns>转换后的货币值</returns>
    public static double ObjToMoney(this object thisValue)
    {
        double reval = 0;
        if (thisValue != null && thisValue != DBNull.Value &&
            double.TryParse(thisValue.ToString(), out reval)) return reval;
        return 0;
    }

    /// <summary>
    ///     将对象转换为货币值（双精度浮点数），如果转换失败则返回指定的错误值
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <param name="errorValue">转换失败时返回的错误值</param>
    /// <returns>转换后的货币值或错误值</returns>
    public static double ObjToMoney(this object thisValue, double errorValue)
    {
        double reval = 0;
        if (thisValue != null && thisValue != DBNull.Value &&
            double.TryParse(thisValue.ToString(), out reval)) return reval;
        return errorValue;
    }

    /// <summary>
    ///     将对象转换为字符串，如果对象为空则返回空字符串
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <returns>转换后的字符串</returns>
    public static string ObjToString(this object thisValue)
    {
        if (thisValue != null) return thisValue.ToString().Trim();
        return "";
    }

    /// <summary>
    ///     检查对象是否不为空或不为null、undefined
    /// </summary>
    /// <param name="thisValue">要检查的对象</param>
    /// <returns>如果对象不为空且不为null、undefined则返回true，否则返回false</returns>
    public static bool IsNotEmptyOrNull(this object thisValue)
    {
        return thisValue.ObjToString() != "" && thisValue.ObjToString() != "undefined" &&
               thisValue.ObjToString() != "null";
    }

    /// <summary>
    ///     将对象转换为字符串，如果对象为空则返回指定的错误值
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <param name="errorValue">转换失败时返回的错误值</param>
    /// <returns>转换后的字符串或错误值</returns>
    public static string ObjToString(this object thisValue, string errorValue)
    {
        if (thisValue != null) return thisValue.ToString().Trim();
        return errorValue;
    }

    /// <summary>
    ///     将对象转换为十进制数，如果转换失败则返回0
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <returns>转换后的十进制数</returns>
    public static decimal ObjToDecimal(this object thisValue)
    {
        decimal reval = 0;
        if (thisValue != null && thisValue != DBNull.Value &&
            decimal.TryParse(thisValue.ToString(), out reval)) return reval;
        return 0;
    }

    /// <summary>
    ///     将对象转换为十进制数，如果转换失败则返回指定的错误值
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <param name="errorValue">转换失败时返回的错误值</param>
    /// <returns>转换后的十进制数或错误值</returns>
    public static decimal ObjToDecimal(this object thisValue, decimal errorValue)
    {
        decimal reval = 0;
        if (thisValue != null && thisValue != DBNull.Value &&
            decimal.TryParse(thisValue.ToString(), out reval)) return reval;
        return errorValue;
    }

    /// <summary>
    ///     将对象转换为日期时间，如果转换失败则返回DateTime.MinValue
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <returns>转换后的日期时间</returns>
    public static DateTime ObjToDate(this object thisValue)
    {
        var reval = DateTime.MinValue;
        if (thisValue != null && thisValue != DBNull.Value && DateTime.TryParse(thisValue.ToString(), out reval))
            reval = Convert.ToDateTime(thisValue);
        return reval;
    }

    /// <summary>
    ///     将对象转换为日期时间，如果转换失败则返回指定的错误值
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <param name="errorValue">转换失败时返回的错误值</param>
    /// <returns>转换后的日期时间或错误值</returns>
    public static DateTime ObjToDate(this object thisValue, DateTime errorValue)
    {
        var reval = DateTime.MinValue;
        if (thisValue != null && thisValue != DBNull.Value &&
            DateTime.TryParse(thisValue.ToString(), out reval)) return reval;
        return errorValue;
    }

    /// <summary>
    ///     将对象转换为布尔值，如果转换失败则返回false
    /// </summary>
    /// <param name="thisValue">要转换的对象</param>
    /// <returns>转换后的布尔值</returns>
    public static bool ObjToBool(this object thisValue)
    {
        var reval = false;
        if (thisValue != null && thisValue != DBNull.Value && bool.TryParse(thisValue.ToString(), out reval))
            return reval;
        return reval;
    }

    /// <summary>
    ///     获取当前时间的时间戳（自1970年1月1日以来的秒数）
    /// </summary>
    /// <param name="thisValue">要转换的日期时间</param>
    /// <returns>时间戳字符串</returns>
    public static string DateToTimeStamp(this DateTime thisValue)
    {
        var ts = thisValue - new DateTime(1970, 1, 1, 0, 0, 0, 0);
        return Convert.ToInt64(ts.TotalSeconds).ToString();
    }
}