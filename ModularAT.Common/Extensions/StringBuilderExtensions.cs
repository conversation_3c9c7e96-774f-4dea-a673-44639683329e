using System.Text;

namespace ModularAT.Common.Extensions;

public static class StringBuilderExtensions
{
    public static int IndexOf(this StringBuilder sb, string value)
    {
        // 检查 Value 是否为空或 null
        if (string.IsNullOrEmpty(value)) return 0; // 如果 Value 是空的，可以认为它在 sb 的开头

        var length = sb.Length;
        var valueLength = value.Length;

        // 检查 Value 是否可能存在于 sb 中
        if (length < valueLength) return -1;

        for (var i = 0; i <= length - valueLength; i++)
        {
            var match = true;
            //使用 sb[i] 来逐字符比较，这样可以避免创建字符串实例
            for (var j = 0; j < valueLength; j++)
                if (sb[i + j] != value[j])
                {
                    match = false;
                    break;
                }

            if (match) return i;
        }

        return -1;
    }
}