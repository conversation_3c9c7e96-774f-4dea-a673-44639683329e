using System;
using System.Diagnostics;
using log4net;

//[assembly: log4net.Config.XmlConfigurator(ConfigFile = "log4net.config", Watch = true)]
namespace ModularAT.Common.Log;

public class LogHelper
{
    private readonly Lazy<ILog> _instance;

    // 私有构造函数，确保外部不能直接实例化
    private LogHelper(Type type)
    {
        _instance = new Lazy<ILog>(() => LogManager.GetLogger(type));
    }

    // 提供公共的静态方法来获取LogHelper实例
    public static ILog GetInstance<T>()
    {
        return new LogHelper(typeof(T))._instance.Value;
    }

    // 提供公共的静态方法来获取LogHelper实例，使用调用者的类型
    public static ILog GetInstance()
    {
        var stackTrace = new StackTrace();
        var callingType = stackTrace.GetFrame(1).GetMethod().DeclaringType;
        return new LogHelper(callingType)._instance.Value;
    }
}