using System;
using log4net.Appender;
using log4net.Core;

namespace ModularAT.Common.Log;

/// <summary>
///     目前用户控制器通讯指令记录，显示到Debug页
/// </summary>
public class UiLogAppender : AppenderSkeleton
{
    public static event Action<string> LogReceived;

    protected override void Append(LoggingEvent loggingEvent)
    {
        var logMessage = RenderLoggingEvent(loggingEvent);
        LogReceived?.Invoke(logMessage);
    }

    // 实现所需的方法，例如ActivateOptions
    public override void ActivateOptions()
    {
        base.ActivateOptions();
    }
}