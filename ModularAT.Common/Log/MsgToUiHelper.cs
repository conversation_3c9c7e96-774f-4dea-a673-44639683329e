using System;
using System.Drawing;
using log4net;
using log4net.Core;

namespace ModularAT.Common.Log;

/// <summary>
///     显示到左下角的消息反馈，不记入日志
/// </summary>
public static class MsgToUiHelper
{
    public static event Action<string, Level> MsgReceived;

    public static void SendMsgInfo(string msg)
    {
        MsgReceived?.Invoke(msg, Level.Info);
    }

    public static void SendMsgWarn(string msg)
    {
        MsgReceived?.Invoke(msg, Level.Warn);
    }

    public static void SendMsgError(string msg)
    {
        MsgReceived?.Invoke(msg, Level.Error);
    }

    public static void SendMsgNotice(string msg)
    {
        MsgReceived?.Invoke(msg, Level.Notice);
    }
}