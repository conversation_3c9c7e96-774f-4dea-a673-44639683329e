using System;
using System.Runtime.InteropServices;

namespace ModularAT.Common;

public class PerformanceTimer
{
    private readonly long freq;
    private bool isRunning;

    private long startTime;
    private long stopTime;

    public PerformanceTimer()
    {
        startTime = 0;
        stopTime = 0;
        isRunning = false;

        if (QueryPerformanceFrequency(out freq) == false) throw new Exception("Timer not supported.");
    }

    public long ElapsedMilliseconds
    {
        get
        {
            if (isRunning)
            {
                QueryPerformanceCounter(out var current);
                return (current - startTime) / (freq / 1000);
            }

            return (stopTime - startTime) / (freq / 1000);
        }
    }

    [DllImport("Kernel32.dll")]
    private static extern bool QueryPerformanceCounter(out long lpPerformanceCount);

    [DllImport("Kernel32.dll")]
    private static extern bool QueryPerformanceFrequency(out long lpFrequency);

    public void Start()
    {
        if (isRunning) throw new InvalidOperationException("Timer is already running.");

        QueryPerformanceCounter(out startTime);
        isRunning = true;
    }

    public void Stop()
    {
        if (!isRunning) throw new InvalidOperationException("Timer is not running.");

        QueryPerformanceCounter(out stopTime);
        isRunning = false;
    }
}