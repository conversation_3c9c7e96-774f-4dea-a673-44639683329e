<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Library</OutputType>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    </PropertyGroup>
    <ItemGroup>
        <None Remove="app.config"/>
    </ItemGroup>
    <ItemGroup>
        <Reference Include="PresentationCore"/>
        <Reference Include="System.Configuration"/>
        <Reference Include="System.Web"/>
        <Reference Include="WindowsBase"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="Microsoft.CSharp" Version="4.7.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0"/>
        <PackageReference Include="RandomSkunk.RuntimePolyfill" Version="1.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Rougamo.Fody" Version="5.0.0" PrivateAssets="contentfiles;analyzers"/>
        <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="10.1.1"/>
        <PackageReference Include="log4net" Version="3.1.0" />
        <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.0"/>
        <PackageReference Include="System.Memory" Version="4.6.0"/>
        <PackageReference Include="System.Numerics.Vectors" Version="4.6.0"/>
        <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.0"/>
        <PackageReference Include="System.Text.Encodings.Web" Version="9.0.0"/>
        <PackageReference Include="System.Text.Json" Version="9.0.0"/>
        <PackageReference Include="System.ValueTuple" Version="4.5.0"/>
    </ItemGroup>
    <ItemGroup>
        <EmbeddedResource Update="Properties\Resources.resx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>
    <ItemGroup>
        <None Update="log4net.config">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\ModularAT.Localization\ModularAT.Localization.csproj" />
    </ItemGroup>
</Project>