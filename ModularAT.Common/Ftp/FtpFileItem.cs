using System;

namespace ModularAT.Common.Ftp
{
    public class FtpFileItem
    {
        public string Name { get; set; }
        public string FullPath { get; set; }
        public bool IsDirectory { get; set; }
        public long Size { get; set; }
        public DateTime Modified { get; set; }

        public string SizeFormatted
        {
            get
            {
                if (IsDirectory)
                    return "";

                string[] sizes = { "B", "KB", "MB", "GB", "TB" };
                double len = Size;
                int order = 0;
                while (len >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    len = len / 1024;
                }
                return $"{len:0.##} {sizes[order]}";
            }
        }

        public string ModifiedFormatted => Modified.ToString("yyyy-MM-dd HH:mm:ss");
    }
}