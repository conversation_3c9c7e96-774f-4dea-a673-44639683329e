<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
    </configSections>
    <log4net>
        <!--通用操作日志-->
        <appender name="OperateFile" type="log4net.Appender.RollingFileAppender">
            <filter type="log4net.Filter.LevelRangeFilter">
                <levelMin value="DEBUG"/>
                <levelMax value="INFO"/>
                <acceptOnMatch value="true"/>
            </filter>
            <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
            <file value="logs\\operate"/>
            <datePattern value="yyyyMMdd'.log'"/>
            <appendToFile value="true"/>
            <maximumFileSize value="10MB"/>
            <maxSizeRollBackups value="10"/>
            <encoding value="utf-8"/>
            <rollingStyle value="Composite"/>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %-5level - %message%newline"/>
            </layout>
        </appender>
        <!--所有警告日志-->
        <appender name="WarnFile" type="log4net.Appender.RollingFileAppender">
            <filter type="log4net.Filter.LevelRangeFilter">
                <levelMin value="WARN"/>
                <levelMax value="WARN"/>
                <acceptOnMatch value="true"/>
            </filter>
            <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
            <file value="logs\\warn"/>
            <datePattern value="yyyyMMdd'.log'"/>
            <appendToFile value="true"/>
            <maximumFileSize value="10MB"/>
            <maxSizeRollBackups value="10"/>
            <encoding value="utf-8"/>
            <rollingStyle value="Composite"/>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %level %logger - %message%newline"/>
            </layout>
        </appender>
        <!--所有错误日志-->
        <appender name="ErrorFile" type="log4net.Appender.RollingFileAppender">
            <filter type="log4net.Filter.LevelRangeFilter">
                <levelMin value="ERROR"/>
                <levelMax value="ERROR"/>
                <acceptOnMatch value="true"/>
            </filter>
            <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
            <file value="logs\\error"/>
            <datePattern value="yyyyMMdd'.log'"/>
            <appendToFile value="true"/>
            <maximumFileSize value="10MB"/>
            <maxSizeRollBackups value="10"/>
            <encoding value="utf-8"/>
            <rollingStyle value="Composite"/>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %level %logger - %message%newline"/>
            </layout>
        </appender>
        <!--所有崩溃日志-->
        <appender name="FatalFile" type="log4net.Appender.FileAppender">
            <filter type="log4net.Filter.LevelRangeFilter">
                <levelMin value="FATAL"/>
                <levelMax value="FATAL"/>
                <acceptOnMatch value="true"/>
            </filter>
            <encoding value="utf-8"/>
            <file value="logs/fatal.log"/>
            <appendToFile value="true"/>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %level %logger - %message%newline"/>
            </layout>
        </appender>
        <!--控制器文件日志-->
        <appender name="ControllerFile" type="log4net.Appender.RollingFileAppender">
            <filter type="log4net.Filter.LevelRangeFilter">
                <param name="LevelMin" value="DEBUG"/>
                <param name="LevelMax" value="WARN"/>
            </filter>
            <file value="logs\\controller"/>
            <datePattern value="yyyyMMdd'.log'"/>
            <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
            <maximumFileSize value="10MB"/>
            <maxSizeRollBackups value="10"/>
            <encoding value="utf-8"/>
            <appendToFile value="true"/>
            <rollingStyle value="Composite"/>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %-5level - %message%newline"/>
            </layout>
        </appender>
        <!--伺服文件日志-->
        <appender name="ServoFile" type="log4net.Appender.RollingFileAppender">
            <file value="logs\\servo"/>
            <datePattern value="yyyyMMdd'.log'"/>
            <appendToFile value="true"/>
            <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
            <maximumFileSize value="10MB"/>
            <maxSizeRollBackups value="10"/>
            <encoding value="utf-8"/>
            <rollingStyle value="Composite"/>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %-5level - %message%newline"/>
            </layout>
        </appender>
        <!--UI反馈日志-->
        <appender name="UiLog" type="ModularAT.Common.Log.UiLogAppender">
            <filter type="log4net.Filter.LevelRangeFilter">
                <levelMin value="DEBUG"/>
                <levelMax value="WARN"/>
                <acceptOnMatch value="true"/>
            </filter>
            <layout type="log4net.Layout.PatternLayout">
                <conversionPattern value="%date [%thread] %logger - %message%newline"/>
            </layout>
        </appender>


        <!-- 根记录器配置 -->
        <root>
            <level value="ALL"/>
            <appender-ref ref="WarnFile"/>
            <appender-ref ref="ErrorFile"/>
            <appender-ref ref="FatalFile"/>
            <appender-ref ref="OperateFile"/>
        </root>

        <!-- 控制器日志组合配置 -->
        <logger name="Controller">
            <level value="DEBUG"/>
            <appender-ref ref="UiLog"/>
            <appender-ref ref="ControllerFile"/>
            <additivity value="false"/>
        </logger>

        <!--伺服日志组合-->
        <logger name="Servo">
            <level value="DEBUG"/>
            <appender-ref ref="ServoFile"/>
            <additivity value="false"/>
        </logger>
    </log4net>
</configuration>