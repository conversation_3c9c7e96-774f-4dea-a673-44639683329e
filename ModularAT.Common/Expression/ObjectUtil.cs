using ModularAT.Localization.Resources;
﻿using System;
using System.Collections.Concurrent;
using System.Linq.Expressions;

namespace ModularAT.Common;

/// <summary>
///     对象操作类
/// </summary>
/// <typeparam name="T">获取或者设置属性的类型</typeparam>
public static class ObjectUtil<T>
{
    private static readonly ConcurrentDictionary<KeyInfo, Func<object, T>> ReadPropertyValueDictionary = new();

    private static readonly ConcurrentDictionary<KeyInfo, Action<object, T>> WritePropertyValueDictionary = new();

    /// <summary>
    ///     获取指定名称的公共属性的值
    /// </summary>
    /// <param name="obj">实例对象</param>
    /// <param name="name">属性名称</param>
    /// <returns>值</returns>
    public static T GetPropertyValue(object obj, string name)
    {
        if (obj == null) throw new Exception(Lang.ObjectUtil_Object_not_empty);
        var type = obj.GetType();
        var key = new KeyInfo(type, name);
        Func<object, T> getValueAction;
        if (!ReadPropertyValueDictionary.TryGetValue(key, out getValueAction))
        {
            string errorMessage;
            if (CreateReadPropertyFunc(type, name, out getValueAction, out errorMessage))
                getValueAction = ReadPropertyValueDictionary.GetOrAdd(key, getValueAction);
            else
                throw new Exception(errorMessage);
        }

        return getValueAction.Invoke(obj);
    }

    /// <summary>
    ///     尝试获取指定名称的公共属性的值
    /// </summary>
    /// <param name="obj">实例对象</param>
    /// <param name="name">属性名称</param>
    /// <param name="value">属性值</param>
    /// <returns>是否获取成功</returns>
    public static bool TryGetPropertyValue(object obj, string name, out T value)
    {
        if (obj == null)
        {
            value = default;
            return false;
        }

        var type = obj.GetType();
        var key = new KeyInfo(type, name);
        Func<object, T> getValueAction;
        if (!ReadPropertyValueDictionary.TryGetValue(key, out getValueAction))
        {
            string errorMessage;
            if (CreateReadPropertyFunc(type, name, out getValueAction, out errorMessage))
            {
                getValueAction = ReadPropertyValueDictionary.GetOrAdd(key, getValueAction);
            }
            else
            {
                value = default;
                return false;
            }
        }

        value = getValueAction.Invoke(obj);
        return true;
    }

    /// <summary>
    ///     设置指定名称的公共属性的值
    /// </summary>
    /// <param name="obj">实例对象</param>
    /// <param name="name">属性名称</param>
    /// <param name="value">设置的值</param>
    public static void SetPropertyValue(object obj, string name, T value)
    {
        if (obj == null) throw new Exception(Lang.ObjectUtil_Object_not_empty);
        var type = obj.GetType();
        var key = new KeyInfo(type, name);
        Action<object, T> setValueAction;
        if (!WritePropertyValueDictionary.TryGetValue(key, out setValueAction))
        {
            string errorMessage;
            if (CreateWritePropertyFunc(type, name, out setValueAction, out errorMessage))
                setValueAction = WritePropertyValueDictionary.GetOrAdd(key, setValueAction);
            else
                throw new Exception(errorMessage);
        }

        setValueAction.Invoke(obj, value);
    }

    /// <summary>
    ///     尝试设置指定名称的公共属性的值
    /// </summary>
    /// <param name="obj">实例对象</param>
    /// <param name="name">属性名称</param>
    /// <param name="value">设置的值</param>
    public static bool TrySetPropertyValue(object obj, string name, T value)
    {
        if (obj == null) return false;
        var type = obj.GetType();
        var key = new KeyInfo(type, name);
        Action<object, T> setValueAction;
        if (!WritePropertyValueDictionary.TryGetValue(key, out setValueAction))
        {
            string errorMessage;
            if (CreateWritePropertyFunc(type, name, out setValueAction, out errorMessage))
                setValueAction = WritePropertyValueDictionary.GetOrAdd(key, setValueAction);
            else
                return false;
        }

        setValueAction.Invoke(obj, value);
        return true;
    }

    /// <summary>
    ///     创建写入函数
    /// </summary>
    /// <param name="type">类型</param>
    /// <param name="name">写入的字段</param>
    /// <param name="setValueAction">写入函数</param>
    /// <param name="errorMessage">异常信息</param>
    /// <returns>是否创建写入函数成功</returns>
    private static bool CreateWritePropertyFunc(Type type, string name, out Action<object, T> setValueAction,
        out string errorMessage)
    {
        var info = type.GetProperty(name);
        if (info == null)
        {
            errorMessage = string.Format("类型“{0}”中没有名称为“{1}”的公共属性！", type, name);
            setValueAction = null;
            return false;
        }

        if (info.PropertyType != typeof(T)
            //验证是否支持数值类型的隐式转换
            && !(info.PropertyType.IsValueType && typeof(T).IsValueType &&
                 ImplicitConversionUtil.ImplicitConversion(typeof(T), info.PropertyType))
            //验证是否支持里式转换
            && !info.PropertyType.IsAssignableFrom(typeof(T)))
        {
            errorMessage = string.Format("类型“{0}”中没有名称为“{1}”且类型可设置为“{2}”的公共属性！", type, name, typeof(T));
            setValueAction = null;
            return false;
        }

        if (!info.CanWrite)
        {
            errorMessage = string.Format("类型“{0}”中没有名称为“{1}”且类型为“{2}”且可写的公共属性！", type, name, typeof(T));
            setValueAction = null;
            return false;
        }

        //生成表达式树
        var oExpression = Expression.Parameter(typeof(object));
        var vExpression = Expression.Parameter(typeof(T));
        var convertExpression = Expression.Convert(oExpression, type);
        var infoExpression = Expression.Property(convertExpression, info);
        var assign = Expression.Assign(infoExpression,
            info.PropertyType == typeof(T)
                ? vExpression
                : Expression.Convert(vExpression, info.PropertyType) //转换
        );
        var lambda = Expression.Lambda<Action<object, T>>(assign, oExpression, vExpression);
        setValueAction = lambda.Compile();
        errorMessage = null;
        return true;
    }

    /// <summary>
    ///     创建读取函数
    /// </summary>
    /// <param name="type">类型</param>
    /// <param name="name">读取的字段</param>
    /// <param name="getValueFunc">读取函数</param>
    /// <param name="errorMessage">异常信息</param>
    /// <returns>是否创建写入函数成功</returns>
    private static bool CreateReadPropertyFunc(Type type, string name, out Func<object, T> getValueFunc,
        out string errorMessage)
    {
        var info = type.GetProperty(name);
        if (info == null)
        {
            errorMessage = string.Format("类型“{0}”中没有名称为“{1}”的公共属性！", type, name);
            getValueFunc = null;
            return false;
        }

        if (info.PropertyType != typeof(T)
            //验证是否支持数值类型的隐式转换
            && !(info.PropertyType.IsValueType && typeof(T).IsValueType &&
                 ImplicitConversionUtil.ImplicitConversion(info.PropertyType, typeof(T)))
            //验证是否支持里式转换
            && !typeof(T).IsAssignableFrom(info.PropertyType))
        {
            errorMessage = string.Format("类型“{0}”中没有名称为“{1}”且类型可转换为“{2}”的公共属性！", type, name, typeof(T));
            getValueFunc = null;
            return false;
        }

        if (!info.CanRead)
        {
            errorMessage = string.Format("类型“{0}”中没有名称为“{1}”且类型为“{2}”且可读的公共属性！", type, name, typeof(T));
            getValueFunc = null;
            return false;
        }

        //生成表达式树
        var oExpression = Expression.Parameter(typeof(object));
        var convertExpression = Expression.Convert(oExpression, type);
        Expression infoExpression = Expression.Property(convertExpression, info);
        var lambda = Expression.Lambda<Func<object, T>>(
            info.PropertyType == typeof(T)
                ? infoExpression
                : Expression.Convert(infoExpression, typeof(T)) //转换
            , oExpression);
        getValueFunc = lambda.Compile();
        errorMessage = null;
        return true;
    }

    /// <summary>
    ///     [Type,Name]作为Key的数据结构
    /// </summary>
    private struct KeyInfo : IEquatable<KeyInfo>
    {
        public KeyInfo(Type type, string name)
        {
            Type = type;
            Name = name;
        }

        public Type Type { get; }

        public string Name { get; }

        public bool Equals(KeyInfo other)
        {
            return Equals(Type, other.Type) && Name == other.Name;
        }

        public override bool Equals(object obj)
        {
            if (!(obj is KeyInfo)) return false;
            return Equals((KeyInfo)obj);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return ((Type != null ? Type.GetHashCode() : 0) * 397) ^ (Name != null ? Name.GetHashCode() : 0);
            }
        }
    }
}