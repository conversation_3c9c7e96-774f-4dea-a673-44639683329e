using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows;
using System.Windows.Media;

namespace ModularAT.Common.Helper;

public class ConvertHelper
{
    public static uint GetNumberBit(uint value, int index, int toBase)
    {
        switch (toBase)
        {
            case 2:
                return (value >> index) & 1u;
            case 8:
                return (value >> (index * 3)) & 7u;
            case 10:
                while (index-- > 0) value /= 10;
                return value % 10;
            case 16:
                return (value >> (index * 4)) & 0xFu;
            default:
                throw new ArgumentOutOfRangeException("toBase");
        }
    }

    public static void SetNumberBit(ref uint value, int index, uint bitValue, int toBase)
    {
        bitValue = GetNumberBit(bitValue, 0, toBase);
        switch (toBase)
        {
            case 2:
            {
                var num = 1u;
                num <<= index;
                num = ~num;
                value &= num;
                bitValue <<= index;
                value |= bitValue;
                break;
            }
            case 8:
            {
                var num = 7u;
                num <<= index * 3;
                num = ~num;
                value &= num;
                bitValue <<= index * 3;
                value |= bitValue;
                break;
            }
            case 10:
            {
                var num2 = (uint)(Math.Floor(value / Math.Pow(10.0, index + 1)) * Math.Pow(10.0, index + 1));
                var num3 = (uint)(value % Math.Pow(10.0, index));
                value = num2 + bitValue * (uint)Math.Pow(10.0, index) + num3;
                break;
            }
            case 16:
            {
                var num = 15u;
                num <<= index * 4;
                num = ~num;
                value &= num;
                bitValue <<= index * 4;
                value |= bitValue;
                break;
            }
            default:
                throw new ArgumentOutOfRangeException("toBase");
        }
    }

    public static string ConvertNumberToReadableString(long? number, int toBase, int length = 0)
    {
        if (number.HasValue)
        {
            var stringBuilder = new StringBuilder(Convert.ToString(number.Value, toBase));
            while (stringBuilder.Length < length) stringBuilder.Insert(0, '0');
            return stringBuilder.ToString().ToUpper();
        }

        return string.Empty;
    }

    public static string ConvertNumberToReadableString(long maxValue, long? number, int toBase, int childrenCount)
    {
        if (number.HasValue)
        {
            StringBuilder stringBuilder = null;
            if (toBase == 16 && childrenCount == 0)
                stringBuilder = new StringBuilder(Convert.ToString(maxValue, toBase));
            var stringBuilder2 = new StringBuilder(Convert.ToString(number.Value, toBase));
            if (stringBuilder != null)
                while (stringBuilder2.Length < stringBuilder.Length)
                    stringBuilder2.Insert(0, '0');
            else
                while (stringBuilder2.Length < childrenCount)
                    stringBuilder2.Insert(0, '0');

            return stringBuilder2.ToString().ToUpper();
        }

        return string.Empty;
    }

    public static byte[] Uint16ToBytes(string value)
    {
        var data = new byte[2];
        var bytes = BitConverter.GetBytes(uint.Parse(value));
        data[0] = bytes[0];
        data[1] = bytes[1];
        return data;
    }

    public static byte[] Int16ToBytes(string value)
    {
        var falseResult = new byte[2];
        if (!short.TryParse(value, out var convertData)) return falseResult;
        var data = new byte[2];
        var bytes = BitConverter.GetBytes(convertData);
        data[0] = bytes[0];
        data[1] = bytes[1];
        return data;
    }

    public static ushort ByteToUint16(byte HeightByte, byte LowByte)
    {
        return (ushort)(((HeightByte & 0xFF) << 8) | LowByte);
    }

    public static int ByteToInt16(byte HeightByte, byte LowByte)
    {
        var value = ((HeightByte & 0xFF) << 8) | LowByte;
        if (value > 32767) value -= 65536;
        return value;
    }

    public static byte[] Uint32toBytes(uint number)
    {
        return new byte[4]
        {
            (byte)number,
            (byte)(number >> 8),
            (byte)(number >> 16),
            (byte)(number >> 24)
        };
    }

    public static byte[] Int32toBytes(int number)
    {
        return new byte[4]
        {
            (byte)number,
            (byte)(number >> 8),
            (byte)(number >> 16),
            (byte)(number >> 24)
        };
    }

    public static byte[] FloatToBytes(float number)
    {
        var bytes = new byte[4];
        var Floats = BitConverter.GetBytes(number);
        bytes[0] = Floats[3];
        bytes[1] = Floats[2];
        bytes[2] = Floats[1];
        bytes[3] = Floats[0];
        return bytes;
    }

    public static float BytesToFloat(byte[] Byte)
    {
        return BitConverter.ToSingle(new byte[4]
        {
            Byte[3],
            Byte[2],
            Byte[1],
            Byte[0]
        }, 0);
    }

    public static long BytesToInt32(byte[] Byte)
    {
        long value = (Byte[3] << 24) + (Byte[2] << 16) + (Byte[1] << 8) + Byte[0];
        if (value > int.MaxValue) value -= uint.MaxValue;
        return value;
    }

    public static uint BytesToUInt32(byte[] Byte)
    {
        return (uint)((Byte[3] << 24) + (Byte[2] << 16) + (Byte[1] << 8) + Byte[0]);
    }

    public static byte[] StructToByte(object structObj)
    {
        var num = Marshal.SizeOf(structObj);
        var array = new byte[num];
        var intPtr = Marshal.AllocHGlobal(num);
        Marshal.StructureToPtr(structObj, intPtr, false);
        Marshal.Copy(intPtr, array, 0, num);
        Marshal.FreeHGlobal(intPtr);
        return array;
    }

    public static T BytesToStruct<T>(byte[] buff, int StartIndex)
    {
        var num = Marshal.SizeOf(typeof(T));
        var intPtr = Marshal.AllocHGlobal(num);
        Marshal.Copy(buff, StartIndex, intPtr, num);
        var obj = Marshal.PtrToStructure(intPtr, typeof(T));
        Marshal.FreeHGlobal(intPtr);
        return (T)obj;
    }

    public static T FindVisualParent<T>(DependencyObject obj) where T : class
    {
        while (obj != null)
        {
            if (!(obj is T result))
            {
                obj = VisualTreeHelper.GetParent(obj);
                continue;
            }

            return result;
        }

        return null;
    }
}