using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Text.Json;

namespace ModularAT.Common.Helper;

public class JsonHelper
{
    /// <summary>
    ///     转换对象为JSON格式数据
    /// </summary>
    /// <typeparam name="T">类</typeparam>
    /// <param name="obj">对象</param>
    /// <returns>字符格式的JSON数据</returns>
    public static string GetJSON<T>(object obj)
    {
        var result = string.Empty;
        var serializer =
            new DataContractJsonSerializer(typeof(T));
        using (var ms = new MemoryStream())
        {
            serializer.WriteObject(ms, obj);
            result = Encoding.UTF8.GetString(ms.ToArray());
        }

        return result;
    }

    /// <summary>
    ///     转换List<T>的数据为JSON格式
    /// </summary>
    /// <typeparam name="T">类</typeparam>
    /// <param name="vals">列表值</param>
    /// <returns>JSON格式数据</returns>
    public string JSON<T>(List<T> vals)
    {
        var st = new StringBuilder();
        try
        {
            var s = new DataContractJsonSerializer(typeof(T));

            foreach (var city in vals)
                using (var ms = new MemoryStream())
                {
                    s.WriteObject(ms, city);
                    st.Append(Encoding.UTF8.GetString(ms.ToArray()));
                }
        }
        catch (Exception)
        {
        }

        return st.ToString();
    }

    /// <summary>
    ///     JSON格式字符转换为T类型的对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="jsonStr"></param>
    /// <returns></returns>
    public static T ParseFormByJson<T>(string jsonStr)
    {
        var obj = Activator.CreateInstance<T>();
        using (var ms =
               new MemoryStream(Encoding.UTF8.GetBytes(jsonStr)))
        {
            var serializer =
                new DataContractJsonSerializer(typeof(T));
            return (T)serializer.ReadObject(ms);
        }
    }

    public string JSON1<SendData>(List<SendData> vals)
    {
        var st = new StringBuilder();
        try
        {
            var s = new DataContractJsonSerializer(typeof(SendData));

            foreach (var city in vals)
                using (var ms = new MemoryStream())
                {
                    s.WriteObject(ms, city);
                    st.Append(Encoding.UTF8.GetString(ms.ToArray()));
                }
        }
        catch (Exception)
        {
        }

        return st.ToString();
    }

    /// <summary>
    /// 泛型方法，用于将对象序列化成JSON字符串并保存到文件
    /// </summary>
    /// <typeparam name="T">要序列化的对象类型</typeparam>
    /// <param name="item">要序列化的对象</param>
    /// <param name="filePath">保存JSON的文件路径</param>
    /// <param name="options">JSON序列化选项，可选</param>
    /// <exception cref="JsonException">JSON序列化失败时抛出</exception>
    /// <exception cref="IOException">文件写入失败时抛出</exception>
    public static void SaveToJson<T>(T item, string filePath, JsonSerializerOptions options = null) where T : class
    {
        if (item == null) throw new ArgumentNullException(nameof(item), "要序列化的对象不能为null");
        if (string.IsNullOrEmpty(filePath)) throw new ArgumentNullException(nameof(filePath), "文件路径不能为空");
        
        try
        {
            // 如果没有提供选项，则使用默认选项
            options ??= new JsonSerializerOptions
            {
                WriteIndented = true // 输出格式化的JSON
            };
            
            var jsonString = JsonSerializer.Serialize(item, options);
            var directoryPath = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath)) 
                Directory.CreateDirectory(directoryPath);
                
            File.WriteAllText(filePath, jsonString); // 写入文件
        }
        catch (JsonException ex)
        {
            // 处理序列化失败的情况
            throw new JsonException($"无法将类型 {typeof(T).Name} 序列化为JSON。错误详情: {ex.Message}", ex);
        }
        catch (Exception ex) when (ex is IOException or UnauthorizedAccessException)
        {
            // 处理文件写入失败的情况
            throw new IOException($"将JSON写入文件 {filePath} 时发生错误: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 泛型方法，用于从JSON字符串反序列化对象，并处理无法序列化的情况
    /// </summary>
    /// <typeparam name="T">要反序列化的目标类型</typeparam>
    /// <param name="filePath">JSON文件路径</param>
    /// <param name="options">JSON序列化选项，可选</param>
    /// <returns>反序列化后的对象</returns>
    /// <exception cref="FileNotFoundException">指定的文件不存在时抛出</exception>
    /// <exception cref="JsonException">JSON反序列化失败时抛出</exception>
    public static T LoadFromJson<T>(string filePath, JsonSerializerOptions options = null) where T : class
    {
        if (!File.Exists(filePath)) throw new FileNotFoundException("The specified file was not found.", filePath);
        
        try
        {
            var jsonString = File.ReadAllText(filePath); // 从文件读取JSON字符串
            return JsonSerializer.Deserialize<T>(jsonString, options);
        }
        catch (JsonException ex)
        {
            // 处理无法序列化成泛型的情况
            throw new JsonException($"无法将JSON反序列化为类型 {typeof(T).Name}。错误详情: {ex.Message}", ex);
        }
        catch (Exception ex) when (ex is not FileNotFoundException)
        {
            // 处理其他可能的异常
            throw new Exception($"从文件 {filePath} 加载JSON时发生错误: {ex.Message}", ex);
        }
    }
    
    /// <summary>
     /// 非泛型方法，用于从JSON字符串反序列化对象，适用于不确定类型或需要动态确定类型的情况
     /// </summary>
     /// <param name="filePath">JSON文件路径</param>
     /// <param name="targetType">要反序列化的目标类型</param>
     /// <param name="options">JSON序列化选项，可选</param>
     /// <returns>反序列化后的对象，需要手动转换为目标类型</returns>
     /// <exception cref="FileNotFoundException">指定的文件不存在时抛出</exception>
     /// <exception cref="JsonException">JSON反序列化失败时抛出</exception>
     public static object LoadFromJson(string filePath, Type targetType, JsonSerializerOptions options = null)
     {
         if (targetType == null) throw new ArgumentNullException(nameof(targetType), "目标类型不能为null");
         if (!File.Exists(filePath)) throw new FileNotFoundException("The specified file was not found.", filePath);
         
         try
         {
             var jsonString = File.ReadAllText(filePath); // 从文件读取JSON字符串
             return JsonSerializer.Deserialize(jsonString, targetType, options);
         }
         catch (JsonException ex)
         {
             // 处理无法序列化的情况
             throw new JsonException($"无法将JSON反序列化为类型 {targetType.Name}。错误详情: {ex.Message}", ex);
         }
         catch (Exception ex) when (ex is not FileNotFoundException)
         {
             // 处理其他可能的异常
             throw new Exception($"从文件 {filePath} 加载JSON时发生错误: {ex.Message}", ex);
         }
     }
     
     /// <summary>
     /// 尝试从JSON文件加载对象，如果失败则返回默认值而不抛出异常
     /// </summary>
     /// <typeparam name="T">要反序列化的目标类型</typeparam>
     /// <param name="filePath">JSON文件路径</param>
     /// <param name="defaultValue">反序列化失败时返回的默认值</param>
     /// <param name="options">JSON序列化选项，可选</param>
     /// <returns>成功则返回反序列化的对象，失败则返回默认值</returns>
     public static T TryLoadFromJson<T>(string filePath, T defaultValue = default, JsonSerializerOptions options = null) where T : class
     {
         try
         {
             if (!File.Exists(filePath)) return defaultValue;
             
             var jsonString = File.ReadAllText(filePath);
             var result = JsonSerializer.Deserialize<T>(jsonString, options);
             return result ?? defaultValue;
         }
         catch
         {
             // 捕获所有异常但不抛出，直接返回默认值
             return defaultValue;
         }
     }
     
     /// <summary>
     /// 尝试将对象序列化为JSON并保存到文件，如果失败则返回false而不抛出异常
     /// </summary>
     /// <typeparam name="T">要序列化的对象类型</typeparam>
     /// <param name="item">要序列化的对象</param>
     /// <param name="filePath">保存JSON的文件路径</param>
     /// <param name="options">JSON序列化选项，可选</param>
     /// <returns>操作是否成功</returns>
     public static bool TrySaveToJson<T>(T item, string filePath, JsonSerializerOptions options = null) where T : class
     {
         if (item == null || string.IsNullOrEmpty(filePath)) return false;
         
         try
         {
             // 如果没有提供选项，则使用默认选项
             options ??= new JsonSerializerOptions
             {
                 WriteIndented = true // 输出格式化的JSON
             };
             
             var jsonString = JsonSerializer.Serialize(item, options);
             var directoryPath = Path.GetDirectoryName(filePath);
             if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath)) 
                 Directory.CreateDirectory(directoryPath);
                 
             File.WriteAllText(filePath, jsonString);
             return true;
         }
         catch
         {
             // 捕获所有异常但不抛出，直接返回失败
             return false;
         }
     }
}