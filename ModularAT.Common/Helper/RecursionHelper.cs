using ModularAT.Localization.Resources;
﻿using System.Collections.Generic;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;

namespace ModularAT.Common.Helper;

/// <summary>
///     泛型递归求树形结构
/// </summary>
public static class RecursionHelper
{
    public static void LoopToAppendChildren(List<PermissionTree> all, PermissionTree curItem, int pid, bool needbtn)
    {
        var subItems = all.Where(ee => ee.Pid == curItem.Value).ToList();

        var btnItems = subItems.Where(ss => ss.IsBtn).ToList();
        if (subItems.Count > 0)
        {
            curItem.Btns = new List<PermissionTree>();
            curItem.Btns.AddRange(btnItems);
        }
        else
        {
            curItem.Btns = null;
        }

        if (!needbtn) subItems = subItems.Where(ss => ss.IsBtn == false).ToList();
        if (subItems.Count > 0)
        {
            curItem.Children = new List<PermissionTree>();
            curItem.Children.AddRange(subItems);
        }
        else
        {
            curItem.Children = null;
        }

        if (curItem.IsBtn)
        {
            //curItem.Label += Lang.RecursionHelper_Button;
        }

        foreach (var subItem in subItems)
        {
            if (subItem.Value == pid && pid > 0)
            {
                //subItem.IsChecked = true;//禁用当前节点
            }

            LoopToAppendChildren(all, subItem, pid, needbtn);
        }
    }


    public static void LoopNaviBarAppendChildren(List<NavigationBar> all, NavigationBar curItem)
    {
        var subItems = all.Where(ee => ee.pid == curItem.id).ToList();

        if (subItems.Count > 0)
        {
            curItem.children = new List<NavigationBar>();
            curItem.children.AddRange(subItems);
        }
        else
        {
            curItem.children = null;
        }


        foreach (var subItem in subItems) LoopNaviBarAppendChildren(all, subItem);
    }


    public static void LoopToAppendChildrenT<T>(List<T> all, T curItem, string parentIdName = "Pid",
        string idName = "Value", string childrenName = "Children")
    {
        var subItems = all.Where(ee =>
            ee.GetType().GetProperty(parentIdName).GetValue(ee, null).ToString() ==
            curItem.GetType().GetProperty(idName).GetValue(curItem, null).ToString()).ToList();

        if (subItems.Count > 0) curItem.GetType().GetField(childrenName).SetValue(curItem, subItems);
        foreach (var subItem in subItems) LoopToAppendChildrenT(all, subItem);
    }
}

[ObservableObject]
public partial class PermissionTree
{
    /// <summary>
    ///     节点id
    /// </summary>
    public int Value { get; set; }

    /// <summary>
    ///     父节点id
    /// </summary>
    [ObservableProperty]
    public partial int Pid { get; set; }

    public string Label { get; set; }
    public int Order { get; set; }
    public bool IsBtn { get; set; }

    [ObservableProperty] public partial bool IsChecked { get; set; }

    public List<PermissionTree> Children { get; set; }
    public List<PermissionTree> Btns { get; set; }
}

public class NavigationBar
{
    public int id { get; set; }
    public int pid { get; set; }
    public int order { get; set; }
    public string name { get; set; }
    public bool IsHide { get; set; } = false;
    public bool IsButton { get; set; } = false;
    public string path { get; set; }
    public string Func { get; set; }
    public string iconCls { get; set; }
    public NavigationBarMeta meta { get; set; }
    public List<NavigationBar> children { get; set; }
}

public class NavigationBarMeta
{
    public string title { get; set; }
    public bool requireAuth { get; set; } = true;
    public bool NoTabPage { get; set; } = false;
    public bool keepAlive { get; set; } = false;
}