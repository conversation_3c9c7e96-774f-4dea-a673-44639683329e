using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace ModularAT.Common.Helper;

public class RuntimeHelper
{
    // 由于.NET Framework没有DependencyContext，我们改用AppDomain来获取已加载的程序集
    public static List<Assembly> GetAllAssemblies()
    {
        var list = new List<Assembly>();
        var assemblies = AppDomain.CurrentDomain.GetAssemblies();

        // 假设我们有一个方法来检查程序集是否是项目程序集（非系统、非NuGet）
        foreach (var assembly in assemblies)
            if (IsProjectAssembly(assembly))
                list.Add(assembly);

        return list;
    }

    private static bool IsProjectAssembly(Assembly assembly)
    {
        return assembly.FullName.StartsWith("ModularAT.");
    }

    public static List<Assembly> GetAllMyAssemblies()
    {
        var list = new List<Assembly>();
        var assemblies = AppDomain.CurrentDomain.GetAssemblies();

        foreach (var assembly in assemblies)
            if (IsProjectAssembly(assembly) && assembly.GetName().Name != "ModularAT.Common")
                list.Add(assembly);

        return list;
    }

    public static Assembly GetAssembly(string assemblyName)
    {
        return AppDomain.CurrentDomain.GetAssemblies()
            .FirstOrDefault(asm => asm.GetName().Name == assemblyName);
    }

    public static IList<Type> GetAllTypes()
    {
        var list = new List<Type>();
        foreach (var assembly in GetAllAssemblies()) list.AddRange(assembly.GetTypes());
        return list;
    }

    public static IList<Type> GetTypesByAssembly(string assemblyName)
    {
        var assembly = GetAssembly(assemblyName);
        return assembly?.GetTypes().ToList() ?? new List<Type>();
    }

    public static Type GetImplementType(string typeName, Type baseInterfaceType)
    {
        return GetAllTypes().FirstOrDefault(t =>
            t.Name == typeName &&
            baseInterfaceType.IsAssignableFrom(t) &&
            t.IsClass &&
            !t.IsAbstract &&
            !t.IsGenericType);
    }
}