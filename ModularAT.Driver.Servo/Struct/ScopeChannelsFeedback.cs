using System.Runtime.InteropServices;

namespace ModularAT.Driver.Servo;

[StructLayout(LayoutKind.Sequential, Pack = 8)]
public struct ScopeChannelsFeedback
{
    public int Channel0 { get; set; }
    public int Channel1 { get; set; }
    public int Channel2 { get; set; }
    public int Channel3 { get; set; }
    public int Channel4 { get; set; }
    public int Channel5 { get; set; }
    public long Time { get; set; }
}