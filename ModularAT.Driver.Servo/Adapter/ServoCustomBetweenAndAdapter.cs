using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Adapter;

/// <summary>
///     开始、结束字符适配器
/// </summary>
internal class ServoCustomBetweenAndAdapter : CustomBetweenAndDataHandlingAdapter<ServoSerialPortResponse>
{
    public ServoCustomBetweenAndAdapter()
    {
        MinSize = 5; //表示，实际数据体不会小于5，长度2、命令码1、CRC2、
    }

    public override byte[] StartCode => [0x55, 0xAA, 0xC8, 0xC9];

    public override byte[] EndCode => [0xA5, 0x5A];
    public override bool CanSendRequestInfo => true;
    public override bool CanSplicingSend => true;

    protected override ServoSerialPortResponse GetInstance()
    {
        return new ServoSerialPortResponse(Reset);
    }

    public void ManualReset()
    {
        Reset();
    }
}