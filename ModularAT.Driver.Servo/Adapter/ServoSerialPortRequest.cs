using System;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Adapter;

internal sealed class ServoSerialPortRequest : IRequestInfoBuilder, IRequestInfo, IServoSerialPortRequest
{
    /// <summary>
    ///     实体序列化包
    /// </summary>
    public ServoPackageBase Package { get; set; }


    int IRequestInfoBuilder.MaxLength => 1024;


    /// <summary>
    ///     生成指令
    /// </summary>
    /// <param name="byteBlock">指令</param>
    void IRequestInfoBuilder.Build(ByteBlock byteBlock)
    {
        //调用顺序不能修改
        byteBlock.Write(Head0);
        byteBlock.Write(Head1);
        byteBlock.Write(DstAddr);
        byteBlock.Write(SrcAddr);
        Package.Package(byteBlock);
        var dataSize = Package.GetDataSize();
        Data = new byte[dataSize];
        Array.Copy(byteBlock.Buffer, 6, Data, 0, dataSize); //从第6个字节开始复制到Data数组
        CRC = SerialPortUtility.CalcCrc(Data);
        byteBlock.Write(CRC);
        byteBlock.Write(Tail0);
        byteBlock.Write(Tail1);
    }

    #region Code

    public byte Head0 => 0x55;

    public byte Head1 => 0xAA;

    public byte DstAddr => 0xC9; //目标地址

    public byte SrcAddr => 0xC8; //源地址，PC

    public ushort DataSize { get; set; }

    public byte[] Data { get; private set; }

    public ushort CRC { get; set; }

    public byte Tail0 => 0xA5;

    public byte Tail1 => 0x5A;

    #endregion
}