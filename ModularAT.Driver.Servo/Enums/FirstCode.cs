namespace ModularAT.Driver.Servo;

public enum FirstCode : byte
{
    /// <summary>
    ///     固件升级请求
    /// </summary>
    FW_UPDATE_REQ = 0x01,

    /// <summary>
    ///     升级请求应答
    /// </summary>
    FW_UPDATE_ACK = 0x02,

    /// <summary>
    ///     固件升级数据
    /// </summary>
    FW_UPDATE_DATA = 0x03,

    /// <summary>
    ///     固件升级错误
    /// </summary>
    FW_UPDATE_ERROR = 0x04,

    /// <summary>
    ///     固件升级成功
    /// </summary>
    FW_UPDATE_OK = 0x05,

    /// <summary>
    ///     固件版本请求
    /// </summary>
    FW_UPDATE_VERREQ = 0x06,

    /// <summary>
    ///     固件版本应答
    /// </summary>
    FW_UPDATE_VERREPLY = 0x07,

    /// <summary>
    ///     固件参数设置
    /// </summary>
    FW_HANDLE_PARA_INFO_SET = 0x08,

    /// <summary>
    ///     固件参数获取
    /// </summary>
    FW_HANDLE_PARA_INFO_GET = 0x09,

    /// <summary>
    ///     固件参数应答
    /// </summary>
    FW_HANDLE_PARA_INFO_REPLY = 0x0A,

    /// <summary>
    ///     设备数据信息上报
    /// </summary>
    FW_DATA_RESP_CMD = 0x0B,

    /// <summary>
    ///     设备状态信息上报
    /// </summary>
    FW_STS_RESP_CMD = 0x0C,

    /// <summary>
    ///     日志指令，用于上传日志信息
    /// </summary>
    FW_LOG_CMD = 0x0D,

    /// <summary>
    ///     设备电压、电流数据上报
    /// </summary>
    FW_DATA_REPORT_CMD = 0x0E,

    /// <summary>
    ///     驱动模式设置
    /// </summary>
    FW_DRIVER_MODE_SET = 0x11,

    /// <summary>
    ///     CDM驱动器目标位置参数设置
    /// </summary>
    CDM_TARGET_POS_SET_CMD = 0x13,

    /// <summary>
    /// CDM驱动器动子检测位置参数设置
    /// </summary>
    CDM_MOVER_CHECK_POS_SET_CMD = 0x14,

    /// <summary>
    /// CDM驱动器使能电机位置参数设置
    /// </summary>
    CDM_FOC_ENS_POS_SET_CMD = 0x15,
    
    /// <summary>
    /// 获取过程调试监控数据
    /// </summary>
    GET_PROCESS_DBG_RECORD_CMD = 0x16,
    
    /// <summary>
    /// 获取PLC控制信号监控数据
    /// </summary>
    GET_PLC_CTRL_SIGNAL_CMD = 0x17,

    /// <summary>
    /// 获取PLC控制信号计数
    /// </summary>
    GET_PLC_CTRL_SIGNAL_CNT = 0x18,

    /// <summary>
    ///     透传升级命令，外设升级(预留)
    /// </summary>
    SPECIAL_UPDATE_RSP = 0xAD
}