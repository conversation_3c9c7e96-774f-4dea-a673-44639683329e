namespace ModularAT.Driver.Servo;

public enum SecondFirmwareParaGetCode : byte
{
    /// <summary>
    ///     获取设备型号(后跟最长20个字节长度的字符串)
    /// </summary>
    Model_GET = 0x01,

    /// <summary>
    ///     获取产品序列号(后跟最长20个字节长度的字符串)
    /// </summary>
    SerialNum_SET = 0x02,

    /// <summary>
    ///     获取硬件版本(后跟最长20个字节长度的字符串)
    /// </summary>
    HardVersion_SET = 0x03,

    /// <summary>
    ///     获取软件版本(后跟最长20个字节长度的字符串)
    /// </summary>
    SoftVersion_SET = 0x04
}