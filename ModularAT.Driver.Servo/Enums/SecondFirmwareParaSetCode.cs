namespace ModularAT.Driver.Servo;

public enum SecondFirmwareParaSetCode : byte
{
    /// <summary>
    ///     轴参数设置
    /// </summary>
    Axis_SET = 0x01,

    /// <summary>
    ///     控制参数设置
    /// </summary>
    ControlPara_SET = 0x02,

    /// <summary>
    ///     电磁参数设置
    /// </summary>
    Electro_SET = 0x03,

    /// <summary>
    ///     限制参数设置
    /// </summary>
    Limit_SET = 0x04,

    /// <summary>
    ///     数据清除
    /// </summary>
    DataClear = 0x05
}