namespace ModularAT.Driver.Servo;

/// <summary>
///     示波器通道设置代码
///     轴0位置反馈：0x0001
///     轴1位置反馈：0x0101
///     轴0速度指令：0x0002
///     轴0速度反馈：0x0003
///     轴1速度指令：0x0102
///     轴1速度反馈：0x0103
///     轴0电流指令：0x0004
///     轴0电流反馈：0x0005
///     轴1电流指令：0x0104
///     轴1电流反馈：0x0105
///     轴0 D轴参考电压：0x0006
///     轴1 D轴参考电压：0x0106
///     轴0 Q轴参考电压：0x0007
///     轴1 Q轴参考电压：0x0107
/// </summary>
public enum ScopeChannelsSetCode : ushort
{
    Axis0PositionFeedback = 0x0001,
    Axis1PositionFeedback = 0x0101,
    Axis0SpeedCommand = 0x0002,
    Axis0SpeedFeedback = 0x0003,
    Axis1SpeedCommand = 0x0102,
    Axis1SpeedFeedback = 0x0103,
    Axis0CurrentCommand = 0x0004,
    Axis0CurrentFeedback = 0x0005,
    Axis1CurrentCommand = 0x0104,
    Axis1CurrentFeedback = 0x0105,
    Axis0DReferenceVoltage = 0x0006,
    Axis1DReferenceVoltage = 0x0106,
    Axis0QReferenceVoltage = 0x0007,
    Axis1QReferenceVoltage = 0x0107
}