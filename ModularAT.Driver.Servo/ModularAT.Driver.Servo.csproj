<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net48</TargetFramework>
        <OutputType>Library</OutputType>
        <LangVersion>latest</LangVersion>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    </PropertyGroup>
    <PropertyGroup>
        <StartupObject/>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper">
            <Version>10.1.1</Version>
        </PackageReference>
        <PackageReference Include="log4net">
            <Version>3.1.0</Version>
        </PackageReference>
        <PackageReference Include="MiniExcel" Version="1.41.3" />
        <PackageReference Include="TouchSocket">
            <Version>2.0.18</Version>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ModularAT.Entity\ModularAT.Entity.csproj"/>
        <ProjectReference Include="..\TouchSocket.SerialPorts\TouchSocket.SerialPorts.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="Adapter\ServoPeriodAdapter.cs"/>
        <Compile Remove="Adapter\ServoTerminatorAdapter.cs"/>
        <Compile Remove="Enums\SecondDeviceDataAxisCode.cs"/>
        <Compile Remove="Enums\SecondDeviceReportAxisCode.cs"/>
        <Compile Remove="Packages\ElectricParaPackage.cs"/>
    </ItemGroup>
    <ItemGroup>
        <None Remove="App.config"/>
    </ItemGroup>
</Project>