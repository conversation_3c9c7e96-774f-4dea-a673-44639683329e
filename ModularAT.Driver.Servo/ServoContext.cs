using ModularAT.Localization.Resources;
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using ModularAT.Common;
using ModularAT.Common.Extensions;
using ModularAT.Common.Log;
using ModularAT.Entity;
using ModularAT.Entity.Enum;

namespace ModularAT.Driver.Servo;

/// <summary>
///     伺服数据上下文
/// </summary>
public class ServoContext
{
    private static ServoContext instance;

    public Action<int, byte, double[]> ErrDataChannelDraw;
    public Action<int, byte, double[]> ProcessRecordChannelDraw;
    public Action<int, byte, byte[]> PlcCtrlSignalRecordChannelDraw;
    public List<BindingList<ParameterModel>> ParameterFromFile;

    /// <summary>
    ///     伺服参数（表名-对象）映射
    ///     DriverParamer.xlsx内表名必须以枚举对应的数字开头，如1-电机参数
    /// </summary>
    public readonly Dictionary<ParamTableEnum, object> ParamsDic = new()
    {
        { ParamTableEnum.Motor, new MotorParamModel() },
        { ParamTableEnum.System, new SystemParamModel() },
        { ParamTableEnum.Encoder, new EncoderParamModel() },
        { ParamTableEnum.Protect, new ProtectParamModel() },
        { ParamTableEnum.Error, new ErrorRecordParamModel() },
        { ParamTableEnum.Control, new ControlStateParamModel() },
        { ParamTableEnum.Position, new PositionParamModel() },
        { ParamTableEnum.Speed, new SpeedParamModel() },
        { ParamTableEnum.Torque, new TorqueParamModel() }
    };

    public Action<ScopeChannelsFeedback> ScopeReceived;

    public ServoContext()
    {
        // DriveContext=>UI列表
        MotorParam.PropertyChanged += GetFromDriveContext;
        SystemParam.PropertyChanged += GetFromDriveContext;
        EncoderParam.PropertyChanged += GetFromDriveContext;
        ControlStateParam.PropertyChanged += GetFromDriveContext;
        ErrorRecordParam.PropertyChanged += GetFromDriveContext;
        ProtectParam.PropertyChanged += GetFromDriveContext;
        SpeedParam.PropertyChanged += GetFromDriveContext;
        PositionParam.PropertyChanged += GetFromDriveContext;
        TorqueParam.PropertyChanged += GetFromDriveContext;

        // UI列表=>DriveContext
        ParamItems.ListChanged += ParamItems_ListChanged;
    }

    public MotorParamModel MotorParam { get; internal set; } = new();
    public SystemParamModel SystemParam { get; internal set; } = new();
    public EncoderParamModel EncoderParam { get; internal set; } = new();
    public ProtectParamModel ProtectParam { get; internal set; } = new();
    public ErrorRecordParamModel ErrorRecordParam { get; internal set; } = new();
    public ControlStateParamModel ControlStateParam { get; internal set; } = new();
    public PositionParamModel PositionParam { get; internal set; } = new();
    public SpeedParamModel SpeedParam { get; internal set; } = new();
    public TorqueParamModel TorqueParam { get; internal set; } = new();

    /// <summary>
    ///     示波器数据源
    /// </summary>
    public ScopeChannelsFeedback ScopeFeedback { get; internal set; }

    public readonly LinkedList<ScopeChannelsFeedback> ScopeFeedbacks = [];
    public int AxisSelect { get; set; } = 1;
    public BindingList<ParameterModel> ParamItems { get; set; } = [];
    public static ServoContext Instance => instance ??= new ServoContext();

    /// <summary>
    ///     设置值到DriveContext内的写入映射表
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void ParamItems_ListChanged(object sender, ListChangedEventArgs e)
    {
        if (e.ListChangedType == ListChangedType.ItemChanged && e.PropertyDescriptor.Name == "Int64Value")
        {
            var item = ParamItems[e.NewIndex];
            //DriverParamer.xlsx内表名必须以枚举对应的数字开头，如1-电机参数
            var obj = ParamsDic[(ParamTableEnum)item.Group[0].ObjToInt()];
            switch (item.DataType)
            {
                case DataType.UINT16:
                    ObjectUtil<ushort>.SetPropertyValue(obj, item.Name, Convert.ToUInt16(item.Int64Value));
                    break;
                case DataType.INT16:
                    ObjectUtil<short>.SetPropertyValue(obj, item.Name, Convert.ToInt16(item.Int64Value));
                    break;
                case DataType.UINT32:
                    ObjectUtil<uint>.SetPropertyValue(obj, item.Name, Convert.ToUInt32(item.Int64Value));
                    break;
                case DataType.INT32:
                    ObjectUtil<int>.SetPropertyValue(obj, item.Name, Convert.ToInt32(item.Int64Value));
                    break;
            }
        }
    }

    /// <summary>
    ///     把DriveContext内数据对象的值赋给ParamModel
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    public void GetFromDriveContext(object sender, PropertyChangedEventArgs e)
    {
        try
        {
            var paramModel = ParamItems.FirstOrDefault(x => x.Name == e.PropertyName);
            paramModel.ReadValue = ObjectUtil<long>.GetPropertyValue(sender, e.PropertyName);
            if (paramModel.Int64Value == null || paramModel.IsRefresh)
            {
                // paramModel.Int64Value = paramModel.ReadValue;
                paramModel.EditString=paramModel.ReadValue.ToString();
                paramModel.IsRefresh = false;
            }
        }
        catch (Exception ex)
        {
            LogHelper.GetInstance<ServoContext>().Error(Lang.ServoContext_Get_from_drive_context_exception, ex);
        }
    }
/// <summary>
/// 触发刷新设置值
/// </summary>
    public void RefreshData()
    {
        foreach (var item in ParamItems)
        {
            item.IsRefresh = true;
        }
    }
}