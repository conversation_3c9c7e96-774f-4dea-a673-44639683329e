using ModularAT.Localization.Resources;
﻿using System;
using System.Collections.ObjectModel;

namespace ModularAT.Driver.Servo;

public class ScopeConst
{
    /// <summary>
    ///     示波器设置参数列表
    ///     分组，参数名，索引值
    /// </summary>
    public static ObservableCollection<Tuple<string, string, ushort>> ChannelParams { get; } =
    [
        new(<PERSON>.<PERSON>t_Position_parameter, Lang.ScopeConst_Axis0_position_feedback, 0x0001),
        new(<PERSON><PERSON>ope<PERSON>onst_Position_parameter, Lang.ScopeConst_Axis1_position_feedback, 0x0101),
        new(<PERSON>.ScopeConst_Speed_parameter, Lang.ScopeConst_Axis0_speed_instruction, 0x0002),
        new(<PERSON><PERSON>t_Speed_parameter, Lang.ScopeConst_Axis0_speed_feedback, 0x0003),
        new(<PERSON>.<PERSON>ope<PERSON>onst_Speed_parameter, Lang.ScopeConst_Axis1_speed_instruction, 0x0102),
        new(<PERSON><PERSON>ope<PERSON>t_Speed_parameter, Lang.ScopeConst_Axis1_speed_feedback, 0x0103),
        new(<PERSON><PERSON>ScopeConst_Current_parameter, <PERSON><PERSON>t_Axis0_current_instruction, 0x0004),
        new(<PERSON><PERSON>t_Current_parameter, <PERSON>.ScopeConst_Axis0_current_feedback, 0x0005),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis1_current_instruction, 0x0104),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis1_current_feedback, 0x0105),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis0_d_axis_voltage, 0x0006),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis1_d_axis_voltage, 0x0106),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis0_q_axis_voltage, 0x0007),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis1_q_axis_voltage, 0x0107),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis0_bus_voltage, 0x0008),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis1_bus_voltage, 0x0108),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis0_u_phase_current, 0x0009),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis1_u_phase_current, 0x0109),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis0_v_phase_current, 0x000A),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis1_v_phase_current, 0x010A),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis0_w_phase_current, 0x000B),
        new(Lang.ScopeConst_Current_parameter, Lang.ScopeConst_Axis1_w_phase_current, 0x010B),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis0_control_voltage, 0x000C),
        new(Lang.ScopeConst_Voltage_parameter, Lang.ScopeConst_Axis1_control_voltage, 0x010C)
    ];
}