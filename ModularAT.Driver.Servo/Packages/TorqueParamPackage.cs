using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class TorqueParamPackage : ServoPackageBase, ITorqueParam
{
    /// <summary>
    ///     Q轴电流目标值(A)
    /// </summary>
    public short Iq_CMD { get; set; }

    /// <summary>
    ///     D轴电流目标值(A)
    /// </summary>
    public short Id_CMD { get; set; }

    /// <summary>
    ///     Q轴电流反馈值(A)
    /// </summary>
    public short Iq_FB { get; set; }

    /// <summary>
    ///     D轴电流反馈值(A)
    /// </summary>
    public short Id_FB { get; set; }

    /// <summary>
    ///     电流环比例系数
    /// </summary>
    public uint Current_Kp { get; set; }

    /// <summary>
    ///     电流环积分系数
    /// </summary>
    public uint Current_Ki { get; set; }

    /// <summary>
    ///     电流环微分系数
    /// </summary>
    public uint Current_Kd { get; set; }

    /// <summary>
    ///     D轴反电势补偿系数
    /// </summary>
    public uint Current_Ke_D { get; set; }

    /// <summary>
    ///     Q轴反电势补偿系数
    /// </summary>
    public uint Current_Ke_Q { get; set; }

    /// <summary>
    ///     永磁体反电势补偿系数
    /// </summary>
    public uint Current_Kf { get; set; }

    /// <summary>
    ///     电流反馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Cur_FB_CutFreq { get; set; }

    /// <summary>
    ///     电流指令低通滤波截止频率(Hz)
    /// </summary>
    public uint CILP_CutFreq { get; set; }

    /// <summary>
    ///     电流前馈增益系数
    /// </summary>
    public uint Cur_FF_Gain { get; set; }

    /// <summary>
    ///     电流前馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Cur_FFLPF_CutFreq { get; set; }

    /// <summary>
    ///     电流指令陷波滤波中心频率(Hz)
    /// </summary>
    public uint CINF_NotchFreq { get; set; }

    /// <summary>
    ///     电流指令陷波滤波带宽(Hz)
    /// </summary>
    public uint CINF_CutFreq { get; set; }

    /// <summary>
    ///     电流指令陷波滤波深度(dB)
    /// </summary>
    public uint CINF_Depth { get; set; }
    
    /// <summary>
    /// U相电流采样调整因子
    /// </summary>
    public uint Cur_SA_Factor_U { get; set; }
    
    /// <summary>
    /// V相电流采样调整因子
    /// </summary>
    public uint Cur_SA_Factor_V { get; set; }

    public override ushort GetDataSize()
    {
        return 67;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Torque);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(Iq_CMD);
        byteBlock.Write(Id_CMD);
        byteBlock.Write(Current_Kp);
        byteBlock.Write(Current_Ki);
        byteBlock.Write(Current_Kd);
        byteBlock.Write(Current_Ke_D);
        byteBlock.Write(Current_Ke_Q);
        byteBlock.Write(Current_Kf);
        byteBlock.Write(Cur_FB_CutFreq);
        byteBlock.Write(CILP_CutFreq);
        byteBlock.Write(Cur_FF_Gain);
        byteBlock.Write(Cur_FFLPF_CutFreq);
        byteBlock.Write(CINF_NotchFreq);
        byteBlock.Write(CINF_CutFreq);
        byteBlock.Write(CINF_Depth);
        byteBlock.Write(Cur_SA_Factor_U);
        byteBlock.Write(Cur_SA_Factor_V);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        Iq_CMD = byteBlock.ReadInt16();
        Id_CMD = byteBlock.ReadInt16();
        Iq_FB = byteBlock.ReadInt16();
        Id_FB = byteBlock.ReadInt16();
        Current_Kp = byteBlock.ReadUInt32();
        Current_Ki = byteBlock.ReadUInt32();
        Current_Kd = byteBlock.ReadUInt32();
        Current_Ke_D = byteBlock.ReadUInt32();
        Current_Ke_Q = byteBlock.ReadUInt32();
        Current_Kf = byteBlock.ReadUInt32();
        Cur_FB_CutFreq = byteBlock.ReadUInt32();
        CILP_CutFreq = byteBlock.ReadUInt32();
        Cur_FF_Gain = byteBlock.ReadUInt32();
        Cur_FFLPF_CutFreq = byteBlock.ReadUInt32();
        CINF_NotchFreq = byteBlock.ReadUInt32();
        CINF_CutFreq = byteBlock.ReadUInt32();
        CINF_Depth = byteBlock.ReadUInt32();
        Cur_SA_Factor_U = byteBlock.ReadUInt32();
        Cur_SA_Factor_V = byteBlock.ReadUInt32();
    }
}