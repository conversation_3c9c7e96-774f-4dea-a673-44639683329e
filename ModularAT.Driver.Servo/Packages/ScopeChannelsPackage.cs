using System;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

internal class ScopeChannelsPackage : ServoPackageBase
{
    //public int Channel1 { get; set; }
    //public int Channel2 { get; set; }
    //public int Channel3 { get; set; }
    //public int Channel4 { get; set; }
    //public int Channel5 { get; set; }
    //public int Channel6 { get; set; }

    public ScopeChannelsFeedback Channels { get; set; }

    public override ushort GetDataSize()
    {
        //return Marshal.SizeOf(typeof(ScopeChannelsFeedback));
        return 24;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        byteBlock.Seek(2); //跳过0B 0A功能码

        Channels = new ScopeChannelsFeedback
        {
            Channel0 = byteBlock.ReadInt32(),
            Channel1 = byteBlock.ReadInt32(),
            Channel2 = byteBlock.ReadInt32(),
            Channel3 = byteBlock.ReadInt32(),
            Channel4 = byteBlock.ReadInt32(),
            Channel5 = byteBlock.ReadInt32()
        };


        //使用下列方式即可高效完成读取
        //if (byteBlock.TryReadBytesPackageInfo(out int pos, out int len))
        //{
        //    var str = Encoding.UTF8.GetString(byteBlock.Buffer, pos, len);

        //    int size = Marshal.SizeOf(typeof(ScopeChannelsFeedback));
        //    Channels = (ScopeChannelsFeedback)Marshal.PtrToStructure(Marshal.AllocHGlobal(size), typeof(ScopeChannelsFeedback));

        //    //从byteArray复制到分配的内存
        //    //Marshal.Copy(byteArray, 0, myStructPtr, size);

        //    //释放分配的内存
        //    //Marshal.FreeHGlobal((IntPtr)myStructPtr);
        //}

        //var firstCode = (FirstCode)byteBlock.ReadByte();
        //var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();

        //Channel1 = byteBlock.ReadInt32();
        //Channel2 = byteBlock.ReadInt32();
        //Channel3 = byteBlock.ReadInt32();
        //Channel4 = byteBlock.ReadInt32();
        //Channel5 = byteBlock.ReadInt32();
        //Channel6 = byteBlock.ReadInt32();
    }
}