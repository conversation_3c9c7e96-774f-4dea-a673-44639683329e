using System;
using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class ErrorRecordParamPackage : ServoPackageBase, IErrorRecordParam
{
    public short New_ErrIndex { get; set; }
    public short Pre_ErrIndex { get; set; }
    public ushort His_Err_Code0 { get; set; }
    public ushort His_Err_Code1 { get; set; }
    public ushort His_Err_Code2 { get; set; }
    public ushort His_Err_Code3 { get; set; }
    public ushort His_Err_Code4 { get; set; }
    public ushort His_Err_Code5 { get; set; }
    public ushort His_Err_Code6 { get; set; }
    public ushort His_Err_Code7 { get; set; }
    public ushort His_Err_Code8 { get; set; }
    public ushort His_Err_Code9 { get; set; }
    public ushort His_Err_Code10 { get; set; }
    public ushort His_Err_Code11 { get; set; }
    public ushort His_Err_Code12 { get; set; }
    public ushort His_Err_Code13 { get; set; }
    public ushort His_Err_Code14 { get; set; }
    public ushort His_Err_Code15 { get; set; }
    public ushort His_Err_Code16 { get; set; }
    public ushort His_Err_Code17 { get; set; }
    public ushort His_Err_Code18 { get; set; }
    public ushort His_Err_Code19 { get; set; }

    public override ushort GetDataSize()
    {
        throw new NotImplementedException();
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        New_ErrIndex = byteBlock.ReadInt16();
        Pre_ErrIndex = byteBlock.ReadInt16();
        His_Err_Code0 = byteBlock.ReadUInt16();
        His_Err_Code1 = byteBlock.ReadUInt16();
        His_Err_Code2 = byteBlock.ReadUInt16();
        His_Err_Code3 = byteBlock.ReadUInt16();
        His_Err_Code4 = byteBlock.ReadUInt16();
        His_Err_Code5 = byteBlock.ReadUInt16();
        His_Err_Code6 = byteBlock.ReadUInt16();
        His_Err_Code7 = byteBlock.ReadUInt16();
        His_Err_Code8 = byteBlock.ReadUInt16();
        His_Err_Code9 = byteBlock.ReadUInt16();
        His_Err_Code10 = byteBlock.ReadUInt16();
        His_Err_Code11 = byteBlock.ReadUInt16();
        His_Err_Code12 = byteBlock.ReadUInt16();
        His_Err_Code13 = byteBlock.ReadUInt16();
        His_Err_Code14 = byteBlock.ReadUInt16();
        His_Err_Code15 = byteBlock.ReadUInt16();
        His_Err_Code16 = byteBlock.ReadUInt16();
        His_Err_Code17 = byteBlock.ReadUInt16();
        His_Err_Code18 = byteBlock.ReadUInt16();
        His_Err_Code19 = byteBlock.ReadUInt16();
    }
}