using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class ControlStateParamPackage : ServoPackageBase, IControlStateParam
{
    /// <summary>
    ///     控制字
    /// </summary>
    public ushort ControlWord { get; set; }

    /// <summary>
    ///     状态字
    /// </summary>
    public ushort StatusWord { get; set; }

    /// <summary>
    ///     运行状态
    /// </summary>
    public ushort ModeOfOperation { get; set; }

    /// <summary>
    ///     实际状态
    /// </summary>
    public ushort ModesOfOperationDisplay { get; set; }

    public override ushort GetDataSize()
    {
        return 7;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Control);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(ControlWord);
        byteBlock.Write(ModeOfOperation);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        ControlWord = byteBlock.ReadUInt16();
        StatusWord = byteBlock.ReadUInt16();
        ModeOfOperation = byteBlock.ReadUInt16();
        ModesOfOperationDisplay = byteBlock.ReadUInt16();
    }
}