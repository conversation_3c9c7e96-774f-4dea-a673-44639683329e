using System;
using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

[Obsolete]
public class ScopeParamPackage : ServoPackageBase, IScopeParam
{
    /// <summary>
    ///     速度指令值(mm/s)
    /// </summary>
    public int Velocity_Cmd { get; set; }

    /// <summary>
    ///     Q轴电流指令值(A)
    /// </summary>
    public int Iq_Cmd { get; set; }

    /// <summary>
    ///     Q轴电流反馈值(A)
    /// </summary>
    public int Iq_FB { get; set; }

    /// <summary>
    ///     实际速度(mm/s)
    /// </summary>
    public int Actual_Velocity { get; set; }

    /// <summary>
    ///     实际位置(PosUnit)
    /// </summary>
    public int Actual_Position { get; set; }

    /// <summary>
    ///     母线电压(V)
    /// </summary>
    public short Bus_Voltage { get; set; }

    public override ushort GetDataSize()
    {
        throw new NotImplementedException();
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        Iq_FB = byteBlock.ReadInt32();
        Actual_Velocity = byteBlock.ReadInt32();
        Actual_Position = byteBlock.ReadInt32();
        Bus_Voltage = byteBlock.ReadInt16();
        Velocity_Cmd = byteBlock.ReadInt32();
        Iq_Cmd = byteBlock.ReadInt32();
    }
}