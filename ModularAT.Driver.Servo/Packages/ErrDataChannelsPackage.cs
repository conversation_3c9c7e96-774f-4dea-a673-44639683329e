using System;
using ModularAT.Driver.Servo.Enums;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

public class ErrDataChannelsPackage : ServoPackageBase
{
    public int ErrAxis { get; set; }
    public ThirdErrDataType ErrDataType { get; set; }
    public int EndIndex { get; set; }

    public double[] Datas { get; set; } = new double[500];

    public override ushort GetDataSize()
    {
        throw new NotImplementedException();
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        byteBlock.Seek(2); //跳过0B 0B功能码
        ErrAxis = byteBlock.ReadByte();
        ErrDataType = (ThirdErrDataType)byteBlock.ReadByte();
        EndIndex = byteBlock.ReadUInt16();
        //游标跳转到分割处，最终索引*Int32字节数+Data之外的数据所占字节数,Data之外指的是功能码、ErrAxis、ErrDataType、EndIndex     
        byteBlock.Seek(EndIndex * 4 + 6);
        // 将数据分为两段，前半段为EndIndex之前的数据，后半段为EndIndex之后的数据
        if (byteBlock.Length > 4006)
        {
            Datas = new double[2000];//龙浩的数据长度为2000
        }
        else
        {
            Datas = new double[1000];//陆工的数据长度为1000
        }

        var splitIndex = Datas.Length - EndIndex;
        // 前半段，读的是EndIndex之后的数据
        for (var i = 0; i < splitIndex; i++) Datas[i] = byteBlock.ReadInt32();
        //跳过Data之外的数据
        byteBlock.Seek(6);
        //后半段，读的是EndIndex之前的数据
        for (var i = splitIndex; i < Datas.Length; i++) Datas[i] = byteBlock.ReadInt32();
    }
}