using System;
using ModularAT.Driver.Servo.Enums;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

public class ProcessRecordChannelsPackage : ServoPackageBase
{
    public int RecordAxis { get; set; }
    public ThirdProcessRecordDataType RecordDataType { get; private set; }
    public int EndIndex { get; set; }

    public double[] Datas { get; private set; }

    public override ushort GetDataSize()
    {
        throw new NotImplementedException();
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        byteBlock.Seek(2); //跳过0B 0C功能码
        RecordAxis = byteBlock.ReadByte();
        RecordDataType = (ThirdProcessRecordDataType)byteBlock.ReadByte();
        EndIndex = byteBlock.ReadUInt16();
        //游标跳转到分割处，最终索引*Int32字节数+Data之外的数据所占字节数,Data之外指的是功能码、ErrAxis、ErrDataType、EndIndex     
        byteBlock.Seek(EndIndex * 4 + 6);
        // 将数据分为两段，前半段为EndIndex之前的数据，后半段为EndIndex之后的数据
        if (byteBlock.Length >= 246)
        {
            Datas = new double[60]; //60个int32
        }

        var splitIndex = Datas.Length - EndIndex;
        // 前半段，读的是EndIndex之后的数据
        for (var i = 0; i < splitIndex; i++) Datas[i] = byteBlock.ReadInt32();
        //跳过Data之外的数据
        byteBlock.Seek(6);
        //后半段，读的是EndIndex之前的数据
        for (var i = splitIndex; i < Datas.Length; i++) Datas[i] = byteBlock.ReadInt32();
    }
}