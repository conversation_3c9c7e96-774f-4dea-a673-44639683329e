using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo
{
    public class FirmwareHandlePackage : ServoPackageBase
    {
        private byte[] data;
        //private ushort dataSize;
        //public byte[] Data => data;
        //public ushort DataSize => (ushort)data.Length;
        public FirstCode FirstFirmwareHandleCode { get; set; }
        public byte SecondCode { get; set; }
        public string Msg { get; set; }

        public override ushort GetDataSize()
        {
            return (ushort)data.Length;
        }

        public override void Package(in ByteBlock byteBlock)
        {
            if (FirstFirmwareHandleCode == FirstCode.FW_HANDLE_PARA_INFO_SET)
            {
                switch (SecondCode)
                {
                    case (byte)SecondFirmwareParaSetCode.Axis_SET:
                        data = new byte[] { (byte)FirstFirmwareHandleCode, SecondCode };
                        break;
                    case (byte)SecondFirmwareParaSetCode.ControlPara_SET:
                        break;
                    case (byte)SecondFirmwareParaSetCode.Electro_SET:
                        break;
                    case (byte)SecondFirmwareParaSetCode.Limit_SET:
                        break;
                    case (byte)SecondFirmwareParaSetCode.DataClear:
                        break;
                    default:
                        break;
                }

            }

            byteBlock.Write(GetDataSize());//封包的时候需要先写入数据长度
            byteBlock.Write(data);
        }

        public override void Unpackage(in ByteBlock byteBlock)
        {
            this.FirstFirmwareHandleCode = (FirstCode)byteBlock.ReadByte();
            this.SecondCode = (byte)byteBlock.ReadByte();
        }
    }
}
