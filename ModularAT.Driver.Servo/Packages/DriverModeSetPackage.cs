using System;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

public class DriverModeSetPackage : ServoPackageBase
{
    /// <summary>
    ///     模式
    /// </summary>
    public byte Mode { get; set; }

    /// <summary>
    ///     控制权
    /// </summary>
    public byte ControlBy { get; set; }

    /// <summary>
    ///     子模式
    /// </summary>
    //本地控制模式在正常操作模式下：
    //0x00：无控制（停止）
    //0x01：双轴位置控制
    //0x02：轴0电角度辨识
    //本地控制模式在工厂测试模式下：
    //0x00：无控制（停止）
    //0x01：直流采样测试
    //0x02：交流采样测试
    public byte SubMode { get; set; }

    public override ushort GetDataSize()
    {
        //return Marshal.SizeOf(typeof(ScopeChannelsFeedback));
        return 4;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_DRIVER_MODE_SET);
        byteBlock.Write(ControlBy);
        byteBlock.Write(Mode);
        byteBlock.Write(SubMode);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }
}