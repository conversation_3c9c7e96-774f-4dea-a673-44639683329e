using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class SpeedParamPackage : ServoPackageBase, ISpeedParam
{
    /// <summary>
    ///     目标速度(mm/s)
    /// </summary>
    public int Target_Velocity { get; set; }

    /// <summary>
    ///     实际速度(mm/s)
    /// </summary>
    public int Actual_Velocity { get; set; }

    /// <summary>
    ///     速度环比例系数(A/(mm/s))
    /// </summary>
    public uint Velocity_Kp { get; set; }

    /// <summary>
    ///     速度环积分系数(A/mm)
    /// </summary>
    public uint Velocity_Ki { get; set; }

    /// <summary>
    ///     速度环微分系数(A/(mm/s^2))
    /// </summary>
    public uint Velocity_Kd { get; set; }

    /// <summary>
    ///     速度环抗积分饱和系数
    /// </summary>
    public uint Velocity_Kc { get; set; }

    /// <summary>
    ///     速度前馈增益系数
    /// </summary>
    public uint Vel_FF_Gain { get; set; }

    /// <summary>
    ///     速度前馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Vel_FFLPF_CutFreq { get; set; }

    /// <summary>
    ///     速度反馈低通滤波截止频率(Hz)
    /// </summary>
    public uint Vel_FBLPF_CutFreq { get; set; }

    /// <summary>
    ///     速度指令低通滤波截止频率(Hz)
    /// </summary>
    public uint VILP_Cutoff_Freq { get; set; }

    /// <summary>
    ///     速度控制输出钳位UP(A)
    /// </summary>
    public int VelCtrl_ClamUp { get; set; }

    /// <summary>
    ///     速度控制输出钳位LOW(A)
    /// </summary>
    public int VelCtrl_ClamLow { get; set; }

    public override ushort GetDataSize()
    {
        return 47;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Speed);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(Target_Velocity);
        byteBlock.Write(Velocity_Kp);
        byteBlock.Write(Velocity_Ki);
        byteBlock.Write(Velocity_Kd);
        byteBlock.Write(Velocity_Kc);
        byteBlock.Write(Vel_FF_Gain);
        byteBlock.Write(Vel_FFLPF_CutFreq);
        byteBlock.Write(Vel_FBLPF_CutFreq);
        byteBlock.Write(VILP_Cutoff_Freq);
        byteBlock.Write(VelCtrl_ClamUp);
        byteBlock.Write(VelCtrl_ClamLow);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        Target_Velocity = byteBlock.ReadInt32();
        Actual_Velocity = byteBlock.ReadInt32();
        Velocity_Kp = byteBlock.ReadUInt32();
        Velocity_Ki = byteBlock.ReadUInt32();
        Velocity_Kd = byteBlock.ReadUInt32();
        Velocity_Kc = byteBlock.ReadUInt32();
        Vel_FF_Gain = byteBlock.ReadUInt32();
        Vel_FFLPF_CutFreq = byteBlock.ReadUInt32();
        Vel_FBLPF_CutFreq = byteBlock.ReadUInt32();
        VILP_Cutoff_Freq = byteBlock.ReadUInt32();
        VelCtrl_ClamUp = byteBlock.ReadInt32();
        VelCtrl_ClamLow = byteBlock.ReadInt32();
    }
}