using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

/// <summary>
///     保护参数表
///     命名必须和DriveParamer.xlsx内的保持一致
/// </summary>
public class ProtectParamPackage : ServoPackageBase, IProtectParam
{
    public uint Short_OLD_Threshold { get; set; }
    public uint Short_OLD_Time { get; set; }
    public uint Sus_OLD_RateCur { get; set; }
    public uint Sus_OLD_PeakCur { get; set; }
    public uint Dur_Of_PeakCur { get; set; }
    public uint Heat_Coeff { get; set; }
    public uint Cool_Coeff { get; set; }
    public uint Locked_rotor_Current { get; set; }
    public uint Locked_rotor_Time { get; set; }
    public uint Locked_rotor_Vel { get; set; }
    public ushort MOS_Temp { get; set; }
    public ushort Encoder_Commu_Err { get; set; }
    public uint Stall_Dect { get; set; }
    public uint Over_Voltage { get; set; }
    public uint Under_Voltage { get; set; }
    public uint Thermal_Time_Const { get; set; }
    public uint Ins_OCD_Threshold { get; set; }
    
    public uint Servo_Thermal_Time { get; set; }
    
    public uint Servo_Heat_Coeff { get; set; }
    
    public uint Servo_Cool_Coeff { get; set; }
    


    public override ushort GetDataSize()
    {
        return 79;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Protect);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(Short_OLD_Threshold);
        byteBlock.Write(Short_OLD_Time);
        byteBlock.Write(Sus_OLD_RateCur);
        byteBlock.Write(Sus_OLD_PeakCur);
        byteBlock.Write(Dur_Of_PeakCur);
        byteBlock.Write(Heat_Coeff);
        byteBlock.Write(Cool_Coeff);
        byteBlock.Write(Locked_rotor_Current);
        byteBlock.Write(Locked_rotor_Time);
        byteBlock.Write(Locked_rotor_Vel);
        byteBlock.Write(MOS_Temp);
        byteBlock.Write(Encoder_Commu_Err);
        byteBlock.Write(Stall_Dect);
        byteBlock.Write(Over_Voltage);
        byteBlock.Write(Under_Voltage);
        byteBlock.Write(Thermal_Time_Const);
        byteBlock.Write(Ins_OCD_Threshold);
        byteBlock.Write(Servo_Thermal_Time);
        byteBlock.Write(Servo_Heat_Coeff);
        byteBlock.Write(Servo_Cool_Coeff);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        Short_OLD_Threshold = byteBlock.ReadUInt32();
        Short_OLD_Time = byteBlock.ReadUInt32();
        Sus_OLD_RateCur = byteBlock.ReadUInt32();
        Sus_OLD_PeakCur = byteBlock.ReadUInt32();
        Dur_Of_PeakCur = byteBlock.ReadUInt32();
        Heat_Coeff = byteBlock.ReadUInt32();
        Cool_Coeff = byteBlock.ReadUInt32();
        Locked_rotor_Current = byteBlock.ReadUInt32();
        Locked_rotor_Time = byteBlock.ReadUInt32();
        Locked_rotor_Vel = byteBlock.ReadUInt32();
        MOS_Temp = byteBlock.ReadUInt16();
        Encoder_Commu_Err = byteBlock.ReadUInt16();
        Stall_Dect = byteBlock.ReadUInt32();
        Over_Voltage = byteBlock.ReadUInt32();
        Under_Voltage = byteBlock.ReadUInt32();
        Thermal_Time_Const = byteBlock.ReadUInt32();
        Ins_OCD_Threshold = byteBlock.ReadUInt32();
        Servo_Thermal_Time = byteBlock.ReadUInt32();
        Servo_Heat_Coeff = byteBlock.ReadUInt32();
        Servo_Cool_Coeff = byteBlock.ReadUInt32();
    }
}