using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class SystemParamPackage : ServoPackageBase, ISystemParam
{
    /// <summary>
    /// 驱动器类型
    /// </summary>
    public ushort DRIVER_VERSION_0 { get; set; }

    /// <summary>
    ///     驱动版本-大版本迭代
    /// </summary>
    public ushort DRIVER_VERSION_1 { get; set; }

    /// <summary>
    ///     驱动版本-功能迭代
    /// </summary>
    public ushort DRIVER_VERSION_2 { get; set; }

    /// <summary>
    ///     驱动版本-Bug迭代
    /// </summary>
    public ushort DRIVER_VERSION_3 { get; set; }

    /// <summary>
    ///     驱动版本-调试发布(0-调试 1-发布)
    /// </summary>
    public ushort DRIVER_VERSION_4 { get; set; }

    /// <summary>
    ///     示波器控制
    /// </summary>
    public ushort ScopeCtl { get; set; }

    /// <summary>
    ///     示波器通道1
    /// </summary>
    public ushort ScopeMapList0 { get; set; }

    public ushort ScopeMapList1 { get; set; }
    public ushort ScopeMapList2 { get; set; }
    public ushort ScopeMapList3 { get; set; }
    public ushort ScopeMapList4 { get; set; }
    public ushort ScopeMapList5 { get; set; }
    public ushort ScopeMapList6 { get; set; }
    public ushort ScopeMapList7 { get; set; }

    public override ushort GetDataSize()
    {
        return 23;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.System);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(DRIVER_VERSION_0);
        byteBlock.Write(ScopeCtl);
        byteBlock.Write(ScopeMapList0);
        byteBlock.Write(ScopeMapList1);
        byteBlock.Write(ScopeMapList2);
        byteBlock.Write(ScopeMapList3);
        byteBlock.Write(ScopeMapList4);
        byteBlock.Write(ScopeMapList5);
        byteBlock.Write(ScopeMapList6);
        byteBlock.Write(ScopeMapList7);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        DRIVER_VERSION_0 = byteBlock.ReadUInt16();
        DRIVER_VERSION_1 = byteBlock.ReadUInt16();
        DRIVER_VERSION_2 = byteBlock.ReadUInt16();
        DRIVER_VERSION_3 = byteBlock.ReadUInt16();
        DRIVER_VERSION_4 = byteBlock.ReadUInt16();
        ScopeCtl = byteBlock.ReadUInt16();
        ScopeMapList0 = byteBlock.ReadUInt16();
        ScopeMapList1 = byteBlock.ReadUInt16();
        ScopeMapList2 = byteBlock.ReadUInt16();
        ScopeMapList3 = byteBlock.ReadUInt16();
        ScopeMapList4 = byteBlock.ReadUInt16();
        ScopeMapList5 = byteBlock.ReadUInt16();
        ScopeMapList6 = byteBlock.ReadUInt16();
        ScopeMapList7 = byteBlock.ReadUInt16();
    }
}