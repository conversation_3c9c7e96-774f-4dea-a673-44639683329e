using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class MotorParamPackage : ServoPackageBase, IMotorParam
{
    public ushort LL_Resistance { get; set; }
    public ushort LL_Inductance { get; set; }
    public ushort Rate_Current { get; set; }
    public ushort Rate_Torque { get; set; }
    public ushort Peak_Current { get; set; }
    public ushort Torque_Constant { get; set; }
    public ushort Back_Emf_Coeff { get; set; }
    public ushort Electrode_Distance { get; set; }
    public ushort Number_Of_Poles { get; set; }
    public uint Elec_Offset { get; set; }
    public short U_Current { get; set; }
    public short V_Current { get; set; }
    public short W_Current { get; set; }
    public short Bus_Voltage { get; set; }
    // public uint Pos_Fit_Type { get; set; }
    // public uint Ceg_1 { get; set; }
    // public uint Ceg_2 { get; set; }
    // public uint Enc0_ThL { get; set; }
    // public uint Enc2_ThH { get; set; }
    // public uint Cth_1 { get; set; }
    // public uint Cth_2 { get; set; }

    public override ushort GetDataSize()
    {
        return 33;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Motor);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(LL_Resistance);
        byteBlock.Write(LL_Inductance);
        byteBlock.Write(Rate_Current);
        byteBlock.Write(Rate_Torque);
        byteBlock.Write(Peak_Current);
        byteBlock.Write(Torque_Constant);
        byteBlock.Write(Back_Emf_Coeff);
        byteBlock.Write(Electrode_Distance);
        byteBlock.Write(Number_Of_Poles);
        byteBlock.Write(Elec_Offset);
        byteBlock.Write(U_Current);
        byteBlock.Write(V_Current);
        byteBlock.Write(W_Current);
        byteBlock.Write(Bus_Voltage);
        // byteBlock.Write(Pos_Fit_Type);
        // byteBlock.Write(Ceg_1);
        // byteBlock.Write(Ceg_2);
        // byteBlock.Write(Enc0_ThL);
        // byteBlock.Write(Enc2_ThH);
        // byteBlock.Write(Cth_1);
        // byteBlock.Write(Cth_2);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        if (byteBlock.Length != 33) return;

        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        LL_Resistance = byteBlock.ReadUInt16();
        LL_Inductance = byteBlock.ReadUInt16();
        Rate_Current = byteBlock.ReadUInt16();
        Rate_Torque = byteBlock.ReadUInt16();
        Peak_Current = byteBlock.ReadUInt16();
        Torque_Constant = byteBlock.ReadUInt16();
        Back_Emf_Coeff = byteBlock.ReadUInt16();
        Electrode_Distance = byteBlock.ReadUInt16();
        Number_Of_Poles = byteBlock.ReadUInt16();
        Elec_Offset = byteBlock.ReadUInt32();
        U_Current = byteBlock.ReadInt16();
        V_Current = byteBlock.ReadInt16();
        W_Current = byteBlock.ReadInt16();
        Bus_Voltage = byteBlock.ReadInt16();
        // Pos_Fit_Type = byteBlock.ReadUInt32();
        // Ceg_1 = byteBlock.ReadUInt32();
        // Ceg_2 = byteBlock.ReadUInt32();
        // Enc0_ThL = byteBlock.ReadUInt32();
        // Enc2_ThH = byteBlock.ReadUInt32();
        // Cth_1 = byteBlock.ReadUInt32();
        // Cth_2 = byteBlock.ReadUInt32();
    }
}