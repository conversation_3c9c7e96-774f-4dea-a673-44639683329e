using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class EncoderParamPackage : ServoPackageBase, IEncodeParam
{
    /// <summary>
    ///     编码器类型
    /// </summary>
    public ushort EncoderType { get; set; }

    /// <summary>
    ///     编码器分辨率
    /// </summary>
    public uint EncoderResolution { get; set; }

    /// <summary>
    ///     编码器版本-大版本迭代
    /// </summary>
    public ushort EncVersion_Master { get; set; }

    /// <summary>
    ///     编码器版本-功能迭代
    /// </summary>
    public ushort EncVersion_Func { get; set; }

    /// <summary>
    ///     编码器版本-Bug迭代
    /// </summary>
    public ushort EncVersion_Bug { get; set; }

    /// <summary>
    ///     编码器版本-调试版本
    /// </summary>
    public ushort EncVersion_Debug { get; set; }

    /// <summary>
    ///     编码器调试功能选择
    /// </summary>
    public ushort EncDebugFunc { get; set; }

    /// <summary>
    ///     编码器0位置调试接口
    /// </summary>
    public uint EncoderPos0 { get; set; }

    /// <summary>
    ///     编码器1位置调试接口
    /// </summary>
    public uint EncoderPos1 { get; set; }

    public override ushort GetDataSize()
    {
        return 5;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Encoder);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(EncDebugFunc);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        EncoderType = byteBlock.ReadUInt16();
        EncoderResolution = byteBlock.ReadUInt32();
        EncVersion_Master = byteBlock.ReadUInt16();
        EncVersion_Func = byteBlock.ReadUInt16();
        EncVersion_Bug = byteBlock.ReadUInt16();
        EncVersion_Debug = byteBlock.ReadUInt16();
        EncDebugFunc = byteBlock.ReadUInt16();
        EncoderPos0 = byteBlock.ReadUInt32();
        EncoderPos1 = byteBlock.ReadUInt32();
    }
}