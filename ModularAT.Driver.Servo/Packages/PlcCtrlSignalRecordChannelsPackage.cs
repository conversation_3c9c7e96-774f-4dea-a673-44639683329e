using System;
using System.Collections.Generic;
using ModularAT.Driver.Servo.Enums;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

public class  PlcCtrlSignalRecordChannelsPackage : ServoPackageBase
{
    public int RecordAxis { get; set; }
    public byte[] BrakeRecordDatas { get; private set; }
    
    public byte[] AccelerationRecordDatas { get; private set; }
    public int EndIndex { get; set; }
    
    public override ushort GetDataSize()
    {
        throw new NotImplementedException();
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        byteBlock.Seek(2); //跳过0B 0D功能码
        RecordAxis = byteBlock.ReadByte();
        _=byteBlock.ReadByte();//类型占位
        EndIndex = byteBlock.ReadUInt16();
        //游标跳转到分割处，最终索引*Int32字节数+Data之外的数据所占字节数,Data之外指的是功能码、ErrAxis、ErrDataType、EndIndex     
        byteBlock.Seek(EndIndex * 4 + 6);
        // 将数据分为两段，前半段为EndIndex之前的数据，后半段为EndIndex之后的数据
        var datas=new Byte[120];
        
        var splitIndex = datas.Length - EndIndex;
        // 前半段，读的是EndIndex之后的数据
        for (var i = 0; i < splitIndex; i++) datas[i] = (byte)byteBlock.ReadByte();
        //跳过Data之外的数据
        byteBlock.Seek(6);
        //后半段，读的是EndIndex之前的数据
        for (var i = splitIndex; i < datas.Length; i++) datas[i] = (byte)byteBlock.ReadByte();
        
        List<byte> highNibbles = new List<byte>();
        List<byte> lowNibbles = new List<byte>();

        foreach (byte b in datas)
        {
            // 取高4位：右移4位
            byte high = (byte)(b >> 4);
            // 取低4位：与0x0F进行按位与
            byte low = (byte)(b & 0x0F);

            highNibbles.Add(high);
            lowNibbles.Add(low);
        }
        BrakeRecordDatas = highNibbles.ToArray();
        AccelerationRecordDatas = lowNibbles.ToArray();
        
    }
}