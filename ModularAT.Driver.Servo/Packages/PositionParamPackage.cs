using ModularAT.Entity;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo;

public class PositionParamPackage : ServoPackageBase, IPositionParam
{
    /// <summary>
    ///     目标位置(PosUnit)
    /// </summary>
    public uint Target_Position { get; set; }

    /// <summary>
    ///     实际位置(PosUnit)
    /// </summary>
    public uint Actual_Position { get; set; }

    /// <summary>
    ///     位置环比例系数((mm/s)/PosUnit)
    /// </summary>
    public uint Position_Kp { get; set; }

    /// <summary>
    ///     位置环积分系数
    /// </summary>
    public uint Position_Ki { get; set; }

    /// <summary>
    ///     位置环微分系数
    /// </summary>
    public uint Position_Kd { get; set; }

    /// <summary>
    ///     位置指令低通滤波截止频率(Hz)
    /// </summary>
    public uint PILF_Cutoff_Freq { get; set; }

    /// <summary>
    ///     位置控制输出钳位UP(mm/s)
    /// </summary>
    public int PosCtrl_ClamUp { get; set; }

    /// <summary>
    ///     位置控制输出钳位LOW(mm/s)
    /// </summary>
    public int PosCtrl_ClamLow { get; set; }

    /// <summary>
    ///     位置指令均值滤波器截止频率(Hz)
    /// </summary>
    public uint PISA_Cutoff { get; set; }

    public override ushort GetDataSize()
    {
        return 35;
    }

    public override void Package(in ByteBlock byteBlock)
    {
        byteBlock.Write(GetDataSize());
        byteBlock.Write((byte)FirstCode.FW_HANDLE_PARA_INFO_SET);
        byteBlock.Write((byte)SecondDeviceDataSetCode.Position);
        byteBlock.Write((byte)AxisSelect);
        byteBlock.Write(Target_Position);
        byteBlock.Write(Position_Kp);
        byteBlock.Write(Position_Ki);
        byteBlock.Write(Position_Kd);
        byteBlock.Write(PILF_Cutoff_Freq);
        byteBlock.Write(PosCtrl_ClamUp);
        byteBlock.Write(PosCtrl_ClamLow);
        byteBlock.Write(PISA_Cutoff);
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        var firstCode = (FirstCode)byteBlock.ReadByte();
        AxisSelect = byteBlock.ReadByte();
        var secondCode = (SecondDeviceDataRespCode)byteBlock.ReadByte();
        Target_Position = byteBlock.ReadUInt32();
        Actual_Position = byteBlock.ReadUInt32();
        Position_Kp = byteBlock.ReadUInt32();
        Position_Ki = byteBlock.ReadUInt32();
        Position_Kd = byteBlock.ReadUInt32();
        PILF_Cutoff_Freq = byteBlock.ReadUInt32();
        PosCtrl_ClamUp = byteBlock.ReadInt32();
        PosCtrl_ClamLow = byteBlock.ReadInt32();
        PISA_Cutoff = byteBlock.ReadUInt32();
    }
}