using System;
using System.Collections.Generic;
using ModularAT.Driver.Servo.Enums;
using TouchSocket.Core;

namespace ModularAT.Driver.Servo.Packages;

public class PlcCtrlSignalCountPackage : ServoPackageBase
{
    public int RecordAxis { get; set; }

    /// <summary>
    /// 信号类型
    /// </summary>
    public ThirdPlcCtrlSignalRecordDataType SignalType { get; private set; }

    /// <summary>
    /// 刹车信号计数
    /// </summary>
    public uint StopSignalCount { get; set; }

    /// <summary>
    /// 加速信号计数
    /// </summary>
    public uint AccSignalCount { get; set; }

    public override ushort GetDataSize()
    {
        throw new NotImplementedException();
    }

    public override void Package(in ByteBlock byteBlock)
    {
        throw new NotImplementedException();
    }

    public override void Unpackage(in ByteBlock byteBlock)
    {
        byteBlock.Seek(2); //跳过0B 0E功能码
        RecordAxis = byteBlock.ReadByte();
        SignalType = (ThirdPlcCtrlSignalRecordDataType)byteBlock.ReadByte();
        StopSignalCount = byteBlock.ReadUInt32();
        AccSignalCount = byteBlock.ReadUInt32();
    }
}