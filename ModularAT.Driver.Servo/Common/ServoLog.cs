using System;
using log4net;
using log4net.Config;
using TouchSocket.Core;
using ILog = TouchSocket.Core.ILog;

[assembly: XmlConfigurator(ConfigFile = "log4net.config", Watch = true)]

namespace ModularAT.Driver.Servo;

internal class ServoLog : ILog
{
    private readonly log4net.ILog m_logger;

    public ServoLog()
    {
        m_logger = LogManager.GetLogger("Servo");
    }

    public LogLevel LogLevel { get; set; }

    public void Log(LogLevel logLevel, object source, string message, Exception exception)
    {
        //此处就是实际的日志输出
        switch (logLevel)
        {
            case LogLevel.Trace:
                m_logger.Debug(message, exception);
                break;

            case LogLevel.Debug:
                m_logger.Debug(message, exception);
                break;

            case LogLevel.Info:
                m_logger.Info(message, exception);
                break;

            case LogLevel.Warning:
                m_logger.Warn(message, exception);
                break;

            case LogLevel.Error:
                m_logger.Error(message, exception);
                break;

            case LogLevel.Critical:
                m_logger.Error(message, exception);
                break;

            case LogLevel.None:
            default:
                break;
        }
    }
}