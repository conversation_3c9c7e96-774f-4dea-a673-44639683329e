using System;
using System.Collections.Generic;
using AutoMapper;
using ModularAT.Entity;

namespace ModularAT.Driver.Servo;

public class ServoPackageMapper
{
    private static readonly Lazy<Dictionary<SecondDeviceDataRespCode, IMapper>> _mapperDic = new(CreateMapper);

    public static Dictionary<SecondDeviceDataRespCode, IMapper> MapperDic => _mapperDic.Value;

    private static Dictionary<SecondDeviceDataRespCode, IMapper> CreateMapper()
    {
        var mapper = new Dictionary<SecondDeviceDataRespCode, IMapper>();

        mapper.Add(SecondDeviceDataRespCode.Motor,
            new MapperConfiguration(cfg => cfg.CreateMap<MotorParamPackage, MotorParamModel>()).CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.System,
            new MapperConfiguration(cfg => cfg.CreateMap<SystemParamPackage, SystemParamModel>()).CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Encoder,
            new MapperConfiguration(cfg => cfg.CreateMap<EncoderParamPackage, EncoderParamModel>()).CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Protect,
            new MapperConfiguration(cfg => cfg.CreateMap<ProtectParamPackage, ProtectParamModel>()).CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Error,
            new MapperConfiguration(cfg => cfg.CreateMap<ErrorRecordParamPackage, ErrorRecordParamModel>())
                .CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Control,
            new MapperConfiguration(cfg => cfg.CreateMap<ControlStateParamPackage, ControlStateParamModel>())
                .CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Position,
            new MapperConfiguration(cfg => cfg.CreateMap<PositionParamPackage, PositionParamModel>()).CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Speed,
            new MapperConfiguration(cfg => cfg.CreateMap<SpeedParamPackage, SpeedParamModel>()).CreateMapper());
        mapper.Add(SecondDeviceDataRespCode.Torque,
            new MapperConfiguration(cfg => cfg.CreateMap<TorqueParamPackage, TorqueParamModel>()).CreateMapper());
        //mapper.Add(SecondDeviceDataRespCode.Scope, new MapperConfiguration(cfg => cfg.CreateMap<ScopeParamPackage, ScopeChannelsFeedback>()).CreateMapper());
        return mapper;
    }
}