//------------------------------------------------------------------------------
//  此代码版权（除特别声明或在XREF结尾的命名空间的代码）归作者本人若汝棋茗所有
//  源代码使用协议遵循本仓库的开源协议及附加协议，若本仓库没有设置，则按MIT开源协议授权
//  CSDN博客：https://blog.csdn.net/qq_40374647
//  哔哩哔哩视频：https://space.bilibili.com/94253567
//  Gitee源代码仓库：https://gitee.com/RRQM_Home
//  Github源代码仓库：https://github.com/RRQM
//  API首页：https://touchsocket.net/
//  交流QQ群：234762506
//  感谢您的下载和使用
//------------------------------------------------------------------------------

using System;
using System.IO.Ports;
using System.Threading;
using System.Threading.Tasks;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace TouchSocket.SerialPorts;

/// <summary>
///     Serial核心
/// </summary>
public class SerialCore : DisposableObject, ISender
{
    private const string m_msg1 = "远程终端已关闭";

    /// <summary>
    ///     Serial核心
    /// </summary>
    public SerialCore()
    {
        m_receiveCounter = new ValueCounter
        {
            Period = TimeSpan.FromSeconds(1),
            OnPeriod = OnReceivePeriod
        };

        m_sendCounter = new ValueCounter
        {
            Period = TimeSpan.FromSeconds(1),
            OnPeriod = OnSendPeriod
        };
    }

    /// <summary>
    ///     SerialPort
    /// </summary>
    public SerialPort MainSerialPort { get; private set; }

    /// <summary>
    ///     最大缓存尺寸
    /// </summary>
    public int MaxBufferSize { get; set; } = 1024 * 1024 * 10;

    /// <summary>
    ///     最小缓存尺寸
    /// </summary>
    public int MinBufferSize { get; set; } = 1024 * 10;

    /// <summary>
    ///     当中断Serial的时候。当为<see langword="true" />时，意味着是调用<see cref="Close(string)" />。当为<see langword="false" />时，则是其他中断。
    /// </summary>
    public Action<SerialCore, bool, string> OnBreakOut { get; set; }

    /// <summary>
    ///     当发生异常的时候
    /// </summary>
    public Action<SerialCore, Exception> OnException { get; set; }

    /// <summary>
    ///     在线状态
    /// </summary>
    public bool Online => m_online && MainSerialPort != null && MainSerialPort.IsOpen;

    /// <summary>
    ///     当收到数据的时候
    /// </summary>
    public Action<SerialCore, ByteBlock> OnReceived { get; set; }

    /// <summary>
    ///     接收缓存池,运行时的值会根据流速自动调整
    /// </summary>
    public int ReceiveBufferSize { get; private set; } = 1024 * 10;

    /// <summary>
    ///     接收计数器
    /// </summary>
    public ValueCounter ReceiveCounter => m_receiveCounter;

    /// <summary>
    ///     发送缓存池,运行时的值会根据流速自动调整
    /// </summary>
    public int SendBufferSize { get; private set; } = 1024 * 10;

    /// <summary>
    ///     发送计数器
    /// </summary>
    public ValueCounter SendCounter => m_sendCounter;

    /// <inheritdoc />
    public bool CanSend => Online;

    /// <summary>
    ///     发送数据。
    ///     <para>
    ///         内部会根据是否启用Ssl，进行直接发送，还是Ssl发送。
    ///     </para>
    /// </summary>
    /// <param name="buffer"></param>
    /// <param name="offset"></param>
    /// <param name="length"></param>
    public virtual void Send(byte[] buffer, int offset, int length)
    {
        ThrowIfNotConnected();
        try
        {
            m_semaphoreForSend.Wait();
            MainSerialPort.Write(buffer, offset, length);
            m_sendCounter.Increment(length);
        }
        finally
        {
            m_semaphoreForSend.Release();
        }
    }

    /// <summary>
    ///     异步发送数据。
    /// </summary>
    /// <param name="buffer"></param>
    /// <param name="offset"></param>
    /// <param name="length"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public virtual async Task SendAsync(byte[] buffer, int offset, int length)
    {
        ThrowIfNotConnected();
        try
        {
            await m_semaphoreForSend.WaitAsync();

            MainSerialPort.Write(buffer, offset, length);
            m_sendCounter.Increment(length);
        }
        finally
        {
            m_semaphoreForSend.Release();
        }
    }

    /// <summary>
    ///     析构函数
    /// </summary>
    ~SerialCore()
    {
        this.SafeDispose();
    }

    /// <summary>
    ///     开始接收。
    /// </summary>
    /// <returns></returns>
    public virtual async Task BeginReceive()
    {
        while (m_online)
        {
            var byteBlock = new ByteBlock(ReceiveBufferSize);
            try
            {
                if (MainSerialPort.BytesToRead == 0)
                    // await Task.Delay(0).ConfigureFalseAwait();
                    continue;

                //var r = await Task<int>.Factory.FromAsync(this.m_serialPort.BaseStream.BeginRead, this.m_serialPort.BaseStream.EndRead, byteBlock.Buffer, 0, byteBlock.Capacity, default).ConfigureFalseAwait();
                var r = await MainSerialPort.BaseStream.ReadAsync(byteBlock.Buffer, 0, byteBlock.Capacity, default)
                    .ConfigureFalseAwait();
                if (r == 0)
                {
                    PrivateBreakOut(false, m_msg1);
                    return;
                }

                byteBlock.SetLength(r);
                HandleBuffer(byteBlock);
            }
            catch (Exception ex)
            {
                byteBlock.Dispose();
                PrivateBreakOut(false, ex.Message);
            }
        }
    }

    /// <summary>
    ///     请求关闭
    /// </summary>
    /// <param name="msg"></param>
    public virtual void Close(string msg)
    {
        PrivateBreakOut(true, msg);
    }

    /// <summary>
    ///     重置环境，并设置新的<see cref="SerialPort" />。
    /// </summary>
    /// <param name="serialPort"></param>
    public virtual void Reset(SerialPort serialPort)
    {
        if (serialPort is null) throw new ArgumentNullException(nameof(serialPort));

        if (!serialPort.IsOpen) throw new Exception("新的SerialPort必须在连接状态。");

        Reset();
        MainSerialPort = serialPort;
        m_online = true;
    }

    /// <summary>
    ///     重置环境。
    /// </summary>
    public virtual void Reset()
    {
        m_online = false;
        m_receiveCounter.Reset();
        m_sendCounter.Reset();
        MainSerialPort = null;
        OnReceived = null;
        OnBreakOut = null;
        ReceiveBufferSize = MinBufferSize;
        SendBufferSize = MinBufferSize;
    }

    /// <summary>
    ///     当中断Serial时。
    /// </summary>
    /// <param name="manual">当为<see langword="true" />时，意味着是调用<see cref="Close(string)" />。当为<see langword="false" />时，则是其他中断。</param>
    /// <param name="msg"></param>
    protected virtual void BreakOut(bool manual, string msg)
    {
        OnBreakOut?.Invoke(this, manual, msg);
    }

    /// <summary>
    ///     当发生异常的时候
    /// </summary>
    /// <param name="ex"></param>
    protected virtual void Exception(Exception ex)
    {
        OnException?.Invoke(this, ex);
    }

    /// <summary>
    ///     当收到数据的时候
    /// </summary>
    /// <param name="byteBlock"></param>
    protected virtual void Received(ByteBlock byteBlock)
    {
        OnReceived?.Invoke(this, byteBlock);
    }

    /// <summary>
    ///     判断，当不在连接状态时触发异常。
    /// </summary>
    /// <exception cref="NotConnectedException"></exception>
    protected void ThrowIfNotConnected()
    {
        if (!m_online) throw new NotConnectedException();
    }

    private void HandleBuffer(ByteBlock byteBlock)
    {
        try
        {
            m_receiveCounter.Increment(byteBlock.Length);
            Received(byteBlock);
        }
        catch (Exception ex)
        {
            Exception(ex);
        }
        finally
        {
            byteBlock.Dispose();
        }
    }

    private void OnReceivePeriod(long value)
    {
        ReceiveBufferSize = Math.Max(TouchSocketUtility.HitBufferLength(value), MinBufferSize);
        if (MainSerialPort != null && !MainSerialPort.IsOpen) MainSerialPort.ReadBufferSize = ReceiveBufferSize;
    }

    private void OnSendPeriod(long value)
    {
        SendBufferSize = Math.Max(TouchSocketUtility.HitBufferLength(value), MinBufferSize);
    }

    private void PrivateBreakOut(bool manual, string msg)
    {
        lock (SyncRoot)
        {
            if (m_online)
            {
                m_online = false;
                BreakOut(manual, msg);
            }
        }
    }

    #region 字段

    /// <summary>
    ///     同步根
    /// </summary>
    public readonly object SyncRoot = new();

    private readonly SemaphoreSlim m_semaphoreForSend = new(1, 1);
    private bool m_online;
    private ValueCounter m_receiveCounter;
    private ValueCounter m_sendCounter;

    #endregion 字段
}

internal sealed class InternalSerialCore : SerialCore
{
}