//------------------------------------------------------------------------------
//  此代码版权（除特别声明或在XREF结尾的命名空间的代码）归作者本人若汝棋茗所有
//  源代码使用协议遵循本仓库的开源协议及附加协议，若本仓库没有设置，则按MIT开源协议授权
//  CSDN博客：https://blog.csdn.net/qq_40374647
//  哔哩哔哩视频：https://space.bilibili.com/94253567
//  Gitee源代码仓库：https://gitee.com/RRQM_Home
//  Github源代码仓库：https://github.com/RRQM
//  API首页：https://touchsocket.net/
//  交流QQ群：234762506
//  感谢您的下载和使用
//------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Threading;
using System.Threading.Tasks;
using TouchSocket.Core;
using TouchSocket.Resources;
using TouchSocket.Sockets;

namespace TouchSocket.SerialPorts;

/// <inheritdoc cref="SerialPortClientBase" />
public class SerialPortClient : SerialPortClientBase
{
    /// <summary>
    ///     接收到数据
    /// </summary>
    public ReceivedEventHandler<SerialPortClient> Received { get; set; }

    /// <inheritdoc />
    protected override async Task ReceivedData(ReceivedDataEventArgs e)
    {
        if (Received != null) await Received.Invoke(this, e);
        await base.ReceivedData(e);
    }
}

/// <summary>
///     串口客户端基类
/// </summary>
public class SerialPortClientBase : SetupConfigObject, ISerialPortClient
{
    /// <summary>
    ///     串口客户端基类
    /// </summary>
    public SerialPortClientBase()
    {
        Protocol = SerialPortUtility.SerialPort;
    }

    /// <inheritdoc />
    public virtual void SetDataHandlingAdapter(SingleStreamDataHandlingAdapter adapter)
    {
        if (!CanSetDataHandlingAdapter) throw new Exception($"不允许自由调用{nameof(SetDataHandlingAdapter)}进行赋值。");

        SetAdapter(adapter);
    }

    /// <summary>
    ///     BreakOut。
    /// </summary>
    /// <param name="manual"></param>
    /// <param name="msg"></param>
    protected void BreakOut(bool manual, string msg)
    {
        lock (GetSerialCore())
        {
            if (m_online)
            {
                m_online = false;
                MainSerialPort.SafeDispose();
                m_delaySender.SafeDispose();
                DataHandlingAdapter.SafeDispose();
                Task.Factory.StartNew(PrivateOnDisconnected, new DisconnectEventArgs(manual, msg));
            }
        }
    }

    /// <summary>
    ///     当收到适配器处理的数据时。
    /// </summary>
    /// <param name="e"></param>
    /// <returns>如果返回<see langword="true" />则表示数据已被处理，且不会再向下传递。</returns>
    protected virtual async Task ReceivedData(ReceivedDataEventArgs e)
    {
        if (e.Handled) return;
        await PluginManager.RaiseAsync(nameof(ISerialReceivedPlugin.OnSerialReceived), this, e).ConfigureFalseAwait();
    }

    /// <summary>
    ///     当收到原始数据
    /// </summary>
    /// <param name="byteBlock"></param>
    /// <returns>如果返回<see langword="true" />则表示数据已被处理，且不会再向下传递。</returns>
    protected virtual Task<bool> ReceivingData(ByteBlock byteBlock)
    {
        if (PluginManager.GetPluginCount(nameof(ISerialReceivingPlugin.OnSerialReceiving)) > 0)
            return PluginManager.RaiseAsync(nameof(ISerialReceivingPlugin.OnSerialReceiving), this,
                new ByteBlockEventArgs(byteBlock));
        return Task.FromResult(false);
    }

    /// <summary>
    ///     当即将发送时，如果覆盖父类方法，则不会触发插件。
    /// </summary>
    /// <param name="buffer">数据缓存区</param>
    /// <param name="offset">偏移</param>
    /// <param name="length">长度</param>
    /// <returns>返回值表示是否允许发送</returns>
    protected virtual async Task<bool> SendingData(byte[] buffer, int offset, int length)
    {
        if (PluginManager.GetPluginCount(nameof(ISerialSendingPlugin.OnSerialSending)) > 0)
        {
            var args = new SendingEventArgs(buffer, offset, length);
            await PluginManager.RaiseAsync(nameof(ISerialSendingPlugin.OnSerialSending), this, args)
                .ConfigureAwait(false);
            return args.IsPermitOperation;
        }

        return true;
    }

    /// <summary>
    ///     设置适配器，该方法不会检验<see cref="CanSetDataHandlingAdapter" />的值。
    /// </summary>
    /// <param name="adapter"></param>
    protected void SetAdapter(SingleStreamDataHandlingAdapter adapter)
    {
        ThrowIfDisposed();
        if (adapter is null) throw new ArgumentNullException(nameof(adapter));

        if (Config != null) adapter.Config(Config);

        adapter.Logger = Logger;
        adapter.OnLoaded(this);
        adapter.ReceivedCallBack = PrivateHandleReceivedData;
        adapter.SendCallBack = DefaultSend;
        adapter.SendAsyncCallBack = DefaultSendAsync;
        DataHandlingAdapter = adapter;
    }

    private static SerialPort CreateSerial(SerialPortOption serialPortOption)
    {
        var serialPort = new SerialPort(serialPortOption.PortName, serialPortOption.BaudRate, serialPortOption.Parity,
            serialPortOption.DataBits, serialPortOption.StopBits);
        return serialPort;
    }

    private SerialCore GetSerialCore()
    {
        ThrowIfDisposed();
        return m_serialCore ?? throw new ObjectDisposedException(GetType().Name);
    }

    private void HandleReceived(SerialCore core, ByteBlock byteBlock)
    {
        try
        {
            if (DisposedValue) return;
            if (ReceivingData(byteBlock).GetFalseAwaitResult()) return;

            if (DataHandlingAdapter == null)
            {
                Logger?.Error(this, TouchSocketResource.NullDataAdapter.GetDescription());
                return;
            }

            DataHandlingAdapter.ReceivedInput(byteBlock);
        }
        catch (Exception ex)
        {
            Logger?.Log(LogLevel.Error, this, "在处理数据时发生错误", ex);
        }
    }

    private void PrivateHandleReceivedData(ByteBlock byteBlock, IRequestInfo requestInfo)
    {
        if (m_receiver != null)
            if (m_receiver.TryInputReceive(byteBlock, requestInfo))
                return;

        ReceivedData(new ReceivedDataEventArgs(byteBlock, requestInfo)).GetFalseAwaitResult();
    }

    private void SerialCoreBreakOut(SerialCore core, bool manual, string msg)
    {
        BreakOut(manual, msg);
    }

    private void SetSerialPort(SerialPort serialPort)
    {
        if (serialPort == null) return;

        MainSerialPort = serialPort;
        var delaySenderOption = Config.GetValue(TouchSocketConfigExtension.DelaySenderProperty);
        if (delaySenderOption != null) m_delaySender = new DelaySender(delaySenderOption, GetSerialCore().Send);
        m_serialCore.Reset(serialPort);
        m_serialCore.OnReceived = HandleReceived;
        m_serialCore.OnBreakOut = SerialCoreBreakOut;
        if (Config.GetValue(TouchSocketConfigExtension.MinBufferSizeProperty) is int minValue)
            m_serialCore.MinBufferSize = minValue;

        if (Config.GetValue(TouchSocketConfigExtension.MaxBufferSizeProperty) is int maxValue)
            m_serialCore.MaxBufferSize = maxValue;
    }

    #region 变量

    private readonly SemaphoreSlim m_semaphore = new(1, 1);
    private readonly InternalSerialCore m_serialCore = new();
    private DelaySender m_delaySender;
    private bool m_online;

    #endregion 变量

    #region 事件

    /// <inheritdoc />
    public ConnectedEventHandler<ISerialPortClient> Connected { get; set; }

    /// <inheritdoc />
    public SerialConnectingEventHandler<ISerialPortClient> Connecting { get; set; }

    /// <inheritdoc />
    public DisconnectEventHandler<ISerialPortClient> Disconnected { get; set; }

    /// <inheritdoc />
    public DisconnectEventHandler<ISerialPortClient> Disconnecting { get; set; }

    /// <summary>
    ///     已经建立连接
    /// </summary>
    /// <param name="e"></param>
    protected virtual async Task OnConnected(ConnectedEventArgs e)
    {
        try
        {
            if (Connected != null)
            {
                await Connected.Invoke(this, e);
                if (e.Handled) return;
            }

            await PluginManager.RaiseAsync(nameof(ISerialConnectedPlugin.OnSerialConnected), this, e);
        }
        catch (Exception ex)
        {
            Logger?.Log(LogLevel.Error, this, $"在事件{nameof(Connected)}中发生错误。", ex);
        }
    }

    /// <summary>
    ///     准备连接的时候，此时并未建立连接
    /// </summary>
    /// <param name="e"></param>
    protected virtual async Task OnConnecting(SerialConnectingEventArgs e)
    {
        try
        {
            if (Connecting != null)
            {
                await Connecting.Invoke(this, e);
                if (e.Handled) return;
            }

            await PluginManager.RaiseAsync(nameof(ISerialConnectingPlugin.OnSerialConnecting), this, e);
        }
        catch (Exception ex)
        {
            Logger?.Log(LogLevel.Error, this, $"在事件{nameof(OnConnecting)}中发生错误。", ex);
        }
    }

    /// <summary>
    ///     断开连接。在客户端未设置连接状态时，不会触发
    /// </summary>
    /// <param name="e"></param>
    protected virtual async Task OnDisconnected(DisconnectEventArgs e)
    {
        try
        {
            if (Disconnected != null)
            {
                await Disconnected.Invoke(this, e).ConfigureAwait(false);
                if (e.Handled) return;
            }

            await PluginManager.RaiseAsync(nameof(ISerialDisconnectedPlugin.OnSerialDisconnected), this, e)
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            Logger?.Log(LogLevel.Error, this, $"在事件{nameof(Disconnected)}中发生错误。", ex);
        }
    }

    /// <summary>
    ///     即将断开连接(仅主动断开时有效)。
    /// </summary>
    /// <param name="e"></param>
    protected virtual async Task OnDisconnecting(DisconnectEventArgs e)
    {
        try
        {
            if (Disconnecting != null)
            {
                await Disconnecting.Invoke(this, e).ConfigureAwait(false);
                if (e.Handled) return;
            }

            await PluginManager.RaiseAsync(nameof(ISerialDisconnectingPlugin.OnSerialDisconnecting), this, e)
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            Logger?.Log(LogLevel.Error, this, $"在事件{nameof(Disconnecting)}中发生错误。", ex);
        }
    }

    private async Task PrivateOnConnected(ConnectedEventArgs o)
    {
        await OnConnected(o);
    }

    private async Task PrivateOnConnecting(SerialConnectingEventArgs e)
    {
        if (CanSetDataHandlingAdapter)
            SetDataHandlingAdapter(
                Config.GetValue(SerialPortConfigExtension.SerialDataHandlingAdapterProperty).Invoke());

        await OnConnecting(e);
    }

    private async Task PrivateOnDisconnected(object obj)
    {
        m_receiver?.TryInputReceive(default, default);
        await OnDisconnected((DisconnectEventArgs)obj);
    }

    private async Task PrivateOnDisconnecting(object obj)
    {
        await OnDisconnecting((DisconnectEventArgs)obj);
    }

    #endregion 事件

    #region 属性

    /// <inheritdoc />
    public bool CanSend => Online;

    /// <inheritdoc />
    public virtual bool CanSetDataHandlingAdapter => true;

    /// <inheritdoc />
    public SingleStreamDataHandlingAdapter DataHandlingAdapter { get; private set; }

    /// <inheritdoc />
    public DateTime LastReceivedTime => GetSerialCore().ReceiveCounter.LastIncrement;

    /// <inheritdoc />
    public DateTime LastSendTime => GetSerialCore().SendCounter.LastIncrement;

    /// <inheritdoc />
    public SerialPort MainSerialPort { get; private set; }

    /// <inheritdoc />
    public bool Online => m_online && m_serialCore != null && m_serialCore.Online;

    /// <inheritdoc />
    public Protocol Protocol { get; set; }

    #endregion 属性

    #region 断开操作

    /// <inheritdoc />
    public virtual void Close(string msg)
    {
        lock (GetSerialCore())
        {
            if (m_online)
            {
                Task.Factory.StartNew(PrivateOnDisconnecting, new DisconnectEventArgs(true, msg));
                MainSerialPort.TryClose();
                BreakOut(true, msg);
            }
        }
    }

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="disposing"></param>
    protected override void Dispose(bool disposing)
    {
        lock (GetSerialCore())
        {
            if (m_online)
            {
                Task.Factory.StartNew(PrivateOnDisconnecting, new DisconnectEventArgs(true, $"{nameof(Dispose)}主动断开"));
                BreakOut(true, $"{nameof(Dispose)}主动断开");
            }
        }

        base.Dispose(disposing);
    }

    #endregion 断开操作

    #region Connect

    /// <inheritdoc />
    public void Connect(int millisecondsTimeout, CancellationToken token)
    {
        Open();
    }

    /// <inheritdoc />
    public Task ConnectAsync(int millisecondsTimeout, CancellationToken token)
    {
        return Task.Run(Open);
    }

    /// <summary>
    ///     打开串口
    /// </summary>
    protected void Open()
    {
        try
        {
            ThrowIfDisposed();
            m_semaphore.Wait();
            if (m_online) return;
            if (Config == null) throw new ArgumentNullException(nameof(Config), "配置文件不能为空。");
            var serialPortOption = Config.GetValue(SerialPortConfigExtension.SerialPortOptionProperty) ??
                                   throw new ArgumentNullException("串口配置不能为空。");
            MainSerialPort.SafeDispose();
            var serialPort = CreateSerial(serialPortOption);
            PrivateOnConnecting(new SerialConnectingEventArgs(serialPort)).ConfigureAwait(false).GetAwaiter()
                .GetResult();

            serialPort.Open();

            m_online = true;

            SetSerialPort(serialPort);

            PrivateOnConnected(new ConnectedEventArgs()).ConfigureAwait(false).GetAwaiter().GetResult();
            BeginReceive();
        }
        finally
        {
            m_semaphore.Release();
        }
    }

    private void BeginReceive()
    {
        _ = GetSerialCore().BeginReceive();
    }

    #endregion Connect

    #region Receiver

    private Receiver m_receiver;

    /// <inheritdoc />
    public void ClearReceiver()
    {
        m_receiver = null;
    }

    /// <inheritdoc />
    public IReceiver CreateReceiver()
    {
        return m_receiver ??= new Receiver(this);
    }

    #endregion Receiver

    #region 发送

    #region 同步发送

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="requestInfo"></param>
    /// <exception cref="NotConnectedException"></exception>
    /// <exception cref="OverlengthException"></exception>
    /// <exception cref="Exception"></exception>
    public void Send(IRequestInfo requestInfo)
    {
        if (DisposedValue) return;
        if (DataHandlingAdapter == null)
            throw new ArgumentNullException(nameof(DataHandlingAdapter),
                TouchSocketResource.NullDataAdapter.GetDescription());
        if (!DataHandlingAdapter.CanSendRequestInfo) throw new NotSupportedException("当前适配器不支持对象发送。");
        DataHandlingAdapter.SendInput(requestInfo);
    }

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="buffer">
    ///     <inheritdoc />
    /// </param>
    /// <param name="offset">
    ///     <inheritdoc />
    /// </param>
    /// <param name="length">
    ///     <inheritdoc />
    /// </param>
    /// <exception cref="NotConnectedException">
    ///     <inheritdoc />
    /// </exception>
    /// <exception cref="OverlengthException">
    ///     <inheritdoc />
    /// </exception>
    /// <exception cref="Exception">
    ///     <inheritdoc />
    /// </exception>
    public virtual void Send(byte[] buffer, int offset, int length)
    {
        if (DataHandlingAdapter == null)
            throw new ArgumentNullException(nameof(DataHandlingAdapter),
                TouchSocketResource.NullDataAdapter.GetDescription());
        DataHandlingAdapter.SendInput(buffer, offset, length);
    }

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="transferBytes">
    ///     <inheritdoc />
    /// </param>
    /// <exception cref="NotConnectedException">
    ///     <inheritdoc />
    /// </exception>
    /// <exception cref="OverlengthException">
    ///     <inheritdoc />
    /// </exception>
    /// <exception cref="Exception">
    ///     <inheritdoc />
    /// </exception>
    public virtual void Send(IList<ArraySegment<byte>> transferBytes)
    {
        if (DataHandlingAdapter == null)
            throw new ArgumentNullException(nameof(DataHandlingAdapter),
                TouchSocketResource.NullDataAdapter.GetDescription());

        if (DataHandlingAdapter.CanSplicingSend)
        {
            DataHandlingAdapter.SendInput(transferBytes);
        }
        else
        {
            var length = 0;
            foreach (var item in transferBytes) length += item.Count;
            using (var byteBlock = new ByteBlock(length))
            {
                foreach (var item in transferBytes) byteBlock.Write(item.Array, item.Offset, item.Count);
                DataHandlingAdapter.SendInput(byteBlock.Buffer, 0, byteBlock.Len);
            }
        }
    }

    #endregion 同步发送

    #region 异步发送

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="buffer"></param>
    /// <param name="offset"></param>
    /// <param name="length"></param>
    /// <exception cref="NotConnectedException"></exception>
    /// <exception cref="OverlengthException"></exception>
    /// <exception cref="Exception"></exception>
    public virtual Task SendAsync(byte[] buffer, int offset, int length)
    {
        ThrowIfDisposed();
        if (DataHandlingAdapter == null)
            throw new ArgumentNullException(nameof(DataHandlingAdapter),
                TouchSocketResource.NullDataAdapter.GetDescription());
        return DataHandlingAdapter.SendInputAsync(buffer, offset, length);
    }

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="requestInfo"></param>
    /// <exception cref="NotConnectedException"></exception>
    /// <exception cref="OverlengthException"></exception>
    /// <exception cref="Exception"></exception>
    public virtual Task SendAsync(IRequestInfo requestInfo)
    {
        ThrowIfDisposed();
        if (DataHandlingAdapter == null)
            throw new ArgumentNullException(nameof(DataHandlingAdapter),
                TouchSocketResource.NullDataAdapter.GetDescription());
        if (!DataHandlingAdapter.CanSendRequestInfo) throw new NotSupportedException("当前适配器不支持对象发送。");
        return DataHandlingAdapter.SendInputAsync(requestInfo);
    }

    /// <summary>
    ///     <inheritdoc />
    /// </summary>
    /// <param name="transferBytes"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public virtual Task SendAsync(IList<ArraySegment<byte>> transferBytes)
    {
        ThrowIfDisposed();
        if (DataHandlingAdapter == null)
            throw new ArgumentNullException(nameof(DataHandlingAdapter),
                TouchSocketResource.NullDataAdapter.GetDescription());
        if (DataHandlingAdapter.CanSplicingSend) return DataHandlingAdapter.SendInputAsync(transferBytes);

        var length = 0;
        foreach (var item in transferBytes) length += item.Count;
        using (var byteBlock = new ByteBlock(length))
        {
            foreach (var item in transferBytes) byteBlock.Write(item.Array, item.Offset, item.Count);
            return DataHandlingAdapter.SendInputAsync(byteBlock.Buffer, 0, byteBlock.Len);
        }
    }

    #endregion 异步发送

    /// <inheritdoc />
    public void DefaultSend(byte[] buffer, int offset, int length)
    {
        if (SendingData(buffer, offset, length).GetFalseAwaitResult())
        {
            if (m_delaySender != null)
            {
                m_delaySender.Send(new QueueDataBytes(buffer, offset, length));
                return;
            }

            GetSerialCore().Send(buffer, offset, length);
        }
    }

    /// <inheritdoc />
    public async Task DefaultSendAsync(byte[] buffer, int offset, int length)
    {
        if (await SendingData(buffer, offset, length)) await GetSerialCore().SendAsync(buffer, offset, length);
    }

    #endregion 发送
}